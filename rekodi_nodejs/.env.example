# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/rekodi
MONGODB_TEST_URI=mongodb://localhost:27017/rekodi_test

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRE=7d
JWT_REFRESH_EXPIRE=30d

# Encryption Configuration
ENCRYPTION_KEY=your_32_character_encryption_key_here
ENCRYPTION_IV=your_16_character_iv_here

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo

# Appwrite Configuration (for migration)
APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
APPWRITE_PROJECT_ID=683c61460026c3e03330
APPWRITE_API_KEY=your_appwrite_api_key_here

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,https://your-frontend-domain.com

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=change_this_password_in_production
