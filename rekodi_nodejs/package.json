{"name": "rekodi-backend", "version": "1.0.0", "description": "Express.js backend for Rekodi financial management app", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": ["financial", "management", "express", "mongodb", "api"], "author": "Rekodi Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "multer": "^1.4.5-lts.1", "uuid": "^9.0.1", "moment": "^2.29.4", "crypto-js": "^4.2.0", "openai": "^4.20.1", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "prettier": "^3.1.0"}, "engines": {"node": ">=18.0.0"}}