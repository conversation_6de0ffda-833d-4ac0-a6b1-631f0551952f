const mongoose = require('mongoose');

const categorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Category name is required'],
    trim: true,
    maxlength: [100, 'Category name cannot exceed 100 characters']
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  type: {
    type: String,
    required: [true, 'Category type is required'],
    enum: ['income', 'expense', 'business_income', 'business_expense', 'investment', 'transfer']
  },
  parentCategory: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    default: null
  },
  icon: {
    type: String,
    default: 'category'
  },
  color: {
    type: String,
    default: '#6B7280',
    match: [/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Please enter a valid hex color']
  },
  description: {
    type: String,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  keywords: [String], // For AI categorization
  isSystem: {
    type: Boolean,
    default: false // System categories cannot be deleted
  },
  isActive: {
    type: Boolean,
    default: true
  },
  sortOrder: {
    type: Number,
    default: 0
  },
  
  // Business-specific fields
  businessTypes: [{
    type: String,
    enum: ['retail', 'wholesale', 'service', 'manufacturing', 'restaurant', 'consulting', 'ecommerce', 'other']
  }],
  
  // Tax and accounting information
  taxInfo: {
    isTaxDeductible: { type: Boolean, default: false },
    taxCategory: String,
    accountingCode: String
  },
  
  // Usage statistics
  usage: {
    totalTransactions: { type: Number, default: 0 },
    totalAmount: { type: Number, default: 0 },
    lastUsed: Date,
    popularityScore: { type: Number, default: 0 }
  },
  
  // Localization
  translations: [{
    language: {
      type: String,
      enum: ['en', 'sw', 'fr', 'es'],
      required: true
    },
    name: {
      type: String,
      required: true
    },
    description: String
  }],
  
  // AI and automation
  aiPatterns: [{
    pattern: String,
    confidence: { type: Number, min: 0, max: 1 },
    source: {
      type: String,
      enum: ['user_training', 'system_learning', 'manual']
    }
  }],
  
  metadata: {
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'SystemUser'
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'SystemUser'
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
categorySchema.index({ slug: 1 });
categorySchema.index({ type: 1, isActive: 1 });
categorySchema.index({ parentCategory: 1 });
categorySchema.index({ isSystem: 1 });
categorySchema.index({ sortOrder: 1 });
categorySchema.index({ 'usage.popularityScore': -1 });
categorySchema.index({ keywords: 1 });

// Virtual for subcategories
categorySchema.virtual('subcategories', {
  ref: 'Category',
  localField: '_id',
  foreignField: 'parentCategory'
});

// Virtual for full path
categorySchema.virtual('fullPath').get(function() {
  // This would need to be populated to work properly
  return this.parentCategory ? `${this.parentCategory.name} > ${this.name}` : this.name;
});

// Pre-save middleware to generate slug
categorySchema.pre('save', function(next) {
  if (this.isModified('name') && !this.slug) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '_')
      .replace(/^_+|_+$/g, '');
  }
  next();
});

// Method to get localized name
categorySchema.methods.getLocalizedName = function(language = 'en') {
  const translation = this.translations.find(t => t.language === language);
  return translation ? translation.name : this.name;
};

// Method to update usage statistics
categorySchema.methods.updateUsage = function(amount = 0) {
  this.usage.totalTransactions += 1;
  this.usage.totalAmount += Math.abs(amount);
  this.usage.lastUsed = new Date();
  
  // Calculate popularity score (weighted by recency and frequency)
  const daysSinceCreation = (Date.now() - this.createdAt) / (1000 * 60 * 60 * 24);
  const recentUsageWeight = this.usage.lastUsed ? 
    Math.max(0, 1 - (Date.now() - this.usage.lastUsed) / (1000 * 60 * 60 * 24 * 30)) : 0; // 30 days decay
  
  this.usage.popularityScore = (this.usage.totalTransactions / Math.max(1, daysSinceCreation)) * 
                               (1 + recentUsageWeight);
  
  return this.save();
};

// Static method to get default categories
categorySchema.statics.getDefaultCategories = function() {
  return [
    // Income Categories
    { name: 'Salary', slug: 'salary', type: 'income', icon: 'work', color: '#10B981', isSystem: true, sortOrder: 1 },
    { name: 'Freelance', slug: 'freelance', type: 'income', icon: 'freelance', color: '#3B82F6', isSystem: true, sortOrder: 2 },
    { name: 'Business Income', slug: 'business_income', type: 'income', icon: 'business', color: '#8B5CF6', isSystem: true, sortOrder: 3 },
    { name: 'Investment Income', slug: 'investment_income', type: 'income', icon: 'trending_up', color: '#F59E0B', isSystem: true, sortOrder: 4 },
    { name: 'Rental Income', slug: 'rental_income', type: 'income', icon: 'home', color: '#EF4444', isSystem: true, sortOrder: 5 },
    { name: 'Other Income', slug: 'other_income', type: 'income', icon: 'add_circle', color: '#6B7280', isSystem: true, sortOrder: 99 },
    
    // Expense Categories
    { name: 'Food & Dining', slug: 'food', type: 'expense', icon: 'restaurant', color: '#EF4444', isSystem: true, sortOrder: 1 },
    { name: 'Transportation', slug: 'transportation', type: 'expense', icon: 'directions_car', color: '#3B82F6', isSystem: true, sortOrder: 2 },
    { name: 'Housing', slug: 'housing', type: 'expense', icon: 'home', color: '#8B5CF6', isSystem: true, sortOrder: 3 },
    { name: 'Utilities', slug: 'utilities', type: 'expense', icon: 'flash_on', color: '#F59E0B', isSystem: true, sortOrder: 4 },
    { name: 'Healthcare', slug: 'healthcare', type: 'expense', icon: 'local_hospital', color: '#10B981', isSystem: true, sortOrder: 5 },
    { name: 'Entertainment', slug: 'entertainment', type: 'expense', icon: 'movie', color: '#EC4899', isSystem: true, sortOrder: 6 },
    { name: 'Shopping', slug: 'shopping', type: 'expense', icon: 'shopping_cart', color: '#F97316', isSystem: true, sortOrder: 7 },
    { name: 'Education', slug: 'education', type: 'expense', icon: 'school', color: '#06B6D4', isSystem: true, sortOrder: 8 },
    { name: 'Insurance', slug: 'insurance', type: 'expense', icon: 'security', color: '#84CC16', isSystem: true, sortOrder: 9 },
    { name: 'Other Expenses', slug: 'other_expense', type: 'expense', icon: 'more_horiz', color: '#6B7280', isSystem: true, sortOrder: 99 },
    
    // Business Categories
    { name: 'Product Sales', slug: 'product_sales', type: 'business_income', icon: 'inventory', color: '#10B981', isSystem: true, sortOrder: 1 },
    { name: 'Service Sales', slug: 'service_sales', type: 'business_income', icon: 'handyman', color: '#3B82F6', isSystem: true, sortOrder: 2 },
    { name: 'Raw Materials', slug: 'raw_materials', type: 'business_expense', icon: 'inventory_2', color: '#EF4444', isSystem: true, sortOrder: 1 },
    { name: 'Equipment', slug: 'equipment', type: 'business_expense', icon: 'build', color: '#F59E0B', isSystem: true, sortOrder: 2 },
    { name: 'Office Supplies', slug: 'office_supplies', type: 'business_expense', icon: 'business_center', color: '#8B5CF6', isSystem: true, sortOrder: 3 },
    { name: 'Marketing', slug: 'marketing', type: 'business_expense', icon: 'campaign', color: '#EC4899', isSystem: true, sortOrder: 4 }
  ];
};

// Static method to find by AI patterns
categorySchema.statics.findByAIPattern = function(text, type = null) {
  const query = {
    'aiPatterns.pattern': { $regex: text, $options: 'i' },
    isActive: true
  };
  
  if (type) {
    query.type = type;
  }
  
  return this.find(query)
    .sort({ 'aiPatterns.confidence': -1, 'usage.popularityScore': -1 })
    .limit(5);
};

module.exports = mongoose.model('Category', categorySchema);
