const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const systemUserSchema = new mongoose.Schema({
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [8, 'Password must be at least 8 characters'],
    select: false
  },
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters']
  },
  avatar: {
    type: String,
    default: null
  },
  role: {
    type: String,
    required: [true, 'Role is required'],
    enum: ['super_admin', 'admin', 'moderator', 'support', 'analyst'],
    default: 'support'
  },
  permissions: [{
    resource: {
      type: String,
      required: true,
      enum: ['users', 'accounts', 'transactions', 'business', 'analytics', 'system', 'reports']
    },
    actions: [{
      type: String,
      enum: ['create', 'read', 'update', 'delete', 'export', 'import']
    }]
  }],
  department: {
    type: String,
    enum: ['engineering', 'support', 'marketing', 'finance', 'operations'],
    required: true
  },
  employeeId: {
    type: String,
    unique: true,
    sparse: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastLoginAt: Date,
  lastActiveAt: Date,
  loginCount: {
    type: Number,
    default: 0
  },
  security: {
    twoFactorEnabled: { type: Boolean, default: true },
    twoFactorSecret: { type: String, select: false },
    lastPasswordChange: { type: Date, default: Date.now },
    loginAttempts: { type: Number, default: 0 },
    lockUntil: Date,
    ipWhitelist: [String],
    sessionTimeout: { type: Number, default: 8 * 60 * 60 * 1000 } // 8 hours
  },
  auditLog: [{
    action: String,
    resource: String,
    resourceId: String,
    details: mongoose.Schema.Types.Mixed,
    timestamp: { type: Date, default: Date.now },
    ipAddress: String,
    userAgent: String
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SystemUser'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SystemUser'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
systemUserSchema.index({ email: 1 });
systemUserSchema.index({ role: 1 });
systemUserSchema.index({ isActive: 1 });
systemUserSchema.index({ department: 1 });
systemUserSchema.index({ employeeId: 1 });

// Pre-save middleware to hash password
systemUserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare password
systemUserSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Method to check if account is locked
systemUserSchema.methods.isLocked = function() {
  return !!(this.security.lockUntil && this.security.lockUntil > Date.now());
};

// Method to check permissions
systemUserSchema.methods.hasPermission = function(resource, action) {
  if (this.role === 'super_admin') return true;
  
  const permission = this.permissions.find(p => p.resource === resource);
  return permission && permission.actions.includes(action);
};

// Method to log audit action
systemUserSchema.methods.logAudit = function(action, resource, resourceId, details = {}, req = {}) {
  this.auditLog.push({
    action,
    resource,
    resourceId,
    details,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent')
  });
  
  // Keep only last 1000 audit entries
  if (this.auditLog.length > 1000) {
    this.auditLog = this.auditLog.slice(-1000);
  }
  
  return this.save();
};

// Method to update last login
systemUserSchema.methods.updateLastLogin = function() {
  return this.updateOne({
    $set: {
      lastLoginAt: new Date(),
      lastActiveAt: new Date()
    },
    $inc: { loginCount: 1 },
    $unset: { 'security.lockUntil': 1, 'security.loginAttempts': 1 }
  });
};

// Static method to get default permissions by role
systemUserSchema.statics.getDefaultPermissions = function(role) {
  const permissions = {
    super_admin: [
      { resource: 'users', actions: ['create', 'read', 'update', 'delete', 'export'] },
      { resource: 'accounts', actions: ['create', 'read', 'update', 'delete', 'export'] },
      { resource: 'transactions', actions: ['create', 'read', 'update', 'delete', 'export'] },
      { resource: 'business', actions: ['create', 'read', 'update', 'delete', 'export'] },
      { resource: 'analytics', actions: ['read', 'export'] },
      { resource: 'system', actions: ['create', 'read', 'update', 'delete'] },
      { resource: 'reports', actions: ['create', 'read', 'export'] }
    ],
    admin: [
      { resource: 'users', actions: ['read', 'update', 'export'] },
      { resource: 'accounts', actions: ['read', 'update', 'export'] },
      { resource: 'transactions', actions: ['read', 'update', 'export'] },
      { resource: 'business', actions: ['read', 'update', 'export'] },
      { resource: 'analytics', actions: ['read', 'export'] },
      { resource: 'reports', actions: ['create', 'read', 'export'] }
    ],
    moderator: [
      { resource: 'users', actions: ['read', 'update'] },
      { resource: 'accounts', actions: ['read', 'update'] },
      { resource: 'transactions', actions: ['read', 'update'] },
      { resource: 'business', actions: ['read', 'update'] }
    ],
    support: [
      { resource: 'users', actions: ['read'] },
      { resource: 'accounts', actions: ['read'] },
      { resource: 'transactions', actions: ['read'] }
    ],
    analyst: [
      { resource: 'analytics', actions: ['read', 'export'] },
      { resource: 'reports', actions: ['create', 'read', 'export'] }
    ]
  };
  
  return permissions[role] || [];
};

module.exports = mongoose.model('SystemUser', systemUserSchema);
