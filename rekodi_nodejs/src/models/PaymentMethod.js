const mongoose = require('mongoose');

const paymentMethodSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Payment method name is required'],
    trim: true,
    maxlength: [100, 'Payment method name cannot exceed 100 characters']
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  category: {
    type: String,
    required: [true, 'Payment category is required'],
    enum: ['cash', 'bank_transfer', 'mobile_money', 'crypto', 'card_payment', 'digital_wallet', 'other']
  },
  type: {
    type: String,
    required: [true, 'Payment type is required'],
    enum: ['instant', 'delayed', 'scheduled']
  },
  icon: {
    type: String,
    default: 'payment'
  },
  color: {
    type: String,
    default: '#6B7280',
    match: [/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Please enter a valid hex color']
  },
  description: {
    type: String,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  
  // Regional and country-specific information
  regions: [{
    type: String,
    enum: ['global', 'africa', 'kenya', 'nigeria', 'south_africa', 'ghana', 'uganda', 'tanzania']
  }],
  countries: [String], // ISO country codes
  
  // Provider information
  provider: {
    name: String,
    website: String,
    supportContact: String,
    apiEndpoint: String
  },
  
  // Transaction details
  transactionInfo: {
    hasTransactionFees: { type: Boolean, default: false },
    feeStructure: {
      type: String,
      enum: ['fixed', 'percentage', 'tiered', 'none'],
      default: 'none'
    },
    minAmount: { type: Number, default: 0 },
    maxAmount: Number,
    processingTime: {
      type: String,
      enum: ['instant', 'minutes', 'hours', 'days'],
      default: 'instant'
    },
    requiresVerification: { type: Boolean, default: false }
  },
  
  // Integration details
  integration: {
    isIntegrated: { type: Boolean, default: false },
    apiKey: { type: String, select: false },
    webhookUrl: String,
    testMode: { type: Boolean, default: true }
  },
  
  // Usage and popularity
  usage: {
    totalTransactions: { type: Number, default: 0 },
    totalAmount: { type: Number, default: 0 },
    lastUsed: Date,
    popularityScore: { type: Number, default: 0 },
    userCount: { type: Number, default: 0 }
  },
  
  // AI and pattern recognition
  aiPatterns: [{
    pattern: String,
    confidence: { type: Number, min: 0, max: 1 },
    source: {
      type: String,
      enum: ['user_training', 'system_learning', 'manual']
    }
  }],
  
  // Localization
  translations: [{
    language: {
      type: String,
      enum: ['en', 'sw', 'fr', 'es'],
      required: true
    },
    name: {
      type: String,
      required: true
    },
    description: String
  }],
  
  isActive: {
    type: Boolean,
    default: true
  },
  isSystem: {
    type: Boolean,
    default: false
  },
  sortOrder: {
    type: Number,
    default: 0
  },
  
  metadata: {
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'SystemUser'
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'SystemUser'
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
paymentMethodSchema.index({ slug: 1 });
paymentMethodSchema.index({ category: 1, isActive: 1 });
paymentMethodSchema.index({ regions: 1 });
paymentMethodSchema.index({ countries: 1 });
paymentMethodSchema.index({ 'usage.popularityScore': -1 });
paymentMethodSchema.index({ sortOrder: 1 });

// Pre-save middleware to generate slug
paymentMethodSchema.pre('save', function(next) {
  if (this.isModified('name') && !this.slug) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '_')
      .replace(/^_+|_+$/g, '');
  }
  next();
});

// Method to get localized name
paymentMethodSchema.methods.getLocalizedName = function(language = 'en') {
  const translation = this.translations.find(t => t.language === language);
  return translation ? translation.name : this.name;
};

// Method to update usage statistics
paymentMethodSchema.methods.updateUsage = function(amount = 0) {
  this.usage.totalTransactions += 1;
  this.usage.totalAmount += Math.abs(amount);
  this.usage.lastUsed = new Date();
  
  // Calculate popularity score
  const daysSinceCreation = (Date.now() - this.createdAt) / (1000 * 60 * 60 * 24);
  const recentUsageWeight = this.usage.lastUsed ? 
    Math.max(0, 1 - (Date.now() - this.usage.lastUsed) / (1000 * 60 * 60 * 24 * 30)) : 0;
  
  this.usage.popularityScore = (this.usage.totalTransactions / Math.max(1, daysSinceCreation)) * 
                               (1 + recentUsageWeight);
  
  return this.save();
};

// Static method to get default payment methods
paymentMethodSchema.statics.getDefaultPaymentMethods = function() {
  return [
    // Cash
    { name: 'Cash', slug: 'cash', category: 'cash', type: 'instant', icon: 'money', color: '#10B981', regions: ['global'], isSystem: true, sortOrder: 1 },
    
    // Mobile Money (Kenya focus)
    { name: 'M-Pesa', slug: 'm_pesa', category: 'mobile_money', type: 'instant', icon: 'phone_android', color: '#00A651', regions: ['kenya', 'africa'], countries: ['KE'], isSystem: true, sortOrder: 2 },
    { name: 'Airtel Money', slug: 'airtel_money', category: 'mobile_money', type: 'instant', icon: 'phone_android', color: '#FF0000', regions: ['kenya', 'africa'], countries: ['KE'], isSystem: true, sortOrder: 3 },
    { name: 'T-Kash', slug: 't_kash', category: 'mobile_money', type: 'instant', icon: 'phone_android', color: '#E91E63', regions: ['kenya'], countries: ['KE'], isSystem: true, sortOrder: 4 },
    { name: 'Equitel', slug: 'equitel', category: 'mobile_money', type: 'instant', icon: 'phone_android', color: '#2196F3', regions: ['kenya'], countries: ['KE'], isSystem: true, sortOrder: 5 },
    
    // Bank Transfers
    { name: 'NCBA Bank', slug: 'ncba_bank', category: 'bank_transfer', type: 'delayed', icon: 'account_balance', color: '#1976D2', regions: ['kenya'], countries: ['KE'], isSystem: true, sortOrder: 10 },
    { name: 'KCB Bank', slug: 'kcb_bank', category: 'bank_transfer', type: 'delayed', icon: 'account_balance', color: '#4CAF50', regions: ['kenya'], countries: ['KE'], isSystem: true, sortOrder: 11 },
    { name: 'Equity Bank', slug: 'equity_bank', category: 'bank_transfer', type: 'delayed', icon: 'account_balance', color: '#FF5722', regions: ['kenya'], countries: ['KE'], isSystem: true, sortOrder: 12 },
    { name: 'Co-operative Bank', slug: 'cooperative_bank', category: 'bank_transfer', type: 'delayed', icon: 'account_balance', color: '#009688', regions: ['kenya'], countries: ['KE'], isSystem: true, sortOrder: 13 },
    { name: 'Standard Chartered', slug: 'standard_chartered', category: 'bank_transfer', type: 'delayed', icon: 'account_balance', color: '#0066CC', regions: ['global'], isSystem: true, sortOrder: 14 },
    
    // Card Payments
    { name: 'Visa Credit Card', slug: 'visa_credit', category: 'card_payment', type: 'instant', icon: 'credit_card', color: '#1A1F71', regions: ['global'], isSystem: true, sortOrder: 20 },
    { name: 'Visa Debit Card', slug: 'visa_debit', category: 'card_payment', type: 'instant', icon: 'credit_card', color: '#1A1F71', regions: ['global'], isSystem: true, sortOrder: 21 },
    { name: 'Mastercard Credit', slug: 'mastercard_credit', category: 'card_payment', type: 'instant', icon: 'credit_card', color: '#EB001B', regions: ['global'], isSystem: true, sortOrder: 22 },
    { name: 'Mastercard Debit', slug: 'mastercard_debit', category: 'card_payment', type: 'instant', icon: 'credit_card', color: '#EB001B', regions: ['global'], isSystem: true, sortOrder: 23 },
    
    // Cryptocurrency
    { name: 'Bitcoin', slug: 'bitcoin', category: 'crypto', type: 'delayed', icon: 'currency_bitcoin', color: '#F7931A', regions: ['global'], isSystem: true, sortOrder: 30 },
    { name: 'Ethereum', slug: 'ethereum', category: 'crypto', type: 'delayed', icon: 'currency_bitcoin', color: '#627EEA', regions: ['global'], isSystem: true, sortOrder: 31 },
    { name: 'USDT', slug: 'usdt', category: 'crypto', type: 'instant', icon: 'currency_bitcoin', color: '#26A17B', regions: ['global'], isSystem: true, sortOrder: 32 },
    { name: 'USDC', slug: 'usdc', category: 'crypto', type: 'instant', icon: 'currency_bitcoin', color: '#2775CA', regions: ['global'], isSystem: true, sortOrder: 33 },
    
    // Digital Wallets
    { name: 'PayPal', slug: 'paypal', category: 'digital_wallet', type: 'instant', icon: 'account_balance_wallet', color: '#0070BA', regions: ['global'], isSystem: true, sortOrder: 40 },
    { name: 'Apple Pay', slug: 'apple_pay', category: 'digital_wallet', type: 'instant', icon: 'phone_iphone', color: '#000000', regions: ['global'], isSystem: true, sortOrder: 41 },
    { name: 'Google Pay', slug: 'google_pay', category: 'digital_wallet', type: 'instant', icon: 'phone_android', color: '#4285F4', regions: ['global'], isSystem: true, sortOrder: 42 }
  ];
};

// Static method to find by region
paymentMethodSchema.statics.findByRegion = function(region, country = null) {
  const query = {
    isActive: true,
    $or: [
      { regions: region },
      { regions: 'global' }
    ]
  };
  
  if (country) {
    query.$or.push({ countries: country });
  }
  
  return this.find(query).sort({ sortOrder: 1, 'usage.popularityScore': -1 });
};

// Static method to find by AI patterns
paymentMethodSchema.statics.findByAIPattern = function(text) {
  return this.find({
    'aiPatterns.pattern': { $regex: text, $options: 'i' },
    isActive: true
  }).sort({ 'aiPatterns.confidence': -1, 'usage.popularityScore': -1 });
};

module.exports = mongoose.model('PaymentMethod', paymentMethodSchema);
