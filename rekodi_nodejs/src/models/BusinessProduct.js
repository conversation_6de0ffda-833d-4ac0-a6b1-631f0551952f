const mongoose = require('mongoose');

const businessProductSchema = new mongoose.Schema({
  accountId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Account',
    required: [true, 'Account ID is required']
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required']
  },
  name: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true,
    maxlength: [200, 'Product name cannot exceed 200 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  type: {
    type: String,
    required: [true, 'Product type is required'],
    enum: ['product', 'service'],
    default: 'product'
  },
  category: {
    type: String,
    trim: true,
    maxlength: [100, 'Category cannot exceed 100 characters']
  },
  subcategory: {
    type: String,
    trim: true,
    maxlength: [100, 'Subcategory cannot exceed 100 characters']
  },
  
  // Pricing information
  pricing: {
    costPrice: {
      type: Number,
      min: [0, 'Cost price cannot be negative']
    },
    sellingPrice: {
      type: Number,
      required: [true, 'Selling price is required'],
      min: [0.01, 'Selling price must be greater than 0']
    },
    currency: {
      type: String,
      required: [true, 'Currency is required'],
      default: 'USD',
      enum: ['USD', 'EUR', 'GBP', 'KES', 'NGN', 'ZAR', 'GHS', 'UGX', 'TZS']
    },
    discountPrice: Number,
    wholesalePrice: Number,
    minimumPrice: Number
  },
  
  // Inventory information (for products only)
  inventory: {
    quantity: {
      type: Number,
      default: 0,
      min: [0, 'Quantity cannot be negative']
    },
    minStockLevel: {
      type: Number,
      default: 5,
      min: [0, 'Minimum stock level cannot be negative']
    },
    maxStockLevel: Number,
    reorderPoint: Number,
    unit: {
      type: String,
      default: 'pcs',
      enum: ['pcs', 'kg', 'g', 'liter', 'ml', 'meter', 'cm', 'box', 'pack', 'dozen', 'other']
    },
    location: String,
    warehouse: String
  },
  
  // Product identification
  identifiers: {
    sku: {
      type: String,
      unique: true,
      sparse: true,
      trim: true
    },
    barcode: {
      type: String,
      trim: true
    },
    upc: String,
    isbn: String,
    manufacturerCode: String
  },
  
  // Supplier information
  supplier: {
    name: String,
    contact: {
      email: String,
      phone: String,
      address: String
    },
    leadTime: Number, // in days
    minimumOrderQuantity: Number,
    lastOrderDate: Date,
    notes: String
  },
  
  // Media and assets
  media: {
    primaryImage: {
      filename: String,
      url: String,
      alt: String
    },
    images: [{
      filename: String,
      url: String,
      alt: String,
      isPrimary: { type: Boolean, default: false }
    }],
    documents: [{
      filename: String,
      url: String,
      type: {
        type: String,
        enum: ['manual', 'warranty', 'certificate', 'specification', 'other']
      },
      description: String
    }]
  },
  
  // Sales and performance data
  sales: {
    totalSold: { type: Number, default: 0 },
    totalRevenue: { type: Number, default: 0 },
    lastSaleDate: Date,
    averageSalePrice: Number,
    bestSellingPeriod: String,
    seasonality: [{
      month: { type: Number, min: 1, max: 12 },
      averageSales: Number
    }]
  },
  
  // Status and lifecycle
  status: {
    type: String,
    enum: ['active', 'inactive', 'discontinued', 'out_of_stock', 'low_stock'],
    default: 'active'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  
  // Tax and accounting
  tax: {
    isTaxable: { type: Boolean, default: true },
    taxRate: { type: Number, default: 0 },
    taxCategory: String,
    accountingCode: String
  },
  
  // Attributes and specifications
  attributes: [{
    name: String,
    value: String,
    unit: String
  }],
  specifications: mongoose.Schema.Types.Mixed,
  
  // Tags and search
  tags: [String],
  searchKeywords: [String],
  
  // Variants (for products with different sizes, colors, etc.)
  variants: [{
    name: String,
    sku: String,
    barcode: String,
    price: Number,
    quantity: Number,
    attributes: [{
      name: String,
      value: String
    }]
  }],
  
  // Reviews and ratings (if applicable)
  reviews: {
    averageRating: { type: Number, default: 0, min: 0, max: 5 },
    totalReviews: { type: Number, default: 0 },
    lastReviewDate: Date
  },
  
  // Sync and external data
  sync: {
    isSynced: { type: Boolean, default: false },
    lastSyncAt: Date,
    syncErrors: [String],
    externalId: String,
    externalSource: String
  },
  
  // Metadata
  metadata: {
    createdFrom: {
      type: String,
      enum: ['web', 'mobile', 'api', 'import'],
      default: 'mobile'
    },
    lastModifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
businessProductSchema.index({ accountId: 1, status: 1 });
businessProductSchema.index({ accountId: 1, type: 1 });
businessProductSchema.index({ 'identifiers.sku': 1 });
businessProductSchema.index({ 'identifiers.barcode': 1 });
businessProductSchema.index({ name: 'text', description: 'text', tags: 'text' });
businessProductSchema.index({ category: 1, subcategory: 1 });
businessProductSchema.index({ 'inventory.quantity': 1 });
businessProductSchema.index({ 'sales.totalSold': -1 });
businessProductSchema.index({ isFeatured: 1, status: 1 });

// Virtual for profit margin
businessProductSchema.virtual('profitMargin').get(function() {
  if (!this.pricing.costPrice || this.pricing.sellingPrice <= 0) return 0;
  return ((this.pricing.sellingPrice - this.pricing.costPrice) / this.pricing.sellingPrice) * 100;
});

// Virtual for profit per unit
businessProductSchema.virtual('profitPerUnit').get(function() {
  if (!this.pricing.costPrice) return this.pricing.sellingPrice;
  return this.pricing.sellingPrice - this.pricing.costPrice;
});

// Virtual for inventory value
businessProductSchema.virtual('inventoryValue').get(function() {
  if (this.type === 'service') return 0;
  return (this.inventory.quantity || 0) * (this.pricing.costPrice || this.pricing.sellingPrice);
});

// Virtual for stock status
businessProductSchema.virtual('stockStatus').get(function() {
  if (this.type === 'service') return 'not_applicable';
  if (this.inventory.quantity <= 0) return 'out_of_stock';
  if (this.inventory.quantity <= this.inventory.minStockLevel) return 'low_stock';
  return 'in_stock';
});

// Pre-save middleware to auto-generate SKU if not provided
businessProductSchema.pre('save', function(next) {
  if (!this.identifiers.sku && this.isNew) {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    this.identifiers.sku = `${this.type.toUpperCase()}-${timestamp}-${random}`.toUpperCase();
  }
  
  // Update status based on inventory
  if (this.type === 'product') {
    if (this.inventory.quantity <= 0) {
      this.status = 'out_of_stock';
    } else if (this.inventory.quantity <= this.inventory.minStockLevel) {
      this.status = 'low_stock';
    } else if (this.status === 'out_of_stock' || this.status === 'low_stock') {
      this.status = 'active';
    }
  }
  
  next();
});

// Method to update inventory
businessProductSchema.methods.updateInventory = function(quantityChange, operation = 'add') {
  if (this.type === 'service') return this;
  
  if (operation === 'add') {
    this.inventory.quantity += quantityChange;
  } else if (operation === 'subtract') {
    this.inventory.quantity = Math.max(0, this.inventory.quantity - quantityChange);
  } else if (operation === 'set') {
    this.inventory.quantity = Math.max(0, quantityChange);
  }
  
  return this.save();
};

// Method to record sale
businessProductSchema.methods.recordSale = function(quantity, salePrice) {
  this.sales.totalSold += quantity;
  this.sales.totalRevenue += (salePrice * quantity);
  this.sales.lastSaleDate = new Date();
  this.sales.averageSalePrice = this.sales.totalRevenue / this.sales.totalSold;
  
  // Update inventory for products
  if (this.type === 'product') {
    this.inventory.quantity = Math.max(0, this.inventory.quantity - quantity);
  }
  
  return this.save();
};

// Method to check if reorder is needed
businessProductSchema.methods.needsReorder = function() {
  if (this.type === 'service') return false;
  return this.inventory.quantity <= (this.inventory.reorderPoint || this.inventory.minStockLevel);
};

// Static method to get low stock products
businessProductSchema.statics.getLowStockProducts = function(accountId) {
  return this.find({
    accountId: accountId,
    type: 'product',
    status: { $in: ['active', 'low_stock'] },
    $expr: { $lte: ['$inventory.quantity', '$inventory.minStockLevel'] }
  }).sort({ 'inventory.quantity': 1 });
};

// Static method to get best selling products
businessProductSchema.statics.getBestSellingProducts = function(accountId, limit = 10) {
  return this.find({
    accountId: accountId,
    status: 'active'
  }).sort({ 'sales.totalSold': -1 }).limit(limit);
};

// Static method to get inventory summary
businessProductSchema.statics.getInventorySummary = function(accountId) {
  return this.aggregate([
    { $match: { accountId: new mongoose.Types.ObjectId(accountId), type: 'product' } },
    {
      $group: {
        _id: null,
        totalProducts: { $sum: 1 },
        totalQuantity: { $sum: '$inventory.quantity' },
        totalValue: { 
          $sum: { 
            $multiply: ['$inventory.quantity', '$pricing.costPrice'] 
          } 
        },
        lowStockCount: {
          $sum: {
            $cond: [
              { $lte: ['$inventory.quantity', '$inventory.minStockLevel'] },
              1,
              0
            ]
          }
        },
        outOfStockCount: {
          $sum: {
            $cond: [
              { $eq: ['$inventory.quantity', 0] },
              1,
              0
            ]
          }
        }
      }
    }
  ]);
};

module.exports = mongoose.model('BusinessProduct', businessProductSchema);
