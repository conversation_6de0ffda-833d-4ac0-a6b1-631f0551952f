const mongoose = require('mongoose');

const accountSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required']
  },
  name: {
    type: String,
    required: [true, 'Account name is required'],
    trim: true,
    maxlength: [100, 'Account name cannot exceed 100 characters']
  },
  type: {
    type: String,
    required: [true, 'Account type is required'],
    enum: ['personal', 'business'],
    default: 'personal'
  },
  currency: {
    type: String,
    required: [true, 'Currency is required'],
    default: 'USD',
    enum: ['USD', 'EUR', 'GBP', 'KES', 'NGN', 'ZAR', 'GHS', 'UGX', 'TZS', 'MAD', 'DZD', 'TND']
  },
  timezone: {
    type: String,
    default: 'UTC'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isPrimary: {
    type: Boolean,
    default: false
  },
  description: {
    type: String,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  avatar: {
    type: String,
    default: null
  },
  
  // Business-specific information
  businessInfo: {
    businessName: {
      type: String,
      trim: true,
      maxlength: [200, 'Business name cannot exceed 200 characters']
    },
    businessType: {
      type: String,
      enum: ['retail', 'wholesale', 'service', 'manufacturing', 'restaurant', 'consulting', 'ecommerce', 'other']
    },
    industry: {
      type: String,
      enum: ['technology', 'healthcare', 'finance', 'education', 'retail', 'manufacturing', 'agriculture', 'other']
    },
    registrationNumber: String,
    taxId: String,
    vatNumber: String,
    website: String,
    address: {
      street: String,
      city: String,
      state: String,
      country: String,
      zipCode: String,
      coordinates: {
        latitude: Number,
        longitude: Number
      }
    },
    contact: {
      phone: String,
      email: String,
      fax: String
    },
    bankDetails: {
      bankName: String,
      accountNumber: String,
      routingNumber: String,
      swiftCode: String
    }
  },
  
  // Account settings and preferences
  settings: {
    autoCategorizeSMS: { type: Boolean, default: true },
    enableReceipts: { type: Boolean, default: true },
    budgetAlerts: { type: Boolean, default: true },
    lowStockAlerts: { type: Boolean, default: true },
    transactionNotifications: { type: Boolean, default: true },
    weeklyReports: { type: Boolean, default: false },
    monthlyReports: { type: Boolean, default: true },
    dataRetentionPeriod: { type: Number, default: 365 }, // days
    backupFrequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly'],
      default: 'weekly'
    },
    defaultPaymentMethod: String,
    fiscalYearStart: {
      type: String,
      default: '01-01' // MM-DD format
    }
  },
  
  // Financial summary (calculated fields)
  summary: {
    totalIncome: { type: Number, default: 0 },
    totalExpenses: { type: Number, default: 0 },
    totalSales: { type: Number, default: 0 },
    totalPurchases: { type: Number, default: 0 },
    netProfit: { type: Number, default: 0 },
    lastCalculatedAt: Date,
    transactionCount: { type: Number, default: 0 },
    lastTransactionAt: Date
  },
  
  // Subscription and limits
  limits: {
    maxTransactions: { type: Number, default: 100 },
    maxProducts: { type: Number, default: 50 },
    maxBudgets: { type: Number, default: 10 },
    storageLimit: { type: Number, default: 100 * 1024 * 1024 }, // 100MB in bytes
    apiCallsPerDay: { type: Number, default: 1000 }
  },
  
  // Sync and backup information
  sync: {
    lastSyncAt: Date,
    syncStatus: {
      type: String,
      enum: ['synced', 'pending', 'error'],
      default: 'synced'
    },
    syncErrors: [String],
    lastBackupAt: Date,
    backupStatus: {
      type: String,
      enum: ['success', 'failed', 'pending'],
      default: 'pending'
    }
  },
  
  // Metadata
  metadata: {
    createdFrom: {
      type: String,
      enum: ['web', 'mobile', 'api'],
      default: 'mobile'
    },
    migrationData: {
      migratedFrom: String,
      migrationDate: Date,
      originalId: String
    }
  },
  
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'archived'],
    default: 'active'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
accountSchema.index({ userId: 1, type: 1 });
accountSchema.index({ userId: 1, isActive: 1 });
accountSchema.index({ userId: 1, isPrimary: 1 });
accountSchema.index({ type: 1, status: 1 });
accountSchema.index({ 'businessInfo.businessType': 1 });
accountSchema.index({ createdAt: -1 });

// Ensure only one primary account per user per type
accountSchema.index({ userId: 1, type: 1, isPrimary: 1 }, { 
  unique: true, 
  partialFilterExpression: { isPrimary: true } 
});

// Virtual for transactions
accountSchema.virtual('transactions', {
  ref: 'Transaction',
  localField: '_id',
  foreignField: 'accountId'
});

// Virtual for business products
accountSchema.virtual('products', {
  ref: 'BusinessProduct',
  localField: '_id',
  foreignField: 'accountId'
});

// Virtual for budgets
accountSchema.virtual('budgets', {
  ref: 'Budget',
  localField: '_id',
  foreignField: 'accountId'
});

// Pre-save middleware to ensure only one primary account per type
accountSchema.pre('save', async function(next) {
  if (this.isPrimary && this.isModified('isPrimary')) {
    await this.constructor.updateMany(
      { 
        userId: this.userId, 
        type: this.type, 
        _id: { $ne: this._id } 
      },
      { isPrimary: false }
    );
  }
  next();
});

// Method to calculate account summary
accountSchema.methods.calculateSummary = async function() {
  const Transaction = mongoose.model('Transaction');
  const BusinessTransaction = mongoose.model('BusinessTransaction');
  
  const [personalStats, businessStats] = await Promise.all([
    Transaction.aggregate([
      { $match: { accountId: this._id } },
      {
        $group: {
          _id: '$type',
          total: { $sum: '$amount' },
          count: { $sum: 1 },
          lastTransaction: { $max: '$date' }
        }
      }
    ]),
    BusinessTransaction.aggregate([
      { $match: { accountId: this._id } },
      {
        $group: {
          _id: '$type',
          total: { $sum: '$totalAmount' },
          count: { $sum: 1 },
          lastTransaction: { $max: '$transactionDate' }
        }
      }
    ])
  ]);
  
  const income = personalStats.find(s => s._id === 'income')?.total || 0;
  const expenses = personalStats.find(s => s._id === 'expense')?.total || 0;
  const sales = businessStats.find(s => s._id === 'sale')?.total || 0;
  const purchases = businessStats.find(s => s._id === 'purchase')?.total || 0;
  
  this.summary = {
    totalIncome: income,
    totalExpenses: expenses,
    totalSales: sales,
    totalPurchases: purchases,
    netProfit: (income + sales) - (expenses + purchases),
    lastCalculatedAt: new Date(),
    transactionCount: personalStats.reduce((sum, s) => sum + s.count, 0) + 
                     businessStats.reduce((sum, s) => sum + s.count, 0),
    lastTransactionAt: Math.max(
      ...personalStats.map(s => s.lastTransaction || 0),
      ...businessStats.map(s => s.lastTransaction || 0)
    ) || null
  };
  
  return this.save();
};

// Method to check if account has reached limits
accountSchema.methods.checkLimits = async function() {
  const Transaction = mongoose.model('Transaction');
  const BusinessProduct = mongoose.model('BusinessProduct');
  const Budget = mongoose.model('Budget');
  
  const [transactionCount, productCount, budgetCount] = await Promise.all([
    Transaction.countDocuments({ accountId: this._id }),
    BusinessProduct.countDocuments({ accountId: this._id }),
    Budget.countDocuments({ accountId: this._id })
  ]);
  
  return {
    transactions: {
      current: transactionCount,
      limit: this.limits.maxTransactions,
      exceeded: transactionCount >= this.limits.maxTransactions
    },
    products: {
      current: productCount,
      limit: this.limits.maxProducts,
      exceeded: productCount >= this.limits.maxProducts
    },
    budgets: {
      current: budgetCount,
      limit: this.limits.maxBudgets,
      exceeded: budgetCount >= this.limits.maxBudgets
    }
  };
};

module.exports = mongoose.model('Account', accountSchema);
