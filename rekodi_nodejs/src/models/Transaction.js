const mongoose = require('mongoose');

const transactionSchema = new mongoose.Schema({
  accountId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Account',
    required: [true, 'Account ID is required']
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required']
  },
  title: {
    type: String,
    required: [true, 'Transaction title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  amount: {
    type: Number,
    required: [true, 'Amount is required'],
    min: [0.01, 'Amount must be greater than 0']
  },
  type: {
    type: String,
    required: [true, 'Transaction type is required'],
    enum: ['income', 'expense', 'transfer']
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: [true, 'Category is required']
  },
  subcategory: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category'
  },
  paymentMethod: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'PaymentMethod',
    required: [true, 'Payment method is required']
  },
  currency: {
    type: String,
    required: [true, 'Currency is required'],
    default: 'USD',
    enum: ['USD', 'EUR', 'GBP', 'KES', 'NGN', 'ZAR', 'GHS', 'UGX', 'TZS', 'MAD', 'DZD', 'TND']
  },
  exchangeRate: {
    type: Number,
    default: 1
  },
  baseAmount: {
    type: Number // Amount in account's base currency
  },
  date: {
    type: Date,
    required: [true, 'Transaction date is required'],
    default: Date.now
  },
  location: {
    name: String,
    address: String,
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  tags: [String],
  
  // Transaction fees and charges
  fees: {
    transactionFee: { type: Number, default: 0 },
    processingFee: { type: Number, default: 0 },
    exchangeFee: { type: Number, default: 0 },
    totalFees: { type: Number, default: 0 }
  },
  
  // Receipt and attachments
  receipt: {
    filename: String,
    originalName: String,
    mimetype: String,
    size: Number,
    url: String,
    ocrData: {
      extractedText: String,
      confidence: Number,
      processedAt: Date
    }
  },
  attachments: [{
    filename: String,
    originalName: String,
    mimetype: String,
    size: Number,
    url: String,
    type: {
      type: String,
      enum: ['receipt', 'invoice', 'contract', 'other']
    }
  }],
  
  // SMS and automation data
  smsData: {
    originalMessage: String,
    sender: String,
    receivedAt: Date,
    parsedData: mongoose.Schema.Types.Mixed,
    confidence: Number,
    isVerified: { type: Boolean, default: false }
  },
  
  // AI processing information
  aiProcessing: {
    wasProcessedByAI: { type: Boolean, default: false },
    aiConfidence: Number,
    aiSuggestions: {
      category: String,
      paymentMethod: String,
      tags: [String]
    },
    originalInput: String, // Natural language input
    processedAt: Date
  },
  
  // Recurring transaction information
  recurring: {
    isRecurring: { type: Boolean, default: false },
    recurringTransactionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'RecurringTransaction'
    },
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly', 'quarterly', 'yearly']
    },
    nextDueDate: Date
  },
  
  // Transfer-specific information
  transfer: {
    fromAccountId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Account'
    },
    toAccountId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Account'
    },
    linkedTransactionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Transaction'
    }
  },
  
  // Budget tracking
  budget: {
    budgetId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Budget'
    },
    impactAmount: Number
  },
  
  // Status and verification
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'cancelled'],
    default: 'completed'
  },
  isVerified: {
    type: Boolean,
    default: true
  },
  verificationNotes: String,
  
  // Sync and external data
  sync: {
    isSynced: { type: Boolean, default: false },
    lastSyncAt: Date,
    syncErrors: [String],
    externalId: String,
    externalSource: {
      type: String,
      enum: ['bank_api', 'sms', 'manual', 'import', 'recurring']
    }
  },
  
  // Metadata
  metadata: {
    createdFrom: {
      type: String,
      enum: ['web', 'mobile', 'api', 'sms', 'import'],
      default: 'mobile'
    },
    deviceInfo: {
      platform: String,
      version: String,
      deviceId: String
    },
    ipAddress: String,
    userAgent: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
transactionSchema.index({ accountId: 1, date: -1 });
transactionSchema.index({ userId: 1, date: -1 });
transactionSchema.index({ accountId: 1, type: 1, category: 1 });
transactionSchema.index({ accountId: 1, status: 1 });
transactionSchema.index({ date: -1 });
transactionSchema.index({ type: 1, category: 1 });
transactionSchema.index({ tags: 1 });
transactionSchema.index({ 'recurring.isRecurring': 1, 'recurring.nextDueDate': 1 });
transactionSchema.index({ 'sync.externalId': 1 });

// Virtual for net amount (amount - fees)
transactionSchema.virtual('netAmount').get(function() {
  return this.amount - (this.fees.totalFees || 0);
});

// Virtual for formatted amount with currency
transactionSchema.virtual('formattedAmount').get(function() {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: this.currency
  });
  return formatter.format(this.amount);
});

// Pre-save middleware to calculate base amount and total fees
transactionSchema.pre('save', function(next) {
  // Calculate base amount using exchange rate
  if (this.exchangeRate && this.exchangeRate !== 1) {
    this.baseAmount = this.amount * this.exchangeRate;
  } else {
    this.baseAmount = this.amount;
  }
  
  // Calculate total fees
  this.fees.totalFees = (this.fees.transactionFee || 0) + 
                        (this.fees.processingFee || 0) + 
                        (this.fees.exchangeFee || 0);
  
  next();
});

// Method to update category usage
transactionSchema.methods.updateCategoryUsage = async function() {
  const Category = mongoose.model('Category');
  if (this.category) {
    const category = await Category.findById(this.category);
    if (category) {
      await category.updateUsage(this.amount);
    }
  }
};

// Method to update payment method usage
transactionSchema.methods.updatePaymentMethodUsage = async function() {
  const PaymentMethod = mongoose.model('PaymentMethod');
  if (this.paymentMethod) {
    const paymentMethod = await PaymentMethod.findById(this.paymentMethod);
    if (paymentMethod) {
      await paymentMethod.updateUsage(this.amount);
    }
  }
};

// Method to create linked transfer transaction
transactionSchema.methods.createLinkedTransfer = async function(toAccountId) {
  if (this.type !== 'transfer') return null;
  
  const linkedTransaction = new this.constructor({
    accountId: toAccountId,
    userId: this.userId,
    title: `Transfer from ${this.accountId}`,
    description: this.description,
    amount: this.amount,
    type: 'income',
    category: this.category,
    paymentMethod: this.paymentMethod,
    currency: this.currency,
    date: this.date,
    transfer: {
      fromAccountId: this.accountId,
      toAccountId: toAccountId,
      linkedTransactionId: this._id
    },
    status: this.status,
    metadata: this.metadata
  });
  
  await linkedTransaction.save();
  
  // Update this transaction with linked ID
  this.transfer.linkedTransactionId = linkedTransaction._id;
  await this.save();
  
  return linkedTransaction;
};

// Static method to get transaction summary
transactionSchema.statics.getSummary = function(accountId, startDate, endDate) {
  const matchStage = {
    accountId: new mongoose.Types.ObjectId(accountId),
    status: 'completed'
  };
  
  if (startDate && endDate) {
    matchStage.date = {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    };
  }
  
  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: '$type',
        total: { $sum: '$amount' },
        count: { $sum: 1 },
        avgAmount: { $avg: '$amount' }
      }
    }
  ]);
};

// Static method to get category breakdown
transactionSchema.statics.getCategoryBreakdown = function(accountId, type, startDate, endDate) {
  const matchStage = {
    accountId: new mongoose.Types.ObjectId(accountId),
    type: type,
    status: 'completed'
  };
  
  if (startDate && endDate) {
    matchStage.date = {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    };
  }
  
  return this.aggregate([
    { $match: matchStage },
    {
      $lookup: {
        from: 'categories',
        localField: 'category',
        foreignField: '_id',
        as: 'categoryInfo'
      }
    },
    { $unwind: '$categoryInfo' },
    {
      $group: {
        _id: '$category',
        categoryName: { $first: '$categoryInfo.name' },
        total: { $sum: '$amount' },
        count: { $sum: 1 },
        percentage: { $sum: '$amount' }
      }
    },
    { $sort: { total: -1 } }
  ]);
};

module.exports = mongoose.model('Transaction', transactionSchema);
