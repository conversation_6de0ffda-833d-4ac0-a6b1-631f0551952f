const logger = require('../utils/logger');

const requestLogger = (req, res, next) => {
  const start = Date.now();
  
  // Log request
  logger.info(`${req.method} ${req.originalUrl}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id
  });

  // Log response
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.info(`${req.method} ${req.originalUrl} - ${res.statusCode}`, {
      duration: `${duration}ms`,
      ip: req.ip,
      userId: req.user?.id
    });
  });

  next();
};

const auditLogger = (action, resource, userId, details = {}) => {
  logger.info(`AUDIT: ${action}`, {
    action,
    resource,
    userId,
    timestamp: new Date().toISOString(),
    ...details
  });
};

module.exports = {
  requestLogger,
  auditLogger
};
