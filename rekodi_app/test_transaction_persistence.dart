import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'lib/core/services/appwrite_service.dart';
import 'lib/core/services/personal_transaction_service.dart';
import 'lib/core/services/account_service.dart';
import 'lib/core/models/transaction.dart';

/// Simple test script to verify transaction persistence to Appwrite
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🧪 Testing Transaction Persistence to Appwrite...\n');
  
  // Initialize services
  Get.put(AppwriteService());
  Get.put(AccountService());
  Get.put(PersonalTransactionService());
  
  await Get.find<AppwriteService>().onInit();
  await Get.find<AccountService>().onInit();
  await Get.find<PersonalTransactionService>().onInit();
  
  // Wait for initialization
  await Future.delayed(Duration(seconds: 2));
  
  final appwriteService = Get.find<AppwriteService>();
  final transactionService = Get.find<PersonalTransactionService>();
  final accountService = Get.find<AccountService>();
  
  print('📊 Service Status:');
  print('  - Appwrite Initialized: ${appwriteService.isInitialized}');
  print('  - Appwrite Online: ${appwriteService.isOnline}');
  print('  - Current Account ID: ${accountService.currentAccountId}');
  print('  - Current Account Type: ${accountService.currentAccountType}');
  print('');
  
  // Test health check
  print('🔍 Testing Appwrite Health...');
  final isHealthy = await appwriteService.checkHealth();
  print('  - Health Check: ${isHealthy ? "✅ PASS" : "❌ FAIL"}');
  print('');
  
  if (!isHealthy) {
    print('❌ Appwrite service is not healthy. Cannot proceed with tests.');
    return;
  }
  
  // Test transaction creation
  print('💰 Testing Transaction Creation...');
  
  try {
    final success = await transactionService.addTransaction(
      title: 'Test Transaction - ${DateTime.now().millisecondsSinceEpoch}',
      description: 'Test transaction for persistence verification',
      amount: 100.0,
      type: TransactionType.income,
      category: TransactionCategory.salary,
      date: DateTime.now(),
      paymentMethod: 'cash',
    );
    
    print('  - Transaction Creation: ${success ? "✅ PASS" : "❌ FAIL"}');
    
    if (success) {
      // Wait a moment for sync
      await Future.delayed(Duration(seconds: 3));
      
      // Try to load transactions from Appwrite
      print('📥 Testing Transaction Sync from Appwrite...');
      await transactionService.loadTransactionsFromAppwrite();
      
      final transactions = transactionService.transactions;
      print('  - Total Transactions Loaded: ${transactions.length}');
      
      if (transactions.isNotEmpty) {
        final latestTransaction = transactions.first;
        print('  - Latest Transaction: ${latestTransaction.title}');
        print('  - Amount: ${latestTransaction.amount}');
        print('  - Date: ${latestTransaction.date}');
        print('  - Sync Status: ✅ SUCCESS');
      } else {
        print('  - Sync Status: ❌ NO TRANSACTIONS FOUND');
      }
    }
    
  } catch (e, stackTrace) {
    print('  - Transaction Creation: ❌ ERROR - $e');
    print('  - Stack Trace: $stackTrace');
  }
  
  print('\n🏁 Test Complete');
}
