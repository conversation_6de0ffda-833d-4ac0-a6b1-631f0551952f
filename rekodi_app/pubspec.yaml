name: rekodi_app
description: "Smart financial management with AI-powered transaction tracking, budgeting, and analytics for personal and business use."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  get: ^4.6.5
  get_storage: ^2.1.1
  appwrite: ^17.0.0
  curved_navigation_bar: ^1.0.0

  calendar_view: ^1.3.0

  syncfusion_flutter_charts: ^22.1.35
  glassmorphism_widgets: ^0.2.2

  http: ^1.2.2
  #  sqflite: ^2.0.0+4
  sqlite3_flutter_libs: ^0.5.27

  drift: ^2.19.1
  flutter_svg: ^2.1.0
  lottie: ^3.2.0
  smooth_page_indicator: ^1.2.1
  google_fonts: ^6.2.1
  image_picker: ^1.0.4
  camera: ^0.10.5+5
  animate_do: ^4.2.0
  # sms_advanced: ^1.1.0  # TODO: Fix namespace issues
  # workmanager: ^0.5.2  # Temporarily disabled due to build issues
  flutter_local_notifications: ^17.2.2
  timezone: ^0.9.4

  # Additional utilities
  uuid: ^4.5.1
  connectivity_plus: ^6.0.5
  path_provider: ^2.1.4
  path: ^1.9.0
  intl: ^0.18.1
  json_annotation: ^4.9.0

  # Export and sharing
  share_plus: ^7.2.2
  pdf: ^3.10.7

  # Security and encryption
  local_auth: ^2.3.0
  crypto: ^3.0.5
  encrypt: ^5.0.3

  # SMS and Message Parsing (temporarily disabled for build compatibility)
  # telephony: ^0.2.0  # TODO: Replace with compatible SMS package
  permission_handler: ^11.3.1

  # Machine Learning and Text Processing
  google_mlkit_commons: ^0.5.0
  google_mlkit_text_recognition: ^0.9.0
  google_mlkit_barcode_scanning: ^0.8.0
  string_similarity: ^2.0.0

  # Charts and Visualizations
  fl_chart: ^0.69.0

  # Camera and OCR (already included above)

  # Biometric Authentication (already included above)

  # Notifications (already included above)

  # Background Processing
  # workmanager: ^0.5.2  # TODO: Replace with compatible background processing package

  # Additional UI Components
  shimmer: ^3.0.0
  cached_network_image: ^3.4.1
  flutter_staggered_animations: ^1.1.1

  # File Handling (already included above)
  file_picker: ^8.1.2
  csv: ^6.0.0

  # Encryption and Security (already included above)

dev_dependencies:
  drift_dev: ^2.19.1
  build_runner: ^2.4.0
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  json_serializable: ^6.8.0

  # Testing dependencies
  mockito: ^5.4.4
  integration_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/illustrations/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
