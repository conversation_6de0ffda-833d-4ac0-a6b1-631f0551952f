# Rekodi Backend API Documentation

## Overview

This document provides comprehensive guidelines for creating a Node.js Express backend for the Rekodi financial management application. The backend will handle user authentication, transaction management, business analytics, and data synchronization.

## Technology Stack

- **Runtime**: Node.js (v18+)
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT (JSON Web Tokens)
- **Validation**: Joi
- **File Upload**: Multer
- **Environment**: dotenv
- **Testing**: Jest
- **Documentation**: Swagger/OpenAPI

## Project Structure

```
rekodi-backend/
├── src/
│   ├── controllers/
│   │   ├── auth.controller.js
│   │   ├── transaction.controller.js
│   │   ├── business.controller.js
│   │   ├── analytics.controller.js
│   │   └── user.controller.js
│   ├── models/
│   │   ├── User.js
│   │   ├── Account.js
│   │   ├── Transaction.js
│   │   ├── BusinessProduct.js
│   │   └── RecurringTransaction.js
│   ├── routes/
│   │   ├── auth.routes.js
│   │   ├── transaction.routes.js
│   │   ├── business.routes.js
│   │   └── analytics.routes.js
│   ├── middleware/
│   │   ├── auth.middleware.js
│   │   ├── validation.middleware.js
│   │   └── error.middleware.js
│   ├── services/
│   │   ├── auth.service.js
│   │   ├── transaction.service.js
│   │   ├── analytics.service.js
│   │   └── sms.service.js
│   ├── utils/
│   │   ├── database.js
│   │   ├── logger.js
│   │   └── helpers.js
│   └── app.js
├── tests/
├── docs/
├── package.json
└── server.js
```

## Installation & Setup

### 1. Initialize Project

```bash
mkdir rekodi-backend
cd rekodi-backend
npm init -y
```

### 2. Install Dependencies

```bash
# Core dependencies
npm install express mongoose cors helmet morgan dotenv bcryptjs jsonwebtoken

# Validation and utilities
npm install joi multer uuid moment

# Development dependencies
npm install -D nodemon jest supertest eslint prettier
```

### 3. Environment Configuration

Create `.env` file:

```env
# Server Configuration
PORT=3000
NODE_ENV=development

# Database
MONGODB_URI=mongodb://localhost:27017/rekodi
MONGODB_TEST_URI=mongodb://localhost:27017/rekodi_test

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRE=7d
JWT_REFRESH_EXPIRE=30d

# Appwrite Configuration (for migration)
APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
APPWRITE_PROJECT_ID=683c61460026c3e03330
APPWRITE_API_KEY=your_appwrite_api_key

# SMS Service (for transaction parsing)
SMS_SERVICE_API_KEY=your_sms_service_key

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
```

## Database Schema

### User Model

```javascript
// src/models/User.js
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  avatar: {
    type: String,
    default: null
  },
  isEmailVerified: {
    type: Boolean,
    default: false
  },
  preferences: {
    currency: {
      type: String,
      default: 'USD'
    },
    theme: {
      type: String,
      enum: ['light', 'dark', 'system'],
      default: 'system'
    },
    notifications: {
      email: { type: Boolean, default: true },
      push: { type: Boolean, default: true },
      sms: { type: Boolean, default: false }
    }
  },
  subscription: {
    plan: {
      type: String,
      enum: ['free', 'premium', 'business'],
      default: 'free'
    },
    expiresAt: Date,
    features: [String]
  }
}, {
  timestamps: true
});

// Password hashing middleware
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  this.password = await bcrypt.hash(this.password, 12);
  next();
});

// Password comparison method
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

module.exports = mongoose.model('User', userSchema);
```

### Account Model

```javascript
// src/models/Account.js
const mongoose = require('mongoose');

const accountSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  type: {
    type: String,
    enum: ['personal', 'business'],
    required: true
  },
  currency: {
    type: String,
    default: 'USD'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  businessInfo: {
    businessName: String,
    businessType: String,
    taxId: String,
    address: {
      street: String,
      city: String,
      state: String,
      country: String,
      zipCode: String
    }
  },
  settings: {
    autoCategorizeSMS: { type: Boolean, default: true },
    enableReceipts: { type: Boolean, default: true },
    budgetAlerts: { type: Boolean, default: true }
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('Account', accountSchema);
```

### Transaction Model

```javascript
// src/models/Transaction.js
const mongoose = require('mongoose');

const transactionSchema = new mongoose.Schema({
  accountId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Account',
    required: true
  },
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  transactionCost: {
    type: Number,
    default: 0,
    min: 0
  },
  type: {
    type: String,
    enum: ['income', 'expense', 'sale', 'purchase'],
    required: true
  },
  category: {
    type: String,
    required: true
  },
  paymentMethod: {
    type: String,
    enum: ['cash', 'mpesa', 'bank_transfer', 'card', 'digital_wallet', 'crypto'],
    default: 'cash'
  },
  date: {
    type: Date,
    required: true
  },
  location: {
    type: String,
    trim: true
  },
  tags: [String],
  receipt: {
    filename: String,
    originalName: String,
    mimetype: String,
    size: Number,
    url: String
  },
  smsData: {
    originalMessage: String,
    parsedData: mongoose.Schema.Types.Mixed,
    confidence: Number
  },
  isRecurring: {
    type: Boolean,
    default: false
  },
  recurringTransactionId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'RecurringTransaction'
  }
}, {
  timestamps: true
});

// Indexes for better query performance
transactionSchema.index({ accountId: 1, date: -1 });
transactionSchema.index({ accountId: 1, type: 1, category: 1 });
transactionSchema.index({ accountId: 1, createdAt: -1 });

module.exports = mongoose.model('Transaction', transactionSchema);
```

## API Endpoints

### Authentication Routes

```javascript
// src/routes/auth.routes.js
const express = require('express');
const authController = require('../controllers/auth.controller');
const { validateRegistration, validateLogin } = require('../middleware/validation.middleware');

const router = express.Router();

// POST /api/auth/register
router.post('/register', validateRegistration, authController.register);

// POST /api/auth/login
router.post('/login', validateLogin, authController.login);

// POST /api/auth/refresh
router.post('/refresh', authController.refreshToken);

// POST /api/auth/logout
router.post('/logout', authController.logout);

// POST /api/auth/forgot-password
router.post('/forgot-password', authController.forgotPassword);

// POST /api/auth/reset-password
router.post('/reset-password', authController.resetPassword);

module.exports = router;
```

### Transaction Routes

```javascript
// src/routes/transaction.routes.js
const express = require('express');
const transactionController = require('../controllers/transaction.controller');
const authMiddleware = require('../middleware/auth.middleware');
const { validateTransaction } = require('../middleware/validation.middleware');

const router = express.Router();

// Apply authentication to all routes
router.use(authMiddleware);

// GET /api/transactions
router.get('/', transactionController.getTransactions);

// POST /api/transactions
router.post('/', validateTransaction, transactionController.createTransaction);

// GET /api/transactions/:id
router.get('/:id', transactionController.getTransaction);

// PUT /api/transactions/:id
router.put('/:id', validateTransaction, transactionController.updateTransaction);

// DELETE /api/transactions/:id
router.delete('/:id', transactionController.deleteTransaction);

// POST /api/transactions/bulk
router.post('/bulk', transactionController.createBulkTransactions);

// POST /api/transactions/parse-sms
router.post('/parse-sms', transactionController.parseSMSTransaction);

module.exports = router;
```

## Controllers Implementation

### Authentication Controller

```javascript
// src/controllers/auth.controller.js
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const Account = require('../models/Account');
const authService = require('../services/auth.service');

const generateTokens = (userId) => {
  const accessToken = jwt.sign({ userId }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRE
  });
  
  const refreshToken = jwt.sign({ userId }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_REFRESH_EXPIRE
  });
  
  return { accessToken, refreshToken };
};

const register = async (req, res) => {
  try {
    const { email, password, name, accountType = 'personal' } = req.body;
    
    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User already exists with this email'
      });
    }
    
    // Create user
    const user = new User({ email, password, name });
    await user.save();
    
    // Create default account
    const account = new Account({
      userId: user._id,
      name: accountType === 'business' ? 'My Business' : 'Personal Account',
      type: accountType
    });
    await account.save();
    
    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user._id);
    
    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: {
          id: user._id,
          email: user.email,
          name: user.name
        },
        account: {
          id: account._id,
          name: account.name,
          type: account.type
        },
        tokens: {
          accessToken,
          refreshToken
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Registration failed',
      error: error.message
    });
  }
};

const login = async (req, res) => {
  try {
    const { email, password } = req.body;
    
    // Find user
    const user = await User.findOne({ email });
    if (!user || !(await user.comparePassword(password))) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }
    
    // Get user accounts
    const accounts = await Account.find({ userId: user._id, isActive: true });
    
    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user._id);
    
    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: user._id,
          email: user.email,
          name: user.name,
          preferences: user.preferences
        },
        accounts,
        tokens: {
          accessToken,
          refreshToken
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Login failed',
      error: error.message
    });
  }
};

module.exports = {
  register,
  login,
  refreshToken: authService.refreshToken,
  logout: authService.logout,
  forgotPassword: authService.forgotPassword,
  resetPassword: authService.resetPassword
};
```

### Transaction Controller

```javascript
// src/controllers/transaction.controller.js
const Transaction = require('../models/Transaction');
const Account = require('../models/Account');
const smsService = require('../services/sms.service');

const getTransactions = async (req, res) => {
  try {
    const { accountId, type, category, startDate, endDate, page = 1, limit = 50 } = req.query;

    // Build query
    const query = { accountId };

    if (type) query.type = type;
    if (category) query.category = category;
    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }

    // Execute query with pagination
    const transactions = await Transaction.find(query)
      .sort({ date: -1, createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .populate('accountId', 'name type currency');

    const total = await Transaction.countDocuments(query);

    res.json({
      success: true,
      data: {
        transactions,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch transactions',
      error: error.message
    });
  }
};

const createTransaction = async (req, res) => {
  try {
    const transactionData = {
      ...req.body,
      accountId: req.body.accountId || req.user.defaultAccountId
    };

    // Validate account ownership
    const account = await Account.findOne({
      _id: transactionData.accountId,
      userId: req.user.id
    });

    if (!account) {
      return res.status(403).json({
        success: false,
        message: 'Account not found or access denied'
      });
    }

    const transaction = new Transaction(transactionData);
    await transaction.save();

    res.status(201).json({
      success: true,
      message: 'Transaction created successfully',
      data: { transaction }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to create transaction',
      error: error.message
    });
  }
};

const parseSMSTransaction = async (req, res) => {
  try {
    const { message, accountId } = req.body;

    const parsedData = await smsService.parseTransactionSMS(message);

    if (!parsedData.success) {
      return res.status(400).json({
        success: false,
        message: 'Could not parse SMS message',
        data: parsedData
      });
    }

    // Create transaction from parsed data
    const transaction = new Transaction({
      accountId,
      title: parsedData.title,
      amount: parsedData.amount,
      type: parsedData.type,
      category: parsedData.category,
      paymentMethod: parsedData.paymentMethod,
      date: parsedData.date,
      smsData: {
        originalMessage: message,
        parsedData,
        confidence: parsedData.confidence
      }
    });

    await transaction.save();

    res.status(201).json({
      success: true,
      message: 'SMS transaction parsed and created successfully',
      data: { transaction, parsedData }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to parse SMS transaction',
      error: error.message
    });
  }
};

module.exports = {
  getTransactions,
  createTransaction,
  updateTransaction: async (req, res) => { /* Implementation */ },
  deleteTransaction: async (req, res) => { /* Implementation */ },
  getTransaction: async (req, res) => { /* Implementation */ },
  createBulkTransactions: async (req, res) => { /* Implementation */ },
  parseSMSTransaction
};
```

## Services

### SMS Parser Service

```javascript
// src/services/sms.service.js
const moment = require('moment');

class SMSParserService {
  constructor() {
    this.patterns = {
      mpesa: {
        confirmed: /([A-Z0-9]+) Confirmed\./i,
        amount: /Ksh([\d,]+\.?\d*)/i,
        from: /from ([^.]+)/i,
        to: /to ([^.]+)/i,
        date: /on (\d{1,2}\/\d{1,2}\/\d{2,4})/i,
        time: /at (\d{1,2}:\d{2})/i,
        balance: /New M-PESA balance is Ksh([\d,]+\.?\d*)/i
      },
      bank: {
        debit: /debited|withdrawn/i,
        credit: /credited|deposited/i,
        amount: /(?:KES|USD|Ksh)\s*([\d,]+\.?\d*)/i,
        balance: /balance.*?(?:KES|USD|Ksh)\s*([\d,]+\.?\d*)/i
      }
    };
  }

  async parseTransactionSMS(message) {
    try {
      // Detect SMS type
      const smsType = this.detectSMSType(message);

      if (smsType === 'mpesa') {
        return this.parseMpesaSMS(message);
      } else if (smsType === 'bank') {
        return this.parseBankSMS(message);
      }

      return {
        success: false,
        message: 'Unknown SMS format',
        confidence: 0
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to parse SMS',
        error: error.message,
        confidence: 0
      };
    }
  }

  detectSMSType(message) {
    if (message.includes('M-PESA') || message.includes('MPESA')) {
      return 'mpesa';
    }
    if (message.includes('bank') || message.includes('account')) {
      return 'bank';
    }
    return 'unknown';
  }

  parseMpesaSMS(message) {
    const patterns = this.patterns.mpesa;

    // Extract transaction ID
    const transactionMatch = message.match(patterns.confirmed);
    const transactionId = transactionMatch ? transactionMatch[1] : null;

    // Extract amount
    const amountMatch = message.match(patterns.amount);
    const amount = amountMatch ? parseFloat(amountMatch[1].replace(/,/g, '')) : 0;

    // Determine transaction type and parties
    const fromMatch = message.match(patterns.from);
    const toMatch = message.match(patterns.to);

    let type = 'expense';
    let title = 'M-Pesa Transaction';
    let category = 'other_expense';

    if (fromMatch && toMatch) {
      const from = fromMatch[1].trim();
      const to = toMatch[1].trim();

      // If transaction is from a number to user, it's income
      if (to.toLowerCase().includes('you') || to.includes('254')) {
        type = 'income';
        title = `M-Pesa from ${from}`;
        category = 'other_income';
      } else {
        type = 'expense';
        title = `M-Pesa to ${to}`;
        category = 'other_expense';
      }
    }

    // Extract date and time
    const dateMatch = message.match(patterns.date);
    const timeMatch = message.match(patterns.time);

    let transactionDate = new Date();
    if (dateMatch) {
      const dateStr = dateMatch[1];
      const timeStr = timeMatch ? timeMatch[1] : '12:00';
      transactionDate = moment(`${dateStr} ${timeStr}`, 'DD/MM/YY HH:mm').toDate();
    }

    return {
      success: true,
      title,
      amount,
      type,
      category,
      paymentMethod: 'mpesa',
      date: transactionDate,
      transactionId,
      confidence: 0.9
    };
  }

  parseBankSMS(message) {
    const patterns = this.patterns.bank;

    // Determine transaction type
    const isDebit = patterns.debit.test(message);
    const isCredit = patterns.credit.test(message);

    const type = isCredit ? 'income' : 'expense';
    const category = isCredit ? 'other_income' : 'other_expense';

    // Extract amount
    const amountMatch = message.match(patterns.amount);
    const amount = amountMatch ? parseFloat(amountMatch[1].replace(/,/g, '')) : 0;

    const title = `Bank ${isCredit ? 'Credit' : 'Debit'}`;

    return {
      success: true,
      title,
      amount,
      type,
      category,
      paymentMethod: 'bank_transfer',
      date: new Date(),
      confidence: 0.8
    };
  }
}

module.exports = new SMSParserService();
```

## Middleware

### Authentication Middleware

```javascript
// src/middleware/auth.middleware.js
const jwt = require('jsonwebtoken');
const User = require('../models/User');

const authMiddleware = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. No token provided.'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId).select('-password');

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token. User not found.'
      });
    }

    req.user = user;
    next();
  } catch (error) {
    res.status(401).json({
      success: false,
      message: 'Invalid token.',
      error: error.message
    });
  }
};

module.exports = authMiddleware;
```

## Integration with Flutter App

### API Client Setup

```dart
// lib/core/services/api_client.dart
import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';

class ApiClient {
  static const String baseUrl = 'http://localhost:3000/api';
  late Dio _dio;
  final GetStorage _storage = GetStorage();

  ApiClient() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        final token = _storage.read('access_token');
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        handler.next(options);
      },
      onError: (error, handler) {
        if (error.response?.statusCode == 401) {
          // Handle token refresh or logout
          _handleUnauthorized();
        }
        handler.next(error);
      },
    ));
  }

  Future<Response> get(String path, {Map<String, dynamic>? queryParameters}) {
    return _dio.get(path, queryParameters: queryParameters);
  }

  Future<Response> post(String path, {dynamic data}) {
    return _dio.post(path, data: data);
  }

  Future<Response> put(String path, {dynamic data}) {
    return _dio.put(path, data: data);
  }

  Future<Response> delete(String path) {
    return _dio.delete(path);
  }

  void _handleUnauthorized() {
    _storage.remove('access_token');
    _storage.remove('refresh_token');
    // Navigate to login screen
  }
}
```

## Deployment

### Docker Configuration

```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3000

USER node

CMD ["npm", "start"]
```

### Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongo:27017/rekodi
    depends_on:
      - mongo
    volumes:
      - ./uploads:/app/uploads

  mongo:
    image: mongo:6
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    environment:
      - MONGO_INITDB_DATABASE=rekodi

volumes:
  mongo_data:
```

## Testing

### Example Test

```javascript
// tests/auth.test.js
const request = require('supertest');
const app = require('../src/app');
const User = require('../src/models/User');

describe('Authentication', () => {
  beforeEach(async () => {
    await User.deleteMany({});
  });

  describe('POST /api/auth/register', () => {
    it('should register a new user', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe(userData.email);
      expect(response.body.data.tokens.accessToken).toBeDefined();
    });
  });
});
```

## Security Considerations

1. **Input Validation**: Use Joi for comprehensive input validation
2. **Rate Limiting**: Implement rate limiting for API endpoints
3. **CORS**: Configure CORS properly for production
4. **Helmet**: Use Helmet.js for security headers
5. **Environment Variables**: Never commit sensitive data
6. **Database Security**: Use MongoDB connection with authentication
7. **File Upload Security**: Validate file types and sizes
8. **JWT Security**: Use strong secrets and appropriate expiration times

## Performance Optimization

1. **Database Indexing**: Create appropriate indexes for queries
2. **Caching**: Implement Redis for caching frequently accessed data
3. **Pagination**: Always paginate large result sets
4. **Compression**: Use gzip compression for responses
5. **Connection Pooling**: Configure MongoDB connection pooling
6. **Monitoring**: Implement logging and monitoring with tools like Winston

This documentation provides a comprehensive foundation for building the Rekodi backend API. Adjust configurations and implementations based on specific requirements and deployment environments.
```
