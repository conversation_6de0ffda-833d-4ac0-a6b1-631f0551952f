import 'package:flutter/material.dart';

/// Two-tier payment method system for all transaction types
/// Primary Categories: Cash, Bank Transfer, Mobile Money, Crypto, Card Payment
/// Each category has specific specifications/methods
class PaymentMethods {
  // Primary Categories
  static const String cash = 'cash';
  static const String bankTransfer = 'bank_transfer';
  static const String mobileMoney = 'mobile_money';
  static const String crypto = 'crypto';
  static const String cardPayment = 'card_payment';

  // Legacy constants for backward compatibility
  static const String mPesa = 'm_pesa';
  static const String airtelMoney = 'airtel_money';
  static const String tkash = 't_kash';
  static const String creditCard = 'credit_card';
  static const String debitCard = 'debit_card';
  static const String digitalWallet = 'digital_wallet';
  static const String bitcoin = 'bitcoin';
  static const String ethereum = 'ethereum';
  static const String usdt = 'usdt';
  static const String binanceCoin = 'binance_coin';
  static const String other = 'other';

  /// Get all primary payment categories
  static List<PaymentCategory> getPrimaryCategories() {
    return [
      PaymentCategory(
        id: cash,
        name: 'Cash',
        icon: Icons.money_rounded,
        description: 'Physical cash payments',
        specifications: ['Cash'],
      ),
      PaymentCategory(
        id: bankTransfer,
        name: 'Bank Transfer',
        icon: Icons.account_balance_rounded,
        description: 'Bank and financial institution transfers',
        specifications: [
          'NCBA Bank',
          'KCB Bank',
          'Equity Bank',
          'Co-operative Bank',
          'Standard Chartered',
          'Barclays Bank',
          'Family Bank',
          'DTB Bank',
          'I&M Bank',
          'Other Bank',
        ],
      ),
      PaymentCategory(
        id: mobileMoney,
        name: 'Mobile Money',
        icon: Icons.phone_android_rounded,
        description: 'Mobile money and digital wallet services',
        specifications: [
          'M-Pesa',
          'Airtel Money',
          'T-Kash',
          'Equitel',
          'Other Mobile Money',
        ],
      ),
      PaymentCategory(
        id: crypto,
        name: 'Crypto',
        icon: Icons.currency_bitcoin_rounded,
        description: 'Cryptocurrency payments',
        specifications: [
          'Bitcoin',
          'Ethereum',
          'USDT',
          'USDC',
          'Binance Coin',
          'Cardano',
          'Solana',
          'Other Crypto',
        ],
      ),
      PaymentCategory(
        id: cardPayment,
        name: 'Card Payment',
        icon: Icons.credit_card_rounded,
        description: 'Credit and debit card payments',
        specifications: [
          'Visa Credit Card',
          'Visa Debit Card',
          'Mastercard Credit',
          'Mastercard Debit',
          'American Express',
          'Other Card',
        ],
      ),
    ];
  }

  /// Get specifications for a payment category
  static List<String> getSpecificationsForCategory(String categoryId) {
    final category = getPrimaryCategories().where((cat) => cat.id == categoryId).firstOrNull;
    return category?.specifications ?? [];
  }

  /// Get payment category by ID
  static PaymentCategory? getCategoryById(String id) {
    try {
      return getPrimaryCategories().firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Create a complete payment method from category and specification
  static PaymentMethod createPaymentMethod(String categoryId, String specification) {
    final category = getCategoryById(categoryId);
    if (category == null) {
      throw ArgumentError('Invalid payment category: $categoryId');
    }

    return PaymentMethod(
      category: category,
      specification: specification,
    );
  }

  /// Parse a legacy payment method string to new format
  static PaymentMethod? parseLegacyPaymentMethod(String? legacyMethod) {
    if (legacyMethod == null || legacyMethod.isEmpty) return null;

    switch (legacyMethod.toLowerCase()) {
      case 'cash':
        return createPaymentMethod(cash, 'Cash');
      case 'm_pesa':
      case 'mpesa':
        return createPaymentMethod(mobileMoney, 'M-Pesa');
      case 'airtel_money':
        return createPaymentMethod(mobileMoney, 'Airtel Money');
      case 't_kash':
      case 'tkash':
        return createPaymentMethod(mobileMoney, 'T-Kash');
      case 'bank_transfer':
        return createPaymentMethod(bankTransfer, 'Other Bank');
      case 'credit_card':
        return createPaymentMethod(cardPayment, 'Visa Credit Card');
      case 'debit_card':
        return createPaymentMethod(cardPayment, 'Visa Debit Card');
      case 'bitcoin':
        return createPaymentMethod(crypto, 'Bitcoin');
      case 'ethereum':
        return createPaymentMethod(crypto, 'Ethereum');
      case 'usdt':
        return createPaymentMethod(crypto, 'USDT');
      case 'binance_coin':
        return createPaymentMethod(crypto, 'Binance Coin');
      default:
        return createPaymentMethod(cash, 'Cash'); // Default fallback
    }
  }

  /// Get popular payment categories for Kenya
  static List<PaymentCategory> getPopularCategories() {
    return [
      getCategoryById(cash)!,
      getCategoryById(mobileMoney)!,
      getCategoryById(bankTransfer)!,
      getCategoryById(cardPayment)!,
    ];
  }
}

/// Payment category information (Primary tier)
class PaymentCategory {
  final String id;
  final String name;
  final IconData icon;
  final String description;
  final List<String> specifications;

  const PaymentCategory({
    required this.id,
    required this.name,
    required this.icon,
    required this.description,
    required this.specifications,
  });

  @override
  String toString() => name;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentCategory &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// Complete payment method (Category + Specification)
class PaymentMethod {
  final PaymentCategory category;
  final String specification;

  const PaymentMethod({
    required this.category,
    required this.specification,
  });

  /// Get display name for the payment method
  String get displayName => '${category.name} - $specification';

  /// Get short display name
  String get shortName => specification;

  /// Get category name
  String get categoryName => category.name;

  /// Get category icon
  IconData get icon => category.icon;

  /// Convert to string for storage
  String toStorageString() => '${category.id}:$specification';

  /// Create from storage string
  static PaymentMethod? fromStorageString(String storageString) {
    final parts = storageString.split(':');
    if (parts.length != 2) return null;

    final categoryId = parts[0];
    final specification = parts[1];

    try {
      return PaymentMethods.createPaymentMethod(categoryId, specification);
    } catch (e) {
      return null;
    }
  }

  @override
  String toString() => displayName;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentMethod &&
          runtimeType == other.runtimeType &&
          category == other.category &&
          specification == other.specification;

  @override
  int get hashCode => category.hashCode ^ specification.hashCode;
}

/// Legacy payment method categories (for backward compatibility)
enum PaymentMethodCategory {
  traditional,
  mobileMoney,
  banking,
  cards,
  digital,
  crypto,
  other,
}

/// Legacy payment method information (for backward compatibility)
class PaymentMethodInfo {
  final String id;
  final String name;
  final IconData icon;
  final PaymentMethodCategory category;
  final String description;

  const PaymentMethodInfo({
    required this.id,
    required this.name,
    required this.icon,
    required this.category,
    required this.description,
  });

  @override
  String toString() => name;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentMethodInfo &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// Extension for legacy payment method category display (backward compatibility)
extension PaymentMethodCategoryExtension on PaymentMethodCategory {
  String get displayName {
    switch (this) {
      case PaymentMethodCategory.traditional:
        return 'Traditional';
      case PaymentMethodCategory.mobileMoney:
        return 'Mobile Money';
      case PaymentMethodCategory.banking:
        return 'Banking';
      case PaymentMethodCategory.cards:
        return 'Cards';
      case PaymentMethodCategory.digital:
        return 'Digital Wallets';
      case PaymentMethodCategory.crypto:
        return 'Cryptocurrency';
      case PaymentMethodCategory.other:
        return 'Other';
    }
  }

  IconData get icon {
    switch (this) {
      case PaymentMethodCategory.traditional:
        return Icons.money_rounded;
      case PaymentMethodCategory.mobileMoney:
        return Icons.phone_android_rounded;
      case PaymentMethodCategory.banking:
        return Icons.account_balance_rounded;
      case PaymentMethodCategory.cards:
        return Icons.credit_card_rounded;
      case PaymentMethodCategory.digital:
        return Icons.account_balance_wallet_rounded;
      case PaymentMethodCategory.crypto:
        return Icons.currency_bitcoin_rounded;
      case PaymentMethodCategory.other:
        return Icons.more_horiz_rounded;
    }
  }
}
