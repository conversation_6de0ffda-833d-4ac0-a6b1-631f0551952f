class AppStrings {
  // App Info
  static const String appName = 'Rekodi';
  static const String appTagline = 'Your Financial Companion';
  static const String appDescription = 'Manage your personal and business finances with ease';
  
  // Onboarding
  static const String onboardingTitle1 = 'Track Your Finances';
  static const String onboardingDesc1 = 'Monitor your income, expenses, and savings all in one place';
  
  static const String onboardingTitle2 = 'Manage Inventory';
  static const String onboardingDesc2 = 'Keep track of your business inventory and stock levels';
  
  static const String onboardingTitle3 = 'Generate Reports';
  static const String onboardingDesc3 = 'Get detailed financial reports and insights for better decisions';
  
  static const String onboardingTitle4 = 'Secure & Private';
  static const String onboardingDesc4 = 'Your financial data is encrypted and stored securely';
  
  // Authentication
  static const String welcomeBack = 'Welcome Back!';
  static const String signInToContinue = 'Sign in to continue to your account';
  static const String createAccount = 'Create Account';
  static const String joinUs = 'Join us and start managing your finances';
  static const String email = 'Email';
  static const String password = 'Password';
  static const String confirmPassword = 'Confirm Password';
  static const String fullName = 'Full Name';
  static const String phoneNumber = 'Phone Number';
  static const String signIn = 'Sign In';
  static const String signUp = 'Sign Up';
  static const String forgotPassword = 'Forgot Password?';
  static const String rememberMe = 'Remember Me';
  static const String orContinueWith = 'Or continue with';
  static const String google = 'Google';
  static const String apple = 'Apple';
  static const String dontHaveAccount = "Don't have an account?";
  static const String alreadyHaveAccount = 'Already have an account?';
  
  // Validation Messages
  static const String emailRequired = 'Email is required';
  static const String emailInvalid = 'Please enter a valid email';
  static const String passwordRequired = 'Password is required';
  static const String passwordTooShort = 'Password must be at least 6 characters';
  static const String passwordsNotMatch = 'Passwords do not match';
  static const String nameRequired = 'Full name is required';
  static const String phoneRequired = 'Phone number is required';
  
  // Common
  static const String next = 'Next';
  static const String skip = 'Skip';
  static const String getStarted = 'Get Started';
  static const String continueText = 'Continue';
  static const String cancel = 'Cancel';
  static const String save = 'Save';
  static const String delete = 'Delete';
  static const String edit = 'Edit';
  static const String add = 'Add';
  static const String search = 'Search';
  static const String filter = 'Filter';
  static const String sort = 'Sort';
  static const String loading = 'Loading...';
  static const String error = 'Error';
  static const String success = 'Success';
  static const String warning = 'Warning';
  static const String info = 'Info';
  
  // Financial Terms
  static const String income = 'Income';
  static const String expense = 'Expense';
  static const String savings = 'Savings';
  static const String investment = 'Investment';
  static const String budget = 'Budget';
  static const String category = 'Category';
  static const String amount = 'Amount';
  static const String date = 'Date';
  static const String description = 'Description';
  static const String balance = 'Balance';
  static const String totalIncome = 'Total Income';
  static const String totalExpense = 'Total Expense';
  static const String netIncome = 'Net Income';
  static const String monthlyBudget = 'Monthly Budget';
  static const String yearlyBudget = 'Yearly Budget';
  
  // Business Terms
  static const String inventory = 'Inventory';
  static const String product = 'Product';
  static const String stock = 'Stock';
  static const String supplier = 'Supplier';
  static const String customer = 'Customer';
  static const String invoice = 'Invoice';
  static const String receipt = 'Receipt';
  static const String profit = 'Profit';
  static const String loss = 'Loss';
  static const String revenue = 'Revenue';
  static const String cost = 'Cost';
  static const String price = 'Price';
  static const String quantity = 'Quantity';
  static const String unit = 'Unit';
  static const String sku = 'SKU';
  static const String barcode = 'Barcode';
}
