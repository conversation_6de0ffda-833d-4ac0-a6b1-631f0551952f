import 'package:equatable/equatable.dart';
import 'business_product.dart';

enum BusinessTransactionType {
  sale,
  purchase,
  adjustment, // For inventory adjustments
  return_sale,
  return_purchase,
}

extension BusinessTransactionTypeExtension on BusinessTransactionType {
  String get displayName {
    switch (this) {
      case BusinessTransactionType.sale:
        return 'Sale';
      case BusinessTransactionType.purchase:
        return 'Purchase';
      case BusinessTransactionType.adjustment:
        return 'Adjustment';
      case BusinessTransactionType.return_sale:
        return 'Sale Return';
      case BusinessTransactionType.return_purchase:
        return 'Purchase Return';
    }
  }

  String get value {
    switch (this) {
      case BusinessTransactionType.sale:
        return 'sale';
      case BusinessTransactionType.purchase:
        return 'purchase';
      case BusinessTransactionType.adjustment:
        return 'adjustment';
      case BusinessTransactionType.return_sale:
        return 'return_sale';
      case BusinessTransactionType.return_purchase:
        return 'return_purchase';
    }
  }

  static BusinessTransactionType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'sale':
        return BusinessTransactionType.sale;
      case 'purchase':
        return BusinessTransactionType.purchase;
      case 'adjustment':
        return BusinessTransactionType.adjustment;
      case 'return_sale':
        return BusinessTransactionType.return_sale;
      case 'return_purchase':
        return BusinessTransactionType.return_purchase;
      default:
        return BusinessTransactionType.sale;
    }
  }

  bool get isIncome => this == BusinessTransactionType.sale || this == BusinessTransactionType.return_purchase;
  bool get isExpense => this == BusinessTransactionType.purchase || this == BusinessTransactionType.return_sale;
}

class BusinessTransaction extends Equatable {
  final String id;
  final String accountId;
  final String productId;
  final BusinessProduct product;
  final int quantity;
  final double unitPrice;
  final double totalAmount;
  final BusinessTransactionType type;
  final String? customerName;
  final String? customerContact;
  final String? customerEmail;
  final String? notes;
  final String? paymentMethod;
  final String? referenceNumber;
  final double? discountAmount;
  final double? taxAmount;
  final DateTime transactionDate;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isSynced;
  final String? appwriteId;

  const BusinessTransaction({
    required this.id,
    required this.accountId,
    required this.productId,
    required this.product,
    required this.quantity,
    required this.unitPrice,
    required this.totalAmount,
    required this.type,
    this.customerName,
    this.customerContact,
    this.customerEmail,
    this.notes,
    this.paymentMethod,
    this.referenceNumber,
    this.discountAmount,
    this.taxAmount,
    required this.transactionDate,
    required this.createdAt,
    required this.updatedAt,
    this.isSynced = false,
    this.appwriteId,
  });

  BusinessTransaction copyWith({
    String? id,
    String? accountId,
    String? productId,
    BusinessProduct? product,
    int? quantity,
    double? unitPrice,
    double? totalAmount,
    BusinessTransactionType? type,
    String? customerName,
    String? customerContact,
    String? customerEmail,
    String? notes,
    String? paymentMethod,
    String? referenceNumber,
    double? discountAmount,
    double? taxAmount,
    DateTime? transactionDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSynced,
    String? appwriteId,
  }) {
    return BusinessTransaction(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      productId: productId ?? this.productId,
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalAmount: totalAmount ?? this.totalAmount,
      type: type ?? this.type,
      customerName: customerName ?? this.customerName,
      customerContact: customerContact ?? this.customerContact,
      customerEmail: customerEmail ?? this.customerEmail,
      notes: notes ?? this.notes,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      discountAmount: discountAmount ?? this.discountAmount,
      taxAmount: taxAmount ?? this.taxAmount,
      transactionDate: transactionDate ?? this.transactionDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSynced: isSynced ?? this.isSynced,
      appwriteId: appwriteId ?? this.appwriteId,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'accountId': accountId,
      'productId': productId,
      'product': product.toJson(),
      'quantity': quantity,
      'unitPrice': unitPrice,
      'totalAmount': totalAmount,
      'type': type.value,
      'customerName': customerName,
      'customerContact': customerContact,
      'customerEmail': customerEmail,
      'notes': notes,
      'paymentMethod': paymentMethod,
      'referenceNumber': referenceNumber,
      'discountAmount': discountAmount,
      'taxAmount': taxAmount,
      'transactionDate': transactionDate.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isSynced': isSynced,
      'appwriteId': appwriteId,
    };
  }

  factory BusinessTransaction.fromJson(Map<String, dynamic> json) {
    return BusinessTransaction(
      id: json['id'],
      accountId: json['accountId'],
      productId: json['productId'],
      product: BusinessProduct.fromJson(json['product']),
      quantity: json['quantity'],
      unitPrice: json['unitPrice'].toDouble(),
      totalAmount: json['totalAmount'].toDouble(),
      type: BusinessTransactionTypeExtension.fromString(json['type']),
      customerName: json['customerName'],
      customerContact: json['customerContact'],
      customerEmail: json['customerEmail'],
      notes: json['notes'],
      paymentMethod: json['paymentMethod'],
      referenceNumber: json['referenceNumber'],
      discountAmount: json['discountAmount']?.toDouble(),
      taxAmount: json['taxAmount']?.toDouble(),
      transactionDate: DateTime.parse(json['transactionDate']),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      isSynced: json['isSynced'] ?? false,
      appwriteId: json['appwriteId'],
    );
  }

  // Calculate profit for sales (selling price - cost price)
  double get profit {
    if (type == BusinessTransactionType.sale) {
      final costPrice = product.costPrice ?? 0.0;
      return (unitPrice - costPrice) * quantity;
    }
    return 0.0;
  }

  // Calculate profit margin percentage
  double get profitMargin {
    if (type == BusinessTransactionType.sale && unitPrice > 0) {
      final costPrice = product.costPrice ?? 0.0;
      return ((unitPrice - costPrice) / unitPrice) * 100;
    }
    return 0.0;
  }

  // Get net amount after discounts and taxes
  double get netAmount {
    double net = totalAmount;
    if (discountAmount != null) net -= discountAmount!;
    if (taxAmount != null) net += taxAmount!;
    return net;
  }

  // Transaction type helpers
  bool get isSale => type == BusinessTransactionType.sale;
  bool get isPurchase => type == BusinessTransactionType.purchase;

  @override
  List<Object?> get props => [
        id,
        accountId,
        productId,
        quantity,
        unitPrice,
        totalAmount,
        type,
        customerName,
        customerContact,
        customerEmail,
        notes,
        paymentMethod,
        referenceNumber,
        discountAmount,
        taxAmount,
        transactionDate,
        createdAt,
        updatedAt,
        isSynced,
        appwriteId,
      ];
}

/// Summary class for business transaction analytics
class BusinessTransactionSummary {
  final double totalSales;
  final double totalPurchases;
  final double totalProfit;
  final int totalTransactions;
  final int salesCount;
  final int purchasesCount;
  final double averageOrderValue;
  final DateTime periodStart;
  final DateTime periodEnd;

  const BusinessTransactionSummary({
    required this.totalSales,
    required this.totalPurchases,
    required this.totalProfit,
    required this.totalTransactions,
    required this.salesCount,
    required this.purchasesCount,
    required this.averageOrderValue,
    required this.periodStart,
    required this.periodEnd,
  });

  double get netRevenue => totalSales - totalPurchases;
  double get profitMargin => totalSales > 0 ? (totalProfit / totalSales) * 100 : 0.0;
}
