enum AccountType {
  personal,
  business;

  String get displayName {
    switch (this) {
      case AccountType.personal:
        return 'Personal';
      case AccountType.business:
        return 'Business';
    }
  }

  String get description {
    switch (this) {
      case AccountType.personal:
        return 'Track your personal income, expenses, and financial goals';
      case AccountType.business:
        return 'Manage inventory, products, services, and business finances';
    }
  }

  String get value {
    switch (this) {
      case AccountType.personal:
        return 'personal';
      case AccountType.business:
        return 'business';
    }
  }

  static AccountType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'business':
        return AccountType.business;
      case 'personal':
      default:
        return AccountType.personal;
    }
  }
}
