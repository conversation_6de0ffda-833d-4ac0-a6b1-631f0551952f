class ScannedReceipt {
  final String id;
  final String imagePath;
  final String merchantName;
  final double totalAmount;
  final DateTime date;
  final List<ReceiptItem> items;
  final double confidence;
  final String rawText;
  final Map<String, dynamic> extractedData;

  ScannedReceipt({
    required this.id,
    required this.imagePath,
    required this.merchantName,
    required this.totalAmount,
    required this.date,
    required this.items,
    required this.confidence,
    required this.rawText,
    required this.extractedData,
  });

  factory ScannedReceipt.fromJson(Map<String, dynamic> json) {
    return ScannedReceipt(
      id: json['id'] ?? '',
      imagePath: json['imagePath'] ?? '',
      merchantName: json['merchantName'] ?? '',
      totalAmount: (json['totalAmount'] ?? 0.0).toDouble(),
      date: DateTime.parse(json['date'] ?? DateTime.now().toIso8601String()),
      items: (json['items'] as List<dynamic>?)
          ?.map((item) => ReceiptItem.fromJson(item))
          .toList() ?? [],
      confidence: (json['confidence'] ?? 0.0).toDouble(),
      rawText: json['rawText'] ?? '',
      extractedData: json['extractedData'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'imagePath': imagePath,
      'merchantName': merchantName,
      'totalAmount': totalAmount,
      'date': date.toIso8601String(),
      'items': items.map((item) => item.toJson()).toList(),
      'confidence': confidence,
      'rawText': rawText,
      'extractedData': extractedData,
    };
  }

  // Get subtotal (total - tax)
  double get subtotal {
    final tax = extractedData['tax'] ?? 0.0;
    return totalAmount - tax;
  }

  // Get tax amount
  double get tax => extractedData['tax'] ?? 0.0;

  // Get merchant address
  String get merchantAddress => extractedData['address'] ?? '';

  // Get merchant phone
  String get merchantPhone => extractedData['phone'] ?? '';

  // Get payment method
  String get paymentMethod => extractedData['payment_method'] ?? '';

  // Check if confidence is high enough
  bool get isHighConfidence => confidence >= 0.8;

  // Get formatted confidence percentage
  String get confidencePercentage => '${(confidence * 100).toInt()}%';

  // Get total number of items
  int get itemCount => items.length;

  // Get average item price
  double get averageItemPrice {
    if (items.isEmpty) return 0.0;
    final totalItemPrice = items.fold<double>(0.0, (sum, item) => sum + item.totalPrice);
    return totalItemPrice / items.length;
  }

  // Check if receipt has valid data
  bool get isValid {
    return merchantName.isNotEmpty && 
           totalAmount > 0 && 
           confidence > 0.5;
  }

  @override
  String toString() {
    return 'ScannedReceipt(id: $id, merchant: $merchantName, total: \$${totalAmount.toStringAsFixed(2)}, confidence: $confidencePercentage)';
  }
}

class ReceiptItem {
  final String name;
  final int quantity;
  final double price;
  final String? category;
  final String? description;

  ReceiptItem({
    required this.name,
    required this.quantity,
    required this.price,
    this.category,
    this.description,
  });

  factory ReceiptItem.fromJson(Map<String, dynamic> json) {
    return ReceiptItem(
      name: json['name'] ?? '',
      quantity: json['quantity'] ?? 1,
      price: (json['price'] ?? 0.0).toDouble(),
      category: json['category'],
      description: json['description'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'quantity': quantity,
      'price': price,
      'category': category,
      'description': description,
    };
  }

  // Get total price for this item (quantity * price)
  double get totalPrice => quantity * price;

  // Get formatted price
  String get formattedPrice => '\$${price.toStringAsFixed(2)}';

  // Get formatted total price
  String get formattedTotalPrice => '\$${totalPrice.toStringAsFixed(2)}';

  // Check if item has valid data
  bool get isValid => name.isNotEmpty && quantity > 0 && price >= 0;

  @override
  String toString() {
    return 'ReceiptItem(name: $name, qty: $quantity, price: $formattedPrice, total: $formattedTotalPrice)';
  }
}
