import 'transaction.dart';

class SmsPattern {
  final String id;
  final String name;
  final String sender;
  final String pattern;
  final TransactionType transactionType;
  final String category;
  final String description;
  
  // Group indices for regex capture groups
  final int amountGroup;
  final int? referenceGroup;
  final int? senderGroup;
  final int? recipientGroup;
  final int? merchantGroup;
  final int? locationGroup;
  final int? accountGroup;
  final int? balanceGroup;
  final int? dateGroup;
  final int? timeGroup;
  final int? descriptionGroup;
  final int? cardGroup;
  
  final double confidence;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isActive;
  final bool isCustom;

  SmsPattern({
    required this.id,
    required this.name,
    required this.sender,
    required this.pattern,
    required this.transactionType,
    required this.category,
    required this.description,
    required this.amountGroup,
    this.referenceGroup,
    this.senderGroup,
    this.recipientGroup,
    this.merchantGroup,
    this.locationGroup,
    this.accountGroup,
    this.balanceGroup,
    this.dateGroup,
    this.timeGroup,
    this.descriptionGroup,
    this.cardGroup,
    this.confidence = 0.8,
    DateTime? createdAt,
    this.updatedAt,
    this.isActive = true,
    this.isCustom = false,
  }) : createdAt = createdAt ?? DateTime.now();

  SmsPattern copyWith({
    String? id,
    String? name,
    String? sender,
    String? pattern,
    TransactionType? transactionType,
    String? category,
    String? description,
    int? amountGroup,
    int? referenceGroup,
    int? senderGroup,
    int? recipientGroup,
    int? merchantGroup,
    int? locationGroup,
    int? accountGroup,
    int? balanceGroup,
    int? dateGroup,
    int? timeGroup,
    int? descriptionGroup,
    int? cardGroup,
    double? confidence,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    bool? isCustom,
  }) {
    return SmsPattern(
      id: id ?? this.id,
      name: name ?? this.name,
      sender: sender ?? this.sender,
      pattern: pattern ?? this.pattern,
      transactionType: transactionType ?? this.transactionType,
      category: category ?? this.category,
      description: description ?? this.description,
      amountGroup: amountGroup ?? this.amountGroup,
      referenceGroup: referenceGroup ?? this.referenceGroup,
      senderGroup: senderGroup ?? this.senderGroup,
      recipientGroup: recipientGroup ?? this.recipientGroup,
      merchantGroup: merchantGroup ?? this.merchantGroup,
      locationGroup: locationGroup ?? this.locationGroup,
      accountGroup: accountGroup ?? this.accountGroup,
      balanceGroup: balanceGroup ?? this.balanceGroup,
      dateGroup: dateGroup ?? this.dateGroup,
      timeGroup: timeGroup ?? this.timeGroup,
      descriptionGroup: descriptionGroup ?? this.descriptionGroup,
      cardGroup: cardGroup ?? this.cardGroup,
      confidence: confidence ?? this.confidence,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      isActive: isActive ?? this.isActive,
      isCustom: isCustom ?? this.isCustom,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'sender': sender,
      'pattern': pattern,
      'transaction_type': transactionType.name,
      'category': category,
      'description': description,
      'amount_group': amountGroup,
      'reference_group': referenceGroup,
      'sender_group': senderGroup,
      'recipient_group': recipientGroup,
      'merchant_group': merchantGroup,
      'location_group': locationGroup,
      'account_group': accountGroup,
      'balance_group': balanceGroup,
      'date_group': dateGroup,
      'time_group': timeGroup,
      'description_group': descriptionGroup,
      'card_group': cardGroup,
      'confidence': confidence,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'is_active': isActive,
      'is_custom': isCustom,
    };
  }

  factory SmsPattern.fromJson(Map<String, dynamic> json) {
    return SmsPattern(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      sender: json['sender'] ?? '',
      pattern: json['pattern'] ?? '',
      transactionType: TransactionType.values.firstWhere(
        (type) => type.name == json['transaction_type'],
        orElse: () => TransactionType.expense,
      ),
      category: json['category'] ?? '',
      description: json['description'] ?? '',
      amountGroup: json['amount_group'] ?? 1,
      referenceGroup: json['reference_group'],
      senderGroup: json['sender_group'],
      recipientGroup: json['recipient_group'],
      merchantGroup: json['merchant_group'],
      locationGroup: json['location_group'],
      accountGroup: json['account_group'],
      balanceGroup: json['balance_group'],
      dateGroup: json['date_group'],
      timeGroup: json['time_group'],
      descriptionGroup: json['description_group'],
      cardGroup: json['card_group'],
      confidence: (json['confidence'] ?? 0.8).toDouble(),
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'])
          : DateTime.now(),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'])
          : null,
      isActive: json['is_active'] ?? true,
      isCustom: json['is_custom'] ?? false,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SmsPattern && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SmsPattern(id: $id, name: $name, sender: $sender, type: ${transactionType.name})';
  }

  // Helper methods
  bool get isIncomePattern => transactionType == TransactionType.income;
  bool get isExpensePattern => transactionType == TransactionType.expense;
  
  bool matchesSender(String senderName) {
    return sender.toLowerCase() == senderName.toLowerCase() ||
           senderName.toLowerCase().contains(sender.toLowerCase()) ||
           sender.toLowerCase().contains(senderName.toLowerCase());
  }

  bool matchesMessage(String message) {
    try {
      final regex = RegExp(pattern, caseSensitive: false);
      return regex.hasMatch(message);
    } catch (e) {
      return false;
    }
  }

  RegExpMatch? extractMatch(String message) {
    try {
      final regex = RegExp(pattern, caseSensitive: false);
      return regex.firstMatch(message);
    } catch (e) {
      return null;
    }
  }

  // Validation
  bool get isValid {
    if (id.isEmpty || name.isEmpty || sender.isEmpty || pattern.isEmpty) {
      return false;
    }
    
    if (category.isEmpty || description.isEmpty) {
      return false;
    }
    
    if (amountGroup < 1) {
      return false;
    }
    
    // Test if pattern is valid regex
    try {
      RegExp(pattern);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Get all capture groups used by this pattern
  List<int> get usedGroups {
    final groups = <int>[amountGroup];
    
    if (referenceGroup != null) groups.add(referenceGroup!);
    if (senderGroup != null) groups.add(senderGroup!);
    if (recipientGroup != null) groups.add(recipientGroup!);
    if (merchantGroup != null) groups.add(merchantGroup!);
    if (locationGroup != null) groups.add(locationGroup!);
    if (accountGroup != null) groups.add(accountGroup!);
    if (balanceGroup != null) groups.add(balanceGroup!);
    if (dateGroup != null) groups.add(dateGroup!);
    if (timeGroup != null) groups.add(timeGroup!);
    if (descriptionGroup != null) groups.add(descriptionGroup!);
    if (cardGroup != null) groups.add(cardGroup!);
    
    return groups..sort();
  }

  // Get pattern complexity score
  double get complexityScore {
    double score = 0.5; // Base score
    
    // Add points for each capture group
    score += usedGroups.length * 0.1;
    
    // Add points for special regex features
    if (pattern.contains(r'\d')) score += 0.1;
    if (pattern.contains(r'\w')) score += 0.1;
    if (pattern.contains(r'[A-Z]')) score += 0.1;
    if (pattern.contains(r'\.')) score += 0.05;
    if (pattern.contains(r'\+')) score += 0.05;
    if (pattern.contains(r'\?')) score += 0.05;
    
    return score.clamp(0.0, 1.0);
  }

  // Create a test pattern for validation
  static SmsPattern createTest({
    required String testMessage,
    required String expectedAmount,
    required TransactionType expectedType,
  }) {
    return SmsPattern(
      id: 'test_${DateTime.now().millisecondsSinceEpoch}',
      name: 'Test Pattern',
      sender: 'TEST',
      pattern: r'Test message with amount ([\d,]+\.?\d*)',
      transactionType: expectedType,
      category: 'Test',
      description: 'Test pattern for validation',
      amountGroup: 1,
      isCustom: true,
    );
  }
}
