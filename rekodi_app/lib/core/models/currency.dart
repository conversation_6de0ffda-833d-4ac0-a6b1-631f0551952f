class Currency {
  final String code;
  final String name;
  final String symbol;
  final String flag;
  final int decimalPlaces;
  final bool isPopular;
  final String? region;

  const Currency({
    required this.code,
    required this.name,
    required this.symbol,
    required this.flag,
    this.decimalPlaces = 2,
    this.isPopular = false,
    this.region,
  });

  factory Currency.fromJson(Map<String, dynamic> json) {
    return Currency(
      code: json['code'] ?? '',
      name: json['name'] ?? '',
      symbol: json['symbol'] ?? '',
      flag: json['flag'] ?? '',
      decimalPlaces: json['decimalPlaces'] ?? 2,
      isPopular: json['isPopular'] ?? false,
      region: json['region'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'name': name,
      'symbol': symbol,
      'flag': flag,
      'decimalPlaces': decimalPlaces,
      'isPopular': isPopular,
      'region': region,
    };
  }

  // Format amount with this currency
  String formatAmount(double amount, {bool showSymbol = true, bool showCode = false}) {
    final formattedAmount = amount.toStringAsFixed(decimalPlaces);
    
    if (!showSymbol && !showCode) {
      return formattedAmount;
    }
    
    if (showSymbol && showCode) {
      return '$symbol$formattedAmount $code';
    }
    
    if (showSymbol) {
      return '$symbol$formattedAmount';
    }
    
    return '$formattedAmount $code';
  }

  // Get display name with flag
  String get displayName => '$flag $name ($code)';

  // Get short display name
  String get shortDisplayName => '$flag $code';

  // Check if this is a major currency
  bool get isMajor => ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF'].contains(code);

  // Check if this is an African currency
  bool get isAfrican => ['KES', 'NGN', 'ZAR', 'EGP', 'GHS', 'UGX', 'TZS', 'MAD', 'DZD', 'TND'].contains(code);

  // Check if this is a cryptocurrency
  bool get isCrypto => ['BTC', 'ETH', 'USDT', 'BNB', 'ADA', 'DOT'].contains(code);

  @override
  String toString() {
    return 'Currency(code: $code, name: $name, symbol: $symbol)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Currency && other.code == code;
  }

  @override
  int get hashCode => code.hashCode;

  // Copy with method
  Currency copyWith({
    String? code,
    String? name,
    String? symbol,
    String? flag,
    int? decimalPlaces,
    bool? isPopular,
    String? region,
  }) {
    return Currency(
      code: code ?? this.code,
      name: name ?? this.name,
      symbol: symbol ?? this.symbol,
      flag: flag ?? this.flag,
      decimalPlaces: decimalPlaces ?? this.decimalPlaces,
      isPopular: isPopular ?? this.isPopular,
      region: region ?? this.region,
    );
  }
}

// Exchange rate model
class ExchangeRate {
  final String fromCurrency;
  final String toCurrency;
  final double rate;
  final DateTime timestamp;
  final String? source;

  const ExchangeRate({
    required this.fromCurrency,
    required this.toCurrency,
    required this.rate,
    required this.timestamp,
    this.source,
  });

  factory ExchangeRate.fromJson(Map<String, dynamic> json) {
    return ExchangeRate(
      fromCurrency: json['fromCurrency'] ?? '',
      toCurrency: json['toCurrency'] ?? '',
      rate: (json['rate'] ?? 0.0).toDouble(),
      timestamp: DateTime.parse(json['timestamp']),
      source: json['source'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fromCurrency': fromCurrency,
      'toCurrency': toCurrency,
      'rate': rate,
      'timestamp': timestamp.toIso8601String(),
      'source': source,
    };
  }

  // Convert amount using this exchange rate
  double convert(double amount) {
    return amount * rate;
  }

  // Get formatted rate
  String get formattedRate => '1 $fromCurrency = ${rate.toStringAsFixed(4)} $toCurrency';

  // Check if rate is fresh (less than 1 hour old)
  bool get isFresh => DateTime.now().difference(timestamp).inHours < 1;

  // Get age of the rate
  String get age {
    final difference = DateTime.now().difference(timestamp);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  @override
  String toString() {
    return 'ExchangeRate($fromCurrency -> $toCurrency: $rate)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ExchangeRate &&
        other.fromCurrency == fromCurrency &&
        other.toCurrency == toCurrency &&
        other.rate == rate;
  }

  @override
  int get hashCode => Object.hash(fromCurrency, toCurrency, rate);
}

// Currency conversion result
class CurrencyConversion {
  final double originalAmount;
  final String fromCurrency;
  final double convertedAmount;
  final String toCurrency;
  final double exchangeRate;
  final DateTime timestamp;

  const CurrencyConversion({
    required this.originalAmount,
    required this.fromCurrency,
    required this.convertedAmount,
    required this.toCurrency,
    required this.exchangeRate,
    required this.timestamp,
  });

  // Get formatted conversion string
  String get formattedConversion {
    return '${originalAmount.toStringAsFixed(2)} $fromCurrency = ${convertedAmount.toStringAsFixed(2)} $toCurrency';
  }

  // Get conversion rate string
  String get rateString {
    return '1 $fromCurrency = ${exchangeRate.toStringAsFixed(4)} $toCurrency';
  }

  @override
  String toString() {
    return 'CurrencyConversion($formattedConversion at rate $exchangeRate)';
  }
}

// Predefined popular currencies
class PopularCurrencies {
  static const List<Currency> all = [
    Currency(code: 'USD', name: 'US Dollar', symbol: '\$', flag: '🇺🇸', isPopular: true, region: 'North America'),
    Currency(code: 'EUR', name: 'Euro', symbol: '€', flag: '🇪🇺', isPopular: true, region: 'Europe'),
    Currency(code: 'GBP', name: 'British Pound', symbol: '£', flag: '🇬🇧', isPopular: true, region: 'Europe'),
    Currency(code: 'JPY', name: 'Japanese Yen', symbol: '¥', flag: '🇯🇵', isPopular: true, region: 'Asia'),
    Currency(code: 'CAD', name: 'Canadian Dollar', symbol: 'C\$', flag: '🇨🇦', isPopular: true, region: 'North America'),
    Currency(code: 'AUD', name: 'Australian Dollar', symbol: 'A\$', flag: '🇦🇺', isPopular: true, region: 'Oceania'),
    Currency(code: 'CHF', name: 'Swiss Franc', symbol: 'CHF', flag: '🇨🇭', isPopular: true, region: 'Europe'),
    Currency(code: 'CNY', name: 'Chinese Yuan', symbol: '¥', flag: '🇨🇳', isPopular: true, region: 'Asia'),
    Currency(code: 'KES', name: 'Kenyan Shilling', symbol: 'KSh', flag: '🇰🇪', isPopular: true, region: 'Africa'),
    Currency(code: 'NGN', name: 'Nigerian Naira', symbol: '₦', flag: '🇳🇬', isPopular: true, region: 'Africa'),
  ];

  static const List<Currency> african = [
    Currency(code: 'KES', name: 'Kenyan Shilling', symbol: 'KSh', flag: '🇰🇪', region: 'Africa'),
    Currency(code: 'NGN', name: 'Nigerian Naira', symbol: '₦', flag: '🇳🇬', region: 'Africa'),
    Currency(code: 'ZAR', name: 'South African Rand', symbol: 'R', flag: '🇿🇦', region: 'Africa'),
    Currency(code: 'EGP', name: 'Egyptian Pound', symbol: 'E£', flag: '🇪🇬', region: 'Africa'),
    Currency(code: 'GHS', name: 'Ghanaian Cedi', symbol: '₵', flag: '🇬🇭', region: 'Africa'),
    Currency(code: 'UGX', name: 'Ugandan Shilling', symbol: 'USh', flag: '🇺🇬', region: 'Africa'),
    Currency(code: 'TZS', name: 'Tanzanian Shilling', symbol: 'TSh', flag: '🇹🇿', region: 'Africa'),
    Currency(code: 'MAD', name: 'Moroccan Dirham', symbol: 'MAD', flag: '🇲🇦', region: 'Africa'),
    Currency(code: 'DZD', name: 'Algerian Dinar', symbol: 'DZD', flag: '🇩🇿', region: 'Africa'),
    Currency(code: 'TND', name: 'Tunisian Dinar', symbol: 'TND', flag: '🇹🇳', region: 'Africa'),
  ];

  static const List<Currency> major = [
    Currency(code: 'USD', name: 'US Dollar', symbol: '\$', flag: '🇺🇸'),
    Currency(code: 'EUR', name: 'Euro', symbol: '€', flag: '🇪🇺'),
    Currency(code: 'GBP', name: 'British Pound', symbol: '£', flag: '🇬🇧'),
    Currency(code: 'JPY', name: 'Japanese Yen', symbol: '¥', flag: '🇯🇵'),
    Currency(code: 'CAD', name: 'Canadian Dollar', symbol: 'C\$', flag: '🇨🇦'),
    Currency(code: 'AUD', name: 'Australian Dollar', symbol: 'A\$', flag: '🇦🇺'),
    Currency(code: 'CHF', name: 'Swiss Franc', symbol: 'CHF', flag: '🇨🇭'),
    Currency(code: 'CNY', name: 'Chinese Yuan', symbol: '¥', flag: '🇨🇳'),
  ];
}
