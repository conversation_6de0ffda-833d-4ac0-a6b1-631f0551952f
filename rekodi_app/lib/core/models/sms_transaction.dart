import 'package:uuid/uuid.dart';

/// SMS Transaction Model
/// Represents a transaction parsed from SMS messages (M-Pesa, bank notifications, etc.)
class SmsTransaction {
  final String id;
  final String rawMessage;
  final String sender;
  final DateTime receivedAt;
  final double? amount;
  final String? transactionType; // 'income', 'expense', 'transfer'
  final String? merchantName;
  final String? referenceNumber;
  final String? accountNumber;
  final double? balance;
  final String? currency;
  final Map<String, dynamic> parsedData;
  final double confidenceScore; // 0.0 to 1.0
  bool isProcessed;
  bool isUserConfirmed;
  final String? assignedCategory;
  final String? userId;
  final DateTime createdAt;
  final DateTime? processedAt;
  final String? description;
  final String? category;

  SmsTransaction({
    required this.id,
    required this.rawMessage,
    required this.sender,
    required this.receivedAt,
    this.amount,
    this.transactionType,
    this.merchantName,
    this.referenceNumber,
    this.accountNumber,
    this.balance,
    this.currency,
    this.parsedData = const {},
    this.confidenceScore = 0.0,
    this.isProcessed = false,
    this.isUserConfirmed = false,
    this.assignedCategory,
    this.userId,
    required this.createdAt,
    this.processedAt,
    this.description,
    this.category,
  });

  factory SmsTransaction.fromRawSms({
    required String rawMessage,
    required String sender,
    required DateTime receivedAt,
    String? userId,
    String? description,
    String? category,
  }) {
    return SmsTransaction(
      id: const Uuid().v4(),
      rawMessage: rawMessage,
      sender: sender,
      receivedAt: receivedAt,
      userId: userId,
      createdAt: DateTime.now(),
      description: description,
      category: category,
    );
  }

  factory SmsTransaction.fromJson(Map<String, dynamic> json) {
    return SmsTransaction(
      id: json['id'] ?? '',
      rawMessage: json['rawMessage'] ?? '',
      sender: json['sender'] ?? '',
      receivedAt: DateTime.parse(json['receivedAt']),
      amount: json['amount']?.toDouble(),
      transactionType: json['transactionType'],
      merchantName: json['merchantName'],
      referenceNumber: json['referenceNumber'],
      accountNumber: json['accountNumber'],
      balance: json['balance']?.toDouble(),
      currency: json['currency'],
      parsedData: Map<String, dynamic>.from(json['parsedData'] ?? {}),
      confidenceScore: (json['confidenceScore'] ?? 0.0).toDouble(),
      isProcessed: json['isProcessed'] ?? false,
      isUserConfirmed: json['isUserConfirmed'] ?? false,
      assignedCategory: json['assignedCategory'],
      userId: json['userId'],
      createdAt: DateTime.parse(json['createdAt']),
      processedAt: json['processedAt'] != null 
          ? DateTime.parse(json['processedAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'rawMessage': rawMessage,
      'sender': sender,
      'receivedAt': receivedAt.toIso8601String(),
      'amount': amount,
      'transactionType': transactionType,
      'merchantName': merchantName,
      'referenceNumber': referenceNumber,
      'accountNumber': accountNumber,
      'balance': balance,
      'currency': currency,
      'parsedData': parsedData,
      'confidenceScore': confidenceScore,
      'isProcessed': isProcessed,
      'isUserConfirmed': isUserConfirmed,
      'assignedCategory': assignedCategory,
      'userId': userId,
      'createdAt': createdAt.toIso8601String(),
      'processedAt': processedAt?.toIso8601String(),
    };
  }

  SmsTransaction copyWith({
    String? id,
    String? rawMessage,
    String? sender,
    DateTime? receivedAt,
    double? amount,
    String? transactionType,
    String? merchantName,
    String? referenceNumber,
    String? accountNumber,
    double? balance,
    String? currency,
    Map<String, dynamic>? parsedData,
    double? confidenceScore,
    bool? isProcessed,
    bool? isUserConfirmed,
    String? assignedCategory,
    String? userId,
    DateTime? createdAt,
    DateTime? processedAt,
  }) {
    return SmsTransaction(
      id: id ?? this.id,
      rawMessage: rawMessage ?? this.rawMessage,
      sender: sender ?? this.sender,
      receivedAt: receivedAt ?? this.receivedAt,
      amount: amount ?? this.amount,
      transactionType: transactionType ?? this.transactionType,
      merchantName: merchantName ?? this.merchantName,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      accountNumber: accountNumber ?? this.accountNumber,
      balance: balance ?? this.balance,
      currency: currency ?? this.currency,
      parsedData: parsedData ?? this.parsedData,
      confidenceScore: confidenceScore ?? this.confidenceScore,
      isProcessed: isProcessed ?? this.isProcessed,
      isUserConfirmed: isUserConfirmed ?? this.isUserConfirmed,
      assignedCategory: assignedCategory ?? this.assignedCategory,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
      processedAt: processedAt ?? this.processedAt,
    );
  }

  /// Check if this SMS transaction has enough data to be converted to a financial transaction
  bool get isValidForTransaction {
    return amount != null && 
           amount! > 0 && 
           transactionType != null && 
           confidenceScore >= 0.7;
  }

  /// Get display name for the transaction
  String get displayName {
    if (merchantName != null && merchantName!.isNotEmpty) {
      return merchantName!;
    }
    if (referenceNumber != null && referenceNumber!.isNotEmpty) {
      return 'Transaction $referenceNumber';
    }
    return 'SMS Transaction';
  }

  /// Get formatted amount with currency
  String get formattedAmount {
    if (amount == null) return 'Unknown';
    final currencySymbol = _getCurrencySymbol(currency ?? 'KES');
    return '$currencySymbol${amount!.toStringAsFixed(2)}';
  }

  String _getCurrencySymbol(String currency) {
    switch (currency.toUpperCase()) {
      case 'KES':
        return 'KSh ';
      case 'USD':
        return '\$ ';
      case 'EUR':
        return '€ ';
      case 'GBP':
        return '£ ';
      default:
        return '$currency ';
    }
  }

  @override
  String toString() {
    return 'SmsTransaction(id: $id, sender: $sender, amount: $amount, type: $transactionType, confidence: $confidenceScore)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SmsTransaction && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}


