import 'package:equatable/equatable.dart';

enum ProductType {
  product,
  service,
}

enum ProductStatus {
  active,
  inactive,
  outOfStock,
}

class BusinessProduct extends Equatable {
  final String id;
  final String accountId;
  final String name;
  final String? description;
  final ProductType type;
  final double price; // Selling price
  final double? costPrice; // Cost/purchase price for profit calculations
  final String? currency;
  final int? quantity; // null for services
  final int? minStockLevel; // null for services
  final String? sku; // Stock Keeping Unit
  final String? barcode;
  final ProductStatus status;
  final String? category;
  final String? imageUrl;
  final Map<String, dynamic>? metadata; // Additional custom fields
  final DateTime createdAt;
  final DateTime updatedAt;

  const BusinessProduct({
    required this.id,
    required this.accountId,
    required this.name,
    this.description,
    required this.type,
    required this.price,
    this.costPrice,
    this.currency,
    this.quantity,
    this.minStockLevel,
    this.sku,
    this.barcode,
    required this.status,
    this.category,
    this.imageUrl,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
  });

  BusinessProduct copyWith({
    String? id,
    String? accountId,
    String? name,
    String? description,
    ProductType? type,
    double? price,
    double? costPrice,
    String? currency,
    int? quantity,
    int? minStockLevel,
    String? sku,
    String? barcode,
    ProductStatus? status,
    String? category,
    String? imageUrl,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BusinessProduct(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      price: price ?? this.price,
      costPrice: costPrice ?? this.costPrice,
      currency: currency ?? this.currency,
      quantity: quantity ?? this.quantity,
      minStockLevel: minStockLevel ?? this.minStockLevel,
      sku: sku ?? this.sku,
      barcode: barcode ?? this.barcode,
      status: status ?? this.status,
      category: category ?? this.category,
      imageUrl: imageUrl ?? this.imageUrl,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'accountId': accountId,
      'name': name,
      'description': description,
      'type': type.name,
      'price': price,
      'costPrice': costPrice,
      'currency': currency,
      'quantity': quantity,
      'minStockLevel': minStockLevel,
      'sku': sku,
      'barcode': barcode,
      'status': status.name,
      'category': category,
      'imageUrl': imageUrl,
      'metadata': metadata,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory BusinessProduct.fromJson(Map<String, dynamic> json) {
    return BusinessProduct(
      id: json['id'],
      accountId: json['accountId'],
      name: json['name'],
      description: json['description'],
      type: ProductType.values.firstWhere((e) => e.name == json['type']),
      price: json['price'].toDouble(),
      costPrice: json['costPrice']?.toDouble(),
      currency: json['currency'],
      quantity: json['quantity'],
      minStockLevel: json['minStockLevel'],
      sku: json['sku'],
      barcode: json['barcode'],
      status: ProductStatus.values.firstWhere((e) => e.name == json['status']),
      category: json['category'],
      imageUrl: json['imageUrl'],
      metadata: json['metadata'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  bool get isProduct => type == ProductType.product;
  bool get isService => type == ProductType.service;
  bool get isActive => status == ProductStatus.active;
  bool get isOutOfStock => status == ProductStatus.outOfStock;
  bool get isLowStock => quantity != null && minStockLevel != null && quantity! <= minStockLevel!;

  // Get actual cost price or default to 70% of selling price if not set
  double get actualCostPrice => costPrice ?? (price * 0.7);

  // Profit per unit
  double get profitPerUnit => price - actualCostPrice;

  // Profit margin percentage
  double get profitMarginPercentage => price > 0 ? ((profitPerUnit / price) * 100) : 0.0;

  // Total inventory value at cost price
  double get totalCostValue => (quantity ?? 0) * actualCostPrice;

  // Total inventory value at selling price
  double get totalSellingValue => (quantity ?? 0) * price;

  // Total potential profit from current inventory
  double get totalPotentialProfit => (quantity ?? 0) * profitPerUnit;

  // Unit for display purposes (defaults to 'units' for products, 'hours' for services)
  String get unit => isProduct ? 'units' : 'hours';

  @override
  List<Object?> get props => [
        id,
        accountId,
        name,
        description,
        type,
        price,
        costPrice,
        currency,
        quantity,
        minStockLevel,
        sku,
        barcode,
        status,
        category,
        imageUrl,
        metadata,
        createdAt,
        updatedAt,
      ];
}


