// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'financial_health.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FinancialHealth _$FinancialHealthFromJson(Map<String, dynamic> json) =>
    FinancialHealth(
      score: (json['score'] as num).toDouble(),
      grade: json['grade'] as String,
      factors: (json['factors'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      recommendations: (json['recommendations'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      lastCalculated: DateTime.parse(json['lastCalculated'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$FinancialHealthToJson(FinancialHealth instance) =>
    <String, dynamic>{
      'score': instance.score,
      'grade': instance.grade,
      'factors': instance.factors,
      'recommendations': instance.recommendations,
      'lastCalculated': instance.lastCalculated.toIso8601String(),
      'metadata': instance.metadata,
    };
