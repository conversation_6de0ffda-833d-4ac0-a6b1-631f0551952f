import 'package:flutter/material.dart';
import 'transaction.dart';

enum BudgetPeriod { 
  weekly, 
  monthly, 
  yearly;

  String get displayName {
    switch (this) {
      case BudgetPeriod.weekly:
        return 'Weekly';
      case BudgetPeriod.monthly:
        return 'Monthly';
      case BudgetPeriod.yearly:
        return 'Yearly';
    }
  }

  IconData get icon {
    switch (this) {
      case BudgetPeriod.weekly:
        return Icons.calendar_view_week_rounded;
      case BudgetPeriod.monthly:
        return Icons.calendar_view_month_rounded;
      case BudgetPeriod.yearly:
        return Icons.calendar_today_rounded;
    }
  }

  Duration get duration {
    switch (this) {
      case BudgetPeriod.weekly:
        return const Duration(days: 7);
      case BudgetPeriod.monthly:
        return const Duration(days: 30);
      case BudgetPeriod.yearly:
        return const Duration(days: 365);
    }
  }
}

class Budget {
  final String id;
  final String accountId;
  final TransactionCategory category;
  final String name;
  final double budgetAmount;
  final double spentAmount;
  final BudgetPeriod period;
  final DateTime startDate;
  final DateTime endDate;
  final bool isActive;
  final String? description;
  final List<String> tags;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Budget({
    required this.id,
    required this.accountId,
    required this.category,
    required this.name,
    required this.budgetAmount,
    required this.spentAmount,
    required this.period,
    required this.startDate,
    required this.endDate,
    this.isActive = true,
    this.description,
    this.tags = const [],
    required this.createdAt,
    this.updatedAt,
  });

  Budget copyWith({
    String? id,
    String? accountId,
    TransactionCategory? category,
    String? name,
    double? budgetAmount,
    double? spentAmount,
    BudgetPeriod? period,
    DateTime? startDate,
    DateTime? endDate,
    bool? isActive,
    String? description,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Budget(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      category: category ?? this.category,
      name: name ?? this.name,
      budgetAmount: budgetAmount ?? this.budgetAmount,
      spentAmount: spentAmount ?? this.spentAmount,
      period: period ?? this.period,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isActive: isActive ?? this.isActive,
      description: description ?? this.description,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'accountId': accountId,
      'category': category.name,
      'name': name,
      'budgetAmount': budgetAmount,
      'spentAmount': spentAmount,
      'period': period.name,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'isActive': isActive,
      'description': description,
      'tags': tags,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory Budget.fromJson(Map<String, dynamic> json) {
    return Budget(
      id: json['id'],
      accountId: json['accountId'],
      category: TransactionCategory.values.firstWhere((e) => e.name == json['category']),
      name: json['name'],
      budgetAmount: json['budgetAmount'].toDouble(),
      spentAmount: json['spentAmount'].toDouble(),
      period: BudgetPeriod.values.firstWhere((e) => e.name == json['period']),
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      isActive: json['isActive'] ?? true,
      description: json['description'],
      tags: List<String>.from(json['tags'] ?? []),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  // Helper methods
  double get progressPercentage {
    if (budgetAmount <= 0) return 0.0;
    return (spentAmount / budgetAmount).clamp(0.0, 1.0);
  }

  double get remainingAmount {
    return (budgetAmount - spentAmount).clamp(0.0, double.infinity);
  }

  bool get isOverBudget => spentAmount > budgetAmount;

  bool get isNearLimit => progressPercentage >= 0.8;

  BudgetStatus get status {
    if (isOverBudget) return BudgetStatus.overBudget;
    if (isNearLimit) return BudgetStatus.nearLimit;
    return BudgetStatus.onTrack;
  }

  String get statusMessage {
    switch (status) {
      case BudgetStatus.onTrack:
        return 'On track';
      case BudgetStatus.nearLimit:
        return 'Near limit';
      case BudgetStatus.overBudget:
        return 'Over budget';
    }
  }

  Color get statusColor {
    switch (status) {
      case BudgetStatus.onTrack:
        return Colors.green;
      case BudgetStatus.nearLimit:
        return Colors.orange;
      case BudgetStatus.overBudget:
        return Colors.red;
    }
  }

  bool get isCurrentPeriod {
    final now = DateTime.now();
    return now.isAfter(startDate) && now.isBefore(endDate);
  }

  Duration get timeRemaining {
    final now = DateTime.now();
    if (now.isAfter(endDate)) return Duration.zero;
    return endDate.difference(now);
  }

  String get timeRemainingText {
    final remaining = timeRemaining;
    if (remaining == Duration.zero) return 'Expired';
    
    if (remaining.inDays > 0) {
      return '${remaining.inDays} days left';
    } else if (remaining.inHours > 0) {
      return '${remaining.inHours} hours left';
    } else {
      return '${remaining.inMinutes} minutes left';
    }
  }

  // Calculate next period dates
  static DateTimeRange calculatePeriodDates(BudgetPeriod period, DateTime startDate) {
    switch (period) {
      case BudgetPeriod.weekly:
        final endDate = startDate.add(const Duration(days: 7));
        return DateTimeRange(start: startDate, end: endDate);
      case BudgetPeriod.monthly:
        final endDate = DateTime(startDate.year, startDate.month + 1, startDate.day);
        return DateTimeRange(start: startDate, end: endDate);
      case BudgetPeriod.yearly:
        final endDate = DateTime(startDate.year + 1, startDate.month, startDate.day);
        return DateTimeRange(start: startDate, end: endDate);
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Budget &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Budget{id: $id, name: $name, amount: $budgetAmount, spent: $spentAmount, period: $period}';
  }
}

enum BudgetStatus {
  onTrack,
  nearLimit,
  overBudget,
}

// Budget analytics model
class BudgetAnalytics {
  final String budgetId;
  final double averageSpending;
  final double projectedSpending;
  final List<DailySpending> dailySpending;
  final Map<String, double> categoryBreakdown;
  final DateTime calculatedAt;

  BudgetAnalytics({
    required this.budgetId,
    required this.averageSpending,
    required this.projectedSpending,
    required this.dailySpending,
    required this.categoryBreakdown,
    required this.calculatedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'budgetId': budgetId,
      'averageSpending': averageSpending,
      'projectedSpending': projectedSpending,
      'dailySpending': dailySpending.map((e) => e.toJson()).toList(),
      'categoryBreakdown': categoryBreakdown,
      'calculatedAt': calculatedAt.toIso8601String(),
    };
  }

  factory BudgetAnalytics.fromJson(Map<String, dynamic> json) {
    return BudgetAnalytics(
      budgetId: json['budgetId'],
      averageSpending: json['averageSpending'].toDouble(),
      projectedSpending: json['projectedSpending'].toDouble(),
      dailySpending: (json['dailySpending'] as List)
          .map((e) => DailySpending.fromJson(e))
          .toList(),
      categoryBreakdown: Map<String, double>.from(json['categoryBreakdown']),
      calculatedAt: DateTime.parse(json['calculatedAt']),
    );
  }
}

class DailySpending {
  final DateTime date;
  final double amount;

  DailySpending({
    required this.date,
    required this.amount,
  });

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'amount': amount,
    };
  }

  factory DailySpending.fromJson(Map<String, dynamic> json) {
    return DailySpending(
      date: DateTime.parse(json['date']),
      amount: json['amount'].toDouble(),
    );
  }
}
