// Simple user model for the Rekodi app

class AppUser {
  final String id;
  final String name;
  final String email;
  final bool emailVerification;
  final String? phone;
  final bool phoneVerification;
  final List<String> labels;
  final DateTime registration;
  final DateTime? passwordUpdate;
  final String status;
  final Map<String, dynamic>? prefs;
  
  AppUser({
    required this.id,
    required this.name,
    required this.email,
    required this.emailVerification,
    this.phone,
    required this.phoneVerification,
    required this.labels,
    required this.registration,
    this.passwordUpdate,
    required this.status,
    this.prefs,
  });
  
  // Create AppUser with default values
  factory AppUser.create({
    required String id,
    required String name,
    required String email,
  }) {
    return AppUser(
      id: id,
      name: name,
      email: email,
      emailVerification: false,
      phoneVerification: false,
      labels: [],
      registration: DateTime.now(),
      status: 'active',
    );
  }
  
  // Convert to JSON for local storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'emailVerification': emailVerification,
      'phone': phone,
      'phoneVerification': phoneVerification,
      'labels': labels,
      'registration': registration.toIso8601String(),
      'passwordUpdate': passwordUpdate?.toIso8601String(),
      'status': status,
      'prefs': prefs,
    };
  }
  
  // Create AppUser from JSON
  factory AppUser.fromJson(Map<String, dynamic> json) {
    return AppUser(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      emailVerification: json['emailVerification'] ?? false,
      phone: json['phone'],
      phoneVerification: json['phoneVerification'] ?? false,
      labels: List<String>.from(json['labels'] ?? []),
      registration: DateTime.parse(json['registration']),
      passwordUpdate: json['passwordUpdate'] != null 
          ? DateTime.parse(json['passwordUpdate']) 
          : null,
      status: json['status'] ?? 'unverified',
      prefs: json['prefs'],
    );
  }
  
  // Helper getters
  bool get isVerified => emailVerification;
  bool get isActive => status == 'true' || status == 'active';
  String get displayName => name.isNotEmpty ? name : email.split('@').first;
  String get initials {
    if (name.isNotEmpty) {
      final parts = name.split(' ');
      if (parts.length >= 2) {
        return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
      } else {
        return name.substring(0, 1).toUpperCase();
      }
    }
    return email.substring(0, 1).toUpperCase();
  }
  
  // Copy with method for updates
  AppUser copyWith({
    String? id,
    String? name,
    String? email,
    bool? emailVerification,
    String? phone,
    bool? phoneVerification,
    List<String>? labels,
    DateTime? registration,
    DateTime? passwordUpdate,
    String? status,
    Map<String, dynamic>? prefs,
  }) {
    return AppUser(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      emailVerification: emailVerification ?? this.emailVerification,
      phone: phone ?? this.phone,
      phoneVerification: phoneVerification ?? this.phoneVerification,
      labels: labels ?? this.labels,
      registration: registration ?? this.registration,
      passwordUpdate: passwordUpdate ?? this.passwordUpdate,
      status: status ?? this.status,
      prefs: prefs ?? this.prefs,
    );
  }
  
  @override
  String toString() {
    return 'AppUser(id: $id, name: $name, email: $email, verified: $emailVerification)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppUser && other.id == id;
  }
  
  @override
  int get hashCode => id.hashCode;
}
