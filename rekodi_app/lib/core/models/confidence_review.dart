import 'package:flutter/material.dart';
import 'transaction.dart';
import 'sms_transaction.dart';
import 'classification_rule.dart';

enum ReviewStatus {
  pending,
  reviewed,
  autoAccepted,
  autoRejected,
  expired,
}

enum ReviewAction {
  accept,
  reject,
  modify,
  skip,
}

enum ConfidenceLevel {
  veryLow,    // 0.0 - 0.3
  low,        // 0.3 - 0.5
  medium,     // 0.5 - 0.7
  high,       // 0.7 - 0.9
  veryHigh,   // 0.9 - 1.0
}

class ConfidenceReview {
  final String id;
  final String transactionId;
  final Transaction? originalTransaction;
  final SmsTransaction? originalSmsTransaction;
  final TransactionClassification classification;
  final ConfidenceLevel confidenceLevel;
  final DateTime createdAt;
  final ReviewStatus status;
  final ReviewAction? reviewAction;
  final DateTime? reviewedAt;
  final String? correctedCategory;
  final TransactionType? correctedType;
  final double? correctedAmount;
  final String? reviewNotes;
  final Map<String, dynamic>? metadata;

  const ConfidenceReview({
    required this.id,
    required this.transactionId,
    this.originalTransaction,
    this.originalSmsTransaction,
    required this.classification,
    required this.confidenceLevel,
    required this.createdAt,
    required this.status,
    this.reviewAction,
    this.reviewedAt,
    this.correctedCategory,
    this.correctedType,
    this.correctedAmount,
    this.reviewNotes,
    this.metadata,
  });

  ConfidenceReview copyWith({
    String? id,
    String? transactionId,
    Transaction? originalTransaction,
    SmsTransaction? originalSmsTransaction,
    TransactionClassification? classification,
    ConfidenceLevel? confidenceLevel,
    DateTime? createdAt,
    ReviewStatus? status,
    ReviewAction? reviewAction,
    DateTime? reviewedAt,
    String? correctedCategory,
    TransactionType? correctedType,
    double? correctedAmount,
    String? reviewNotes,
    Map<String, dynamic>? metadata,
  }) {
    return ConfidenceReview(
      id: id ?? this.id,
      transactionId: transactionId ?? this.transactionId,
      originalTransaction: originalTransaction ?? this.originalTransaction,
      originalSmsTransaction: originalSmsTransaction ?? this.originalSmsTransaction,
      classification: classification ?? this.classification,
      confidenceLevel: confidenceLevel ?? this.confidenceLevel,
      createdAt: createdAt ?? this.createdAt,
      status: status ?? this.status,
      reviewAction: reviewAction ?? this.reviewAction,
      reviewedAt: reviewedAt ?? this.reviewedAt,
      correctedCategory: correctedCategory ?? this.correctedCategory,
      correctedType: correctedType ?? this.correctedType,
      correctedAmount: correctedAmount ?? this.correctedAmount,
      reviewNotes: reviewNotes ?? this.reviewNotes,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'transaction_id': transactionId,
      'original_transaction': originalTransaction?.toJson(),
      'original_sms_transaction': originalSmsTransaction?.toJson(),
      'classification': classification.toJson(),
      'confidence_level': confidenceLevel.name,
      'created_at': createdAt.toIso8601String(),
      'status': status.name,
      'review_action': reviewAction?.name,
      'reviewed_at': reviewedAt?.toIso8601String(),
      'corrected_category': correctedCategory,
      'corrected_type': correctedType?.name,
      'corrected_amount': correctedAmount,
      'review_notes': reviewNotes,
      'metadata': metadata,
    };
  }

  factory ConfidenceReview.fromJson(Map<String, dynamic> json) {
    return ConfidenceReview(
      id: json['id'] ?? '',
      transactionId: json['transaction_id'] ?? '',
      originalTransaction: json['original_transaction'] != null
          ? Transaction.fromJson(json['original_transaction'])
          : null,
      originalSmsTransaction: json['original_sms_transaction'] != null
          ? SmsTransaction.fromJson(json['original_sms_transaction'])
          : null,
      classification: TransactionClassification.fromJson(json['classification'] ?? {}),
      confidenceLevel: ConfidenceLevel.values.firstWhere(
        (level) => level.name == json['confidence_level'],
        orElse: () => ConfidenceLevel.medium,
      ),
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : DateTime.now(),
      status: ReviewStatus.values.firstWhere(
        (status) => status.name == json['status'],
        orElse: () => ReviewStatus.pending,
      ),
      reviewAction: json['review_action'] != null
          ? ReviewAction.values.firstWhere(
              (action) => action.name == json['review_action'],
              orElse: () => ReviewAction.skip,
            )
          : null,
      reviewedAt: json['reviewed_at'] != null
          ? DateTime.parse(json['reviewed_at'])
          : null,
      correctedCategory: json['corrected_category'],
      correctedType: json['corrected_type'] != null
          ? TransactionType.values.firstWhere(
              (type) => type.name == json['corrected_type'],
              orElse: () => TransactionType.expense,
            )
          : null,
      correctedAmount: json['corrected_amount']?.toDouble(),
      reviewNotes: json['review_notes'],
      metadata: json['metadata'],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ConfidenceReview && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ConfidenceReview(id: $id, confidence: ${classification.confidence}, status: ${status.name})';
  }

  // Helper methods
  bool get isPending => status == ReviewStatus.pending;
  bool get isReviewed => status == ReviewStatus.reviewed;
  bool get isAutoProcessed => status == ReviewStatus.autoAccepted || status == ReviewStatus.autoRejected;
  bool get isExpired => status == ReviewStatus.expired;
  
  bool get wasAccepted => reviewAction == ReviewAction.accept;
  bool get wasRejected => reviewAction == ReviewAction.reject;
  bool get wasModified => reviewAction == ReviewAction.modify;
  bool get wasSkipped => reviewAction == ReviewAction.skip;

  // Get display values
  String get displayAmount {
    final amount = correctedAmount ?? 
        originalTransaction?.amount ?? 
        originalSmsTransaction?.amount ?? 
        0.0;
    return 'KES ${amount.toStringAsFixed(2)}';
  }

  String get displayCategory {
    return correctedCategory ?? 
        classification.category;
  }

  TransactionType get displayType {
    return correctedType ?? 
        classification.suggestedType;
  }

  String get displayDescription {
    return originalTransaction?.description ??
        originalSmsTransaction?.rawMessage ??
        'Unknown transaction';
  }

  String get confidenceLevelDisplayName {
    switch (confidenceLevel) {
      case ConfidenceLevel.veryHigh:
        return 'Very High';
      case ConfidenceLevel.high:
        return 'High';
      case ConfidenceLevel.medium:
        return 'Medium';
      case ConfidenceLevel.low:
        return 'Low';
      case ConfidenceLevel.veryLow:
        return 'Very Low';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case ReviewStatus.pending:
        return 'Pending Review';
      case ReviewStatus.reviewed:
        return 'Reviewed';
      case ReviewStatus.autoAccepted:
        return 'Auto-Accepted';
      case ReviewStatus.autoRejected:
        return 'Auto-Rejected';
      case ReviewStatus.expired:
        return 'Expired';
    }
  }

  String get reviewActionDisplayName {
    if (reviewAction == null) return 'No Action';
    
    switch (reviewAction!) {
      case ReviewAction.accept:
        return 'Accepted';
      case ReviewAction.reject:
        return 'Rejected';
      case ReviewAction.modify:
        return 'Modified';
      case ReviewAction.skip:
        return 'Skipped';
    }
  }

  // Get confidence color
  Color get confidenceColor {
    switch (confidenceLevel) {
      case ConfidenceLevel.veryHigh:
        return ConfidenceColors.green;
      case ConfidenceLevel.high:
        return ConfidenceColors.lightGreen;
      case ConfidenceLevel.medium:
        return ConfidenceColors.orange;
      case ConfidenceLevel.low:
        return ConfidenceColors.deepOrange;
      case ConfidenceLevel.veryLow:
        return ConfidenceColors.red;
    }
  }

  // Get status color
  Color get statusColor {
    switch (status) {
      case ReviewStatus.pending:
        return ConfidenceColors.orange;
      case ReviewStatus.reviewed:
        return ConfidenceColors.green;
      case ReviewStatus.autoAccepted:
        return ConfidenceColors.blue;
      case ReviewStatus.autoRejected:
        return ConfidenceColors.grey;
      case ReviewStatus.expired:
        return ConfidenceColors.red;
    }
  }

  // Validation
  bool get isValid {
    if (id.isEmpty || transactionId.isEmpty) return false;
    if (originalTransaction == null && originalSmsTransaction == null) return false;
    return true;
  }

  // Get age in hours
  int get ageInHours {
    return DateTime.now().difference(createdAt).inHours;
  }

  // Check if review is urgent (older than 24 hours)
  bool get isUrgent => ageInHours > 24;

  // Get review priority (lower number = higher priority)
  int get priority {
    int basePriority = 0;
    
    // Lower confidence = higher priority
    switch (confidenceLevel) {
      case ConfidenceLevel.veryLow:
        basePriority = 1;
        break;
      case ConfidenceLevel.low:
        basePriority = 2;
        break;
      case ConfidenceLevel.medium:
        basePriority = 3;
        break;
      case ConfidenceLevel.high:
        basePriority = 4;
        break;
      case ConfidenceLevel.veryHigh:
        basePriority = 5;
        break;
    }
    
    // Older reviews get higher priority
    if (ageInHours > 48) basePriority -= 2;
    else if (ageInHours > 24) basePriority -= 1;
    
    return basePriority.clamp(1, 5);
  }
}

// Helper for creating colors
class ConfidenceColors {
  static const Color green = Color(0xFF4CAF50);
  static const Color lightGreen = Color(0xFF8BC34A);
  static const Color orange = Color(0xFFFF9800);
  static const Color deepOrange = Color(0xFFFF5722);
  static const Color red = Color(0xFFF44336);
  static const Color blue = Color(0xFF2196F3);
  static const Color grey = Color(0xFF9E9E9E);
}
