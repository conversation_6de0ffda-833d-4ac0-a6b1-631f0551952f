import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

enum ProjectionPeriod {
  monthly,
  quarterly,
  yearly;

  String get displayName {
    switch (this) {
      case ProjectionPeriod.monthly:
        return 'Monthly';
      case ProjectionPeriod.quarterly:
        return 'Quarterly';
      case ProjectionPeriod.yearly:
        return 'Yearly';
    }
  }

  IconData get icon {
    switch (this) {
      case ProjectionPeriod.monthly:
        return Icons.calendar_view_month_rounded;
      case ProjectionPeriod.quarterly:
        return Icons.calendar_view_week_rounded;
      case ProjectionPeriod.yearly:
        return Icons.calendar_today_rounded;
    }
  }

  Duration get duration {
    switch (this) {
      case ProjectionPeriod.monthly:
        return const Duration(days: 30);
      case ProjectionPeriod.quarterly:
        return const Duration(days: 90);
      case ProjectionPeriod.yearly:
        return const Duration(days: 365);
    }
  }

  int get months {
    switch (this) {
      case ProjectionPeriod.monthly:
        return 1;
      case ProjectionPeriod.quarterly:
        return 3;
      case ProjectionPeriod.yearly:
        return 12;
    }
  }
}

enum ProjectionStatus {
  onTrack,
  ahead,
  behind,
  atRisk;

  String get displayName {
    switch (this) {
      case ProjectionStatus.onTrack:
        return 'On Track';
      case ProjectionStatus.ahead:
        return 'Ahead of Target';
      case ProjectionStatus.behind:
        return 'Behind Target';
      case ProjectionStatus.atRisk:
        return 'At Risk';
    }
  }

  Color get color {
    switch (this) {
      case ProjectionStatus.onTrack:
        return Colors.green;
      case ProjectionStatus.ahead:
        return Colors.blue;
      case ProjectionStatus.behind:
        return Colors.orange;
      case ProjectionStatus.atRisk:
        return Colors.red;
    }
  }
}

class BusinessProjection extends Equatable {
  final String id;
  final String accountId;
  final String name;
  final String? description;
  final ProjectionPeriod period;
  final double targetSales;
  final double actualSales;
  final double targetProfit;
  final double actualProfit;
  final double targetExpenses;
  final double actualExpenses;
  final DateTime startDate;
  final DateTime endDate;
  final bool isActive;
  final Map<String, double> categoryTargets; // Category-wise sales targets
  final Map<String, double> categoryActuals; // Category-wise actual sales
  final DateTime createdAt;
  final DateTime? updatedAt;

  const BusinessProjection({
    required this.id,
    required this.accountId,
    required this.name,
    this.description,
    required this.period,
    required this.targetSales,
    this.actualSales = 0.0,
    required this.targetProfit,
    this.actualProfit = 0.0,
    required this.targetExpenses,
    this.actualExpenses = 0.0,
    required this.startDate,
    required this.endDate,
    this.isActive = true,
    this.categoryTargets = const {},
    this.categoryActuals = const {},
    required this.createdAt,
    this.updatedAt,
  });

  BusinessProjection copyWith({
    String? id,
    String? accountId,
    String? name,
    String? description,
    ProjectionPeriod? period,
    double? targetSales,
    double? actualSales,
    double? targetProfit,
    double? actualProfit,
    double? targetExpenses,
    double? actualExpenses,
    DateTime? startDate,
    DateTime? endDate,
    bool? isActive,
    Map<String, double>? categoryTargets,
    Map<String, double>? categoryActuals,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BusinessProjection(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      name: name ?? this.name,
      description: description ?? this.description,
      period: period ?? this.period,
      targetSales: targetSales ?? this.targetSales,
      actualSales: actualSales ?? this.actualSales,
      targetProfit: targetProfit ?? this.targetProfit,
      actualProfit: actualProfit ?? this.actualProfit,
      targetExpenses: targetExpenses ?? this.targetExpenses,
      actualExpenses: actualExpenses ?? this.actualExpenses,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isActive: isActive ?? this.isActive,
      categoryTargets: categoryTargets ?? this.categoryTargets,
      categoryActuals: categoryActuals ?? this.categoryActuals,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'accountId': accountId,
      'name': name,
      'description': description,
      'period': period.name,
      'targetSales': targetSales,
      'actualSales': actualSales,
      'targetProfit': targetProfit,
      'actualProfit': actualProfit,
      'targetExpenses': targetExpenses,
      'actualExpenses': actualExpenses,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'isActive': isActive,
      'categoryTargets': categoryTargets,
      'categoryActuals': categoryActuals,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory BusinessProjection.fromJson(Map<String, dynamic> json) {
    return BusinessProjection(
      id: json['id'],
      accountId: json['accountId'],
      name: json['name'],
      description: json['description'],
      period: ProjectionPeriod.values.firstWhere((e) => e.name == json['period']),
      targetSales: json['targetSales'].toDouble(),
      actualSales: json['actualSales']?.toDouble() ?? 0.0,
      targetProfit: json['targetProfit'].toDouble(),
      actualProfit: json['actualProfit']?.toDouble() ?? 0.0,
      targetExpenses: json['targetExpenses'].toDouble(),
      actualExpenses: json['actualExpenses']?.toDouble() ?? 0.0,
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      isActive: json['isActive'] ?? true,
      categoryTargets: Map<String, double>.from(json['categoryTargets'] ?? {}),
      categoryActuals: Map<String, double>.from(json['categoryActuals'] ?? {}),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  // Helper methods
  double get salesProgressPercentage {
    if (targetSales <= 0) return 0.0;
    return (actualSales / targetSales).clamp(0.0, 1.0);
  }

  double get profitProgressPercentage {
    if (targetProfit <= 0) return 0.0;
    return (actualProfit / targetProfit).clamp(0.0, 1.0);
  }

  double get expenseProgressPercentage {
    if (targetExpenses <= 0) return 0.0;
    return (actualExpenses / targetExpenses).clamp(0.0, 1.0);
  }

  double get remainingSales => (targetSales - actualSales).clamp(0.0, double.infinity);
  double get remainingProfit => (targetProfit - actualProfit).clamp(0.0, double.infinity);
  double get remainingExpenses => (targetExpenses - actualExpenses).clamp(0.0, double.infinity);

  bool get isSalesOnTrack => salesProgressPercentage >= 0.8;
  bool get isProfitOnTrack => profitProgressPercentage >= 0.8;
  bool get isExpenseOnTrack => expenseProgressPercentage <= 1.2;

  ProjectionStatus get overallStatus {
    if (isSalesOnTrack && isProfitOnTrack && isExpenseOnTrack) {
      return salesProgressPercentage > 1.0 ? ProjectionStatus.ahead : ProjectionStatus.onTrack;
    } else if (!isSalesOnTrack || !isProfitOnTrack) {
      return ProjectionStatus.behind;
    } else {
      return ProjectionStatus.atRisk;
    }
  }

  String get statusMessage {
    switch (overallStatus) {
      case ProjectionStatus.onTrack:
        return 'Meeting targets';
      case ProjectionStatus.ahead:
        return 'Exceeding targets';
      case ProjectionStatus.behind:
        return 'Below targets';
      case ProjectionStatus.atRisk:
        return 'Needs attention';
    }
  }

  @override
  List<Object?> get props => [
        id,
        accountId,
        name,
        description,
        period,
        targetSales,
        actualSales,
        targetProfit,
        actualProfit,
        targetExpenses,
        actualExpenses,
        startDate,
        endDate,
        isActive,
        categoryTargets,
        categoryActuals,
        createdAt,
        updatedAt,
      ];
}
