import 'transaction.dart';

enum ClassificationRuleType {
  income,
  expense,
}

enum ClassificationMethod {
  rules,
  patterns,
  frequency,
  senderMapping,
  manual,
}

class ClassificationRule {
  final String id;
  final String name;
  final ClassificationRuleType type;
  final List<ClassificationCondition> conditions;
  final String category;
  final double confidence;
  final bool isActive;
  final bool isLearned;
  final DateTime createdAt;
  final DateTime? updatedAt;

  ClassificationRule({
    required this.id,
    required this.name,
    required this.type,
    required this.conditions,
    required this.category,
    required this.confidence,
    this.isActive = true,
    this.isLearned = false,
    DateTime? createdAt,
    this.updatedAt,
  }) : createdAt = createdAt ?? DateTime.now();

  ClassificationRule copyWith({
    String? id,
    String? name,
    ClassificationRuleType? type,
    List<ClassificationCondition>? conditions,
    String? category,
    double? confidence,
    bool? isActive,
    bool? isLearned,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ClassificationRule(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      conditions: conditions ?? this.conditions,
      category: category ?? this.category,
      confidence: confidence ?? this.confidence,
      isActive: isActive ?? this.isActive,
      isLearned: isLearned ?? this.isLearned,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'conditions': conditions.map((c) => c.toJson()).toList(),
      'category': category,
      'confidence': confidence,
      'is_active': isActive,
      'is_learned': isLearned,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  factory ClassificationRule.fromJson(Map<String, dynamic> json) {
    return ClassificationRule(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      type: ClassificationRuleType.values.firstWhere(
        (type) => type.name == json['type'],
        orElse: () => ClassificationRuleType.expense,
      ),
      conditions: (json['conditions'] as List<dynamic>?)
          ?.map((c) => ClassificationCondition.fromJson(c))
          .toList() ?? [],
      category: json['category'] ?? '',
      confidence: (json['confidence'] ?? 0.5).toDouble(),
      isActive: json['is_active'] ?? true,
      isLearned: json['is_learned'] ?? false,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'])
          : DateTime.now(),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'])
          : null,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ClassificationRule && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ClassificationRule(id: $id, name: $name, category: $category, confidence: $confidence)';
  }

  // Helper methods
  bool get isIncomeRule => type == ClassificationRuleType.income;
  bool get isExpenseRule => type == ClassificationRuleType.expense;
  
  // Validation
  bool get isValid {
    if (id.isEmpty || name.isEmpty || category.isEmpty) return false;
    if (confidence < 0 || confidence > 1) return false;
    if (conditions.isEmpty) return false;
    return conditions.every((condition) => condition.isValid);
  }

  // Get rule complexity score
  double get complexityScore {
    double score = conditions.length * 0.2;
    score += conditions.map((c) => c.weight).fold(0.0, (sum, weight) => sum + weight) * 0.1;
    return score.clamp(0.0, 1.0);
  }
}

class ClassificationCondition {
  final String field;
  final String operator;
  final String value;
  final double weight;

  const ClassificationCondition({
    required this.field,
    required this.operator,
    required this.value,
    required this.weight,
  });

  ClassificationCondition copyWith({
    String? field,
    String? operator,
    String? value,
    double? weight,
  }) {
    return ClassificationCondition(
      field: field ?? this.field,
      operator: operator ?? this.operator,
      value: value ?? this.value,
      weight: weight ?? this.weight,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'field': field,
      'operator': operator,
      'value': value,
      'weight': weight,
    };
  }

  factory ClassificationCondition.fromJson(Map<String, dynamic> json) {
    return ClassificationCondition(
      field: json['field'] ?? '',
      operator: json['operator'] ?? '',
      value: json['value'] ?? '',
      weight: (json['weight'] ?? 0.5).toDouble(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ClassificationCondition &&
        other.field == field &&
        other.operator == operator &&
        other.value == value &&
        other.weight == weight;
  }

  @override
  int get hashCode {
    return Object.hash(field, operator, value, weight);
  }

  @override
  String toString() {
    return 'ClassificationCondition(field: $field, operator: $operator, value: $value, weight: $weight)';
  }

  // Validation
  bool get isValid {
    if (field.isEmpty || operator.isEmpty || value.isEmpty) return false;
    if (weight < 0 || weight > 1) return false;
    
    // Validate operator
    const validOperators = [
      'equals',
      'contains',
      'contains_any',
      'range',
      'greater_than',
      'less_than',
    ];
    
    return validOperators.contains(operator);
  }

  // Get supported fields
  static List<String> get supportedFields => [
    'amount',
    'description',
    'category',
    'type',
    'merchant',
    'sender',
    'reference',
    'location',
  ];

  // Get supported operators
  static List<String> get supportedOperators => [
    'equals',
    'contains',
    'contains_any',
    'range',
    'greater_than',
    'less_than',
  ];
}

class TransactionClassification {
  final String category;
  final double confidence;
  final ClassificationMethod method;
  final TransactionType suggestedType;
  final Map<String, dynamic>? metadata;

  const TransactionClassification({
    required this.category,
    required this.confidence,
    required this.method,
    required this.suggestedType,
    this.metadata,
  });

  TransactionClassification copyWith({
    String? category,
    double? confidence,
    ClassificationMethod? method,
    TransactionType? suggestedType,
    Map<String, dynamic>? metadata,
  }) {
    return TransactionClassification(
      category: category ?? this.category,
      confidence: confidence ?? this.confidence,
      method: method ?? this.method,
      suggestedType: suggestedType ?? this.suggestedType,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'category': category,
      'confidence': confidence,
      'method': method.name,
      'suggested_type': suggestedType.name,
      'metadata': metadata,
    };
  }

  factory TransactionClassification.fromJson(Map<String, dynamic> json) {
    return TransactionClassification(
      category: json['category'] ?? '',
      confidence: (json['confidence'] ?? 0.0).toDouble(),
      method: ClassificationMethod.values.firstWhere(
        (method) => method.name == json['method'],
        orElse: () => ClassificationMethod.manual,
      ),
      suggestedType: TransactionType.values.firstWhere(
        (type) => type.name == json['suggested_type'],
        orElse: () => TransactionType.expense,
      ),
      metadata: json['metadata'],
    );
  }

  @override
  String toString() {
    return 'TransactionClassification(category: $category, confidence: $confidence, method: ${method.name})';
  }

  // Helper methods
  bool get isHighConfidence => confidence >= 0.8;
  bool get isMediumConfidence => confidence >= 0.6 && confidence < 0.8;
  bool get isLowConfidence => confidence < 0.6;
  
  String get confidenceLevel {
    if (isHighConfidence) return 'High';
    if (isMediumConfidence) return 'Medium';
    return 'Low';
  }

  bool get requiresReview => confidence < 0.7;
  
  String get methodDisplayName {
    switch (method) {
      case ClassificationMethod.rules:
        return 'Rule-based';
      case ClassificationMethod.patterns:
        return 'Pattern matching';
      case ClassificationMethod.frequency:
        return 'Frequency analysis';
      case ClassificationMethod.senderMapping:
        return 'Sender mapping';
      case ClassificationMethod.manual:
        return 'Manual';
    }
  }
}
