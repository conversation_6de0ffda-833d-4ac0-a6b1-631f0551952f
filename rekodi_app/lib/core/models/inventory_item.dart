import 'package:flutter/material.dart';

enum InventoryCategory {
  electronics,
  clothing,
  food,
  books,
  furniture,
  tools,
  automotive,
  health,
  beauty,
  sports,
  toys,
  office,
  home,
  garden,
  other,
}

extension InventoryCategoryExtension on InventoryCategory {
  String get displayName {
    switch (this) {
      case InventoryCategory.electronics:
        return 'Electronics';
      case InventoryCategory.clothing:
        return 'Clothing';
      case InventoryCategory.food:
        return 'Food & Beverages';
      case InventoryCategory.books:
        return 'Books';
      case InventoryCategory.furniture:
        return 'Furniture';
      case InventoryCategory.tools:
        return 'Tools';
      case InventoryCategory.automotive:
        return 'Automotive';
      case InventoryCategory.health:
        return 'Health';
      case InventoryCategory.beauty:
        return 'Beauty';
      case InventoryCategory.sports:
        return 'Sports';
      case InventoryCategory.toys:
        return 'Toys';
      case InventoryCategory.office:
        return 'Office Supplies';
      case InventoryCategory.home:
        return 'Home & Kitchen';
      case InventoryCategory.garden:
        return 'Garden';
      case InventoryCategory.other:
        return 'Other';
    }
  }

  IconData get icon {
    switch (this) {
      case InventoryCategory.electronics:
        return Icons.devices_rounded;
      case InventoryCategory.clothing:
        return Icons.checkroom_rounded;
      case InventoryCategory.food:
        return Icons.restaurant_rounded;
      case InventoryCategory.books:
        return Icons.menu_book_rounded;
      case InventoryCategory.furniture:
        return Icons.chair_rounded;
      case InventoryCategory.tools:
        return Icons.build_rounded;
      case InventoryCategory.automotive:
        return Icons.directions_car_rounded;
      case InventoryCategory.health:
        return Icons.local_hospital_rounded;
      case InventoryCategory.beauty:
        return Icons.face_rounded;
      case InventoryCategory.sports:
        return Icons.sports_basketball_rounded;
      case InventoryCategory.toys:
        return Icons.toys_rounded;
      case InventoryCategory.office:
        return Icons.business_center_rounded;
      case InventoryCategory.home:
        return Icons.home_rounded;
      case InventoryCategory.garden:
        return Icons.local_florist_rounded;
      case InventoryCategory.other:
        return Icons.category_rounded;
    }
  }

  Color get color {
    switch (this) {
      case InventoryCategory.electronics:
        return Colors.blue;
      case InventoryCategory.clothing:
        return Colors.purple;
      case InventoryCategory.food:
        return Colors.orange;
      case InventoryCategory.books:
        return Colors.brown;
      case InventoryCategory.furniture:
        return Colors.teal;
      case InventoryCategory.tools:
        return Colors.grey;
      case InventoryCategory.automotive:
        return Colors.red;
      case InventoryCategory.health:
        return Colors.green;
      case InventoryCategory.beauty:
        return Colors.pink;
      case InventoryCategory.sports:
        return Colors.indigo;
      case InventoryCategory.toys:
        return Colors.yellow;
      case InventoryCategory.office:
        return Colors.blueGrey;
      case InventoryCategory.home:
        return Colors.cyan;
      case InventoryCategory.garden:
        return Colors.lightGreen;
      case InventoryCategory.other:
        return Colors.deepPurple;
    }
  }
}

enum StockStatus {
  inStock,
  lowStock,
  outOfStock,
}

extension StockStatusExtension on StockStatus {
  String get displayName {
    switch (this) {
      case StockStatus.inStock:
        return 'In Stock';
      case StockStatus.lowStock:
        return 'Low Stock';
      case StockStatus.outOfStock:
        return 'Out of Stock';
    }
  }

  Color get color {
    switch (this) {
      case StockStatus.inStock:
        return Colors.green;
      case StockStatus.lowStock:
        return Colors.orange;
      case StockStatus.outOfStock:
        return Colors.red;
    }
  }

  IconData get icon {
    switch (this) {
      case StockStatus.inStock:
        return Icons.check_circle_rounded;
      case StockStatus.lowStock:
        return Icons.warning_rounded;
      case StockStatus.outOfStock:
        return Icons.error_rounded;
    }
  }
}

class InventoryItem {
  final String id;
  final String name;
  final String? description;
  final String? sku;
  final String? barcode;
  final InventoryCategory category;
  final double costPrice;
  final double sellingPrice;
  final int quantity;
  final int minStockLevel;
  final String unit; // e.g., 'pcs', 'kg', 'liter'
  final String? supplier;
  final String? location;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? imageUrl;

  InventoryItem({
    required this.id,
    required this.name,
    this.description,
    this.sku,
    this.barcode,
    required this.category,
    required this.costPrice,
    required this.sellingPrice,
    required this.quantity,
    this.minStockLevel = 5,
    this.unit = 'pcs',
    this.supplier,
    this.location,
    required this.createdAt,
    this.updatedAt,
    this.imageUrl,
  });

  StockStatus get stockStatus {
    if (quantity <= 0) {
      return StockStatus.outOfStock;
    } else if (quantity <= minStockLevel) {
      return StockStatus.lowStock;
    } else {
      return StockStatus.inStock;
    }
  }

  double get totalValue => quantity * costPrice;
  double get potentialProfit => (sellingPrice - costPrice) * quantity;
  double get profitMargin => sellingPrice > 0 ? ((sellingPrice - costPrice) / sellingPrice) * 100 : 0;

  InventoryItem copyWith({
    String? id,
    String? name,
    String? description,
    String? sku,
    String? barcode,
    InventoryCategory? category,
    double? costPrice,
    double? sellingPrice,
    int? quantity,
    int? minStockLevel,
    String? unit,
    String? supplier,
    String? location,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? imageUrl,
  }) {
    return InventoryItem(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      sku: sku ?? this.sku,
      barcode: barcode ?? this.barcode,
      category: category ?? this.category,
      costPrice: costPrice ?? this.costPrice,
      sellingPrice: sellingPrice ?? this.sellingPrice,
      quantity: quantity ?? this.quantity,
      minStockLevel: minStockLevel ?? this.minStockLevel,
      unit: unit ?? this.unit,
      supplier: supplier ?? this.supplier,
      location: location ?? this.location,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'sku': sku,
      'barcode': barcode,
      'category': category.name,
      'costPrice': costPrice,
      'sellingPrice': sellingPrice,
      'quantity': quantity,
      'minStockLevel': minStockLevel,
      'unit': unit,
      'supplier': supplier,
      'location': location,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'imageUrl': imageUrl,
    };
  }

  factory InventoryItem.fromJson(Map<String, dynamic> json) {
    return InventoryItem(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      sku: json['sku'],
      barcode: json['barcode'],
      category: InventoryCategory.values.firstWhere((e) => e.name == json['category']),
      costPrice: json['costPrice'].toDouble(),
      sellingPrice: json['sellingPrice'].toDouble(),
      quantity: json['quantity'],
      minStockLevel: json['minStockLevel'] ?? 5,
      unit: json['unit'] ?? 'pcs',
      supplier: json['supplier'],
      location: json['location'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      imageUrl: json['imageUrl'],
    );
  }
}
