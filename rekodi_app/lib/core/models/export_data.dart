import 'package:json_annotation/json_annotation.dart';
import 'transaction.dart';
import 'budget.dart';

part 'export_data.g.dart';

@JsonSerializable()
class ExportData {
  final List<Transaction> transactions;
  final List<Budget> budgets;
  final String? accountId;
  final DateTime exportDate;
  final String version;
  final Map<String, dynamic>? metadata;

  const ExportData({
    required this.transactions,
    required this.budgets,
    this.accountId,
    required this.exportDate,
    required this.version,
    this.metadata,
  });

  factory ExportData.fromJson(Map<String, dynamic> json) =>
      _$ExportDataFromJson(json);

  Map<String, dynamic> toJson() => _$ExportDataToJson(this);

  ExportData copyWith({
    List<Transaction>? transactions,
    List<Budget>? budgets,
    String? accountId,
    DateTime? exportDate,
    String? version,
    Map<String, dynamic>? metadata,
  }) {
    return ExportData(
      transactions: transactions ?? this.transactions,
      budgets: budgets ?? this.budgets,
      accountId: accountId ?? this.accountId,
      exportDate: exportDate ?? this.exportDate,
      version: version ?? this.version,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Get export summary
  ExportSummary get summary {
    final totalIncome = transactions
        .where((t) => t.type == 'income')
        .fold(0.0, (sum, t) => sum + t.amount);
    
    final totalExpenses = transactions
        .where((t) => t.type == 'expense')
        .fold(0.0, (sum, t) => sum + t.amount);
    
    final categories = transactions.map((t) => t.category).toSet();
    
    final dateRange = transactions.isNotEmpty
        ? DateRange(
            start: transactions.map((t) => t.date).reduce((a, b) => a.isBefore(b) ? a : b),
            end: transactions.map((t) => t.date).reduce((a, b) => a.isAfter(b) ? a : b),
          )
        : null;

    return ExportSummary(
      transactionCount: transactions.length,
      budgetCount: budgets.length,
      totalIncome: totalIncome,
      totalExpenses: totalExpenses,
      netAmount: totalIncome - totalExpenses,
      categoryCount: categories.length,
      dateRange: dateRange,
      exportDate: exportDate,
      version: version,
    );
  }

  /// Validate export data integrity
  ValidationResult validate() {
    final errors = <String>[];
    final warnings = <String>[];

    // Check version compatibility
    if (version != '1.0') {
      warnings.add('Export version $version may not be fully compatible');
    }

    // Validate transactions
    for (int i = 0; i < transactions.length; i++) {
      final transaction = transactions[i];
      
      if (transaction.amount <= 0) {
        errors.add('Transaction ${i + 1}: Invalid amount (${transaction.amount})');
      }
      
      if (transaction.description?.isEmpty ?? true) {
        warnings.add('Transaction ${i + 1}: Empty description');
      }

      if (transaction.category.name.isEmpty) {
        errors.add('Transaction ${i + 1}: Missing category');
      }
    }

    // Validate budgets
    for (int i = 0; i < budgets.length; i++) {
      final budget = budgets[i];
      
      if (budget.budgetAmount <= 0) {
        errors.add('Budget ${i + 1}: Invalid amount (${budget.budgetAmount})');
      }

      if (budget.category.name.isEmpty) {
        errors.add('Budget ${i + 1}: Missing category');
      }
      
      if (budget.endDate != null && budget.startDate.isAfter(budget.endDate!)) {
        errors.add('Budget ${i + 1}: Start date is after end date');
      }
    }

    // Check for duplicate transaction IDs
    final transactionIds = transactions.map((t) => t.id).toList();
    final uniqueTransactionIds = transactionIds.toSet();
    if (transactionIds.length != uniqueTransactionIds.length) {
      errors.add('Duplicate transaction IDs found');
    }

    // Check for duplicate budget IDs
    final budgetIds = budgets.map((b) => b.id).toList();
    final uniqueBudgetIds = budgetIds.toSet();
    if (budgetIds.length != uniqueBudgetIds.length) {
      errors.add('Duplicate budget IDs found');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Get file size estimate in bytes
  int get estimatedSizeBytes {
    // Rough estimation based on JSON serialization
    final jsonString = toJson().toString();
    return jsonString.length;
  }

  /// Get human-readable file size
  String get estimatedSize {
    final bytes = estimatedSizeBytes;
    
    if (bytes < 1024) {
      return '${bytes}B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }

  @override
  String toString() {
    return 'ExportData{transactions: ${transactions.length}, budgets: ${budgets.length}, version: $version, exportDate: $exportDate}';
  }
}

/// Export summary information
class ExportSummary {
  final int transactionCount;
  final int budgetCount;
  final double totalIncome;
  final double totalExpenses;
  final double netAmount;
  final int categoryCount;
  final DateRange? dateRange;
  final DateTime exportDate;
  final String version;

  const ExportSummary({
    required this.transactionCount,
    required this.budgetCount,
    required this.totalIncome,
    required this.totalExpenses,
    required this.netAmount,
    required this.categoryCount,
    this.dateRange,
    required this.exportDate,
    required this.version,
  });

  /// Get formatted summary text
  String get formattedSummary {
    final buffer = StringBuffer();
    
    buffer.writeln('Export Summary');
    buffer.writeln('Generated: ${exportDate.toIso8601String().split('T')[0]}');
    buffer.writeln('Version: $version');
    buffer.writeln();
    
    buffer.writeln('Data Overview:');
    buffer.writeln('• Transactions: $transactionCount');
    buffer.writeln('• Budgets: $budgetCount');
    buffer.writeln('• Categories: $categoryCount');
    
    if (dateRange != null) {
      buffer.writeln('• Date Range: ${dateRange!.start.toIso8601String().split('T')[0]} to ${dateRange!.end.toIso8601String().split('T')[0]}');
    }
    
    buffer.writeln();
    buffer.writeln('Financial Summary:');
    buffer.writeln('• Total Income: KES ${totalIncome.toStringAsFixed(2)}');
    buffer.writeln('• Total Expenses: KES ${totalExpenses.toStringAsFixed(2)}');
    buffer.writeln('• Net Amount: KES ${netAmount.toStringAsFixed(2)}');
    
    return buffer.toString();
  }
}

/// Date range helper class
class DateRange {
  final DateTime start;
  final DateTime end;

  const DateRange({
    required this.start,
    required this.end,
  });

  /// Get duration in days
  int get durationInDays {
    return end.difference(start).inDays + 1;
  }

  /// Check if date is within range
  bool contains(DateTime date) {
    return (date.isAfter(start) || date.isAtSameMomentAs(start)) &&
           (date.isBefore(end) || date.isAtSameMomentAs(end));
  }

  @override
  String toString() {
    return '${start.toIso8601String().split('T')[0]} to ${end.toIso8601String().split('T')[0]}';
  }
}

/// Validation result for export data
class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const ValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  /// Check if there are any issues
  bool get hasIssues => errors.isNotEmpty || warnings.isNotEmpty;

  /// Get total issue count
  int get totalIssues => errors.length + warnings.length;

  /// Get formatted validation report
  String get report {
    final buffer = StringBuffer();
    
    buffer.writeln('Validation Report');
    buffer.writeln('Status: ${isValid ? 'VALID' : 'INVALID'}');
    buffer.writeln();
    
    if (errors.isNotEmpty) {
      buffer.writeln('Errors (${errors.length}):');
      for (int i = 0; i < errors.length; i++) {
        buffer.writeln('${i + 1}. ${errors[i]}');
      }
      buffer.writeln();
    }
    
    if (warnings.isNotEmpty) {
      buffer.writeln('Warnings (${warnings.length}):');
      for (int i = 0; i < warnings.length; i++) {
        buffer.writeln('${i + 1}. ${warnings[i]}');
      }
    }
    
    return buffer.toString();
  }

  @override
  String toString() {
    return 'ValidationResult{isValid: $isValid, errors: ${errors.length}, warnings: ${warnings.length}}';
  }
}
