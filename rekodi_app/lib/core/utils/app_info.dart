class AppInfo {
  static const String appName = 'Rekodi';
  static const String appVersion = '1.0.0';
  static const String buildNumber = '1';
  static const String packageName = 'com.rekodi.app';
  
  // App Store Information
  static const String appDescription = '''
Rekodi is a comprehensive financial management app designed to help you take control of your finances with ease and intelligence.

KEY FEATURES:
• Smart Transaction Tracking - Automatically categorize and track your income and expenses
• AI-Powered SMS Parsing - Automatically detect and record transactions from SMS notifications
• Multi-Account Support - Manage both personal and business finances in one app
• Advanced Budgeting - Set budgets with intelligent alerts and progress tracking
• Receipt Scanning - Scan receipts with OCR technology for instant transaction recording
• Analytics & Insights - Get detailed financial health reports and spending patterns
• Secure & Private - Bank-level encryption with biometric authentication
• Offline Sync - Work offline and sync when connected
• Export & Backup - Export data to CSV/PDF and create secure backups

PERFECT FOR:
• Individuals managing personal finances
• Small business owners tracking business expenses
• Anyone wanting to understand their spending patterns
• Users seeking automated financial tracking

SECURITY & PRIVACY:
• End-to-end encryption for all sensitive data
• Biometric authentication (fingerprint/face recognition)
• Local data storage with optional cloud sync
• No sharing of personal financial data

Start your journey to financial freedom with <PERSON>ko<PERSON> today!
''';

  static const String shortDescription = 'Smart financial management with AI-powered transaction tracking, budgeting, and analytics.';
  
  // Keywords for app store optimization
  static const List<String> keywords = [
    'finance',
    'budget',
    'expense tracker',
    'money management',
    'personal finance',
    'business finance',
    'SMS parsing',
    'receipt scanner',
    'financial analytics',
    'spending tracker',
    'income tracker',
    'financial health',
    'budget planner',
    'expense manager',
    'financial insights',
  ];
  
  // App Store Categories
  static const String primaryCategory = 'Finance';
  static const String secondaryCategory = 'Business';
  
  // Contact Information
  static const String supportEmail = '<EMAIL>';
  static const String websiteUrl = 'https://rekodi.app';
  static const String privacyPolicyUrl = 'https://rekodi.app/privacy';
  static const String termsOfServiceUrl = 'https://rekodi.app/terms';
  
  // Social Media
  static const String twitterHandle = '@RekodiApp';
  static const String facebookPage = 'RekodiApp';
  static const String linkedinPage = 'rekodi-app';
  
  // Developer Information
  static const String developerName = 'Rekodi Technologies';
  static const String developerEmail = '<EMAIL>';
  static const String developerWebsite = 'https://rekodi.app';
  
  // App Store Ratings
  static const String targetAudience = '17+'; // Financial apps typically 17+
  static const bool containsAds = false;
  static const bool hasInAppPurchases = true; // For premium features
  
  // Localization
  static const List<String> supportedLanguages = [
    'English',
    'Swahili', // For Kenyan market
  ];
  
  // App Store Screenshots (descriptions)
  static const List<String> screenshotDescriptions = [
    'Beautiful dashboard with balance overview and spending insights',
    'Smart transaction tracking with automatic categorization',
    'Advanced budgeting with progress visualization',
    'AI-powered SMS parsing for automatic transaction detection',
    'Receipt scanning with OCR technology',
    'Comprehensive analytics and financial health reports',
    'Multi-account support for personal and business finances',
    'Secure biometric authentication and data protection',
  ];
  
  // Feature Highlights for App Store
  static const List<String> featureHighlights = [
    '🤖 AI-Powered Transaction Detection',
    '📱 SMS Auto-Parsing',
    '📊 Advanced Analytics',
    '🔒 Bank-Level Security',
    '📸 Receipt Scanning',
    '💼 Multi-Account Support',
    '📈 Smart Budgeting',
    '☁️ Secure Cloud Sync',
  ];
  
  // What's New (for app updates)
  static const String whatsNew = '''
🎉 Welcome to Rekodi v1.0!

NEW FEATURES:
• AI-powered transaction detection from SMS
• Advanced receipt scanning with OCR
• Multi-account support for personal and business
• Comprehensive financial analytics
• Biometric security with encryption
• Offline sync capabilities
• Export and backup functionality

IMPROVEMENTS:
• Beautiful, intuitive user interface
• Smooth animations and transitions
• Enhanced performance and stability
• Comprehensive testing and optimization

Get started with smart financial management today!
''';
  
  // App Store Review Guidelines Compliance
  static const Map<String, String> complianceNotes = {
    'data_collection': 'App collects financial data locally with user consent',
    'permissions': 'SMS permission for transaction detection, Camera for receipt scanning, Biometric for security',
    'content_rating': 'Suitable for users 17+ due to financial content',
    'monetization': 'Freemium model with premium features via subscription',
    'privacy': 'Full privacy policy available, no data sharing with third parties',
  };
  
  // Minimum System Requirements
  static const Map<String, String> systemRequirements = {
    'ios_minimum': '12.0',
    'android_minimum': '21', // Android 5.0
    'ram_minimum': '2GB',
    'storage_minimum': '100MB',
    'internet': 'Optional (for sync and backup)',
  };
  
  // App Store Optimization
  static const Map<String, dynamic> asoData = {
    'title_variations': [
      'Rekodi - Smart Finance Manager',
      'Rekodi: AI Financial Tracker',
      'Rekodi - Personal & Business Finance',
    ],
    'subtitle_variations': [
      'AI-powered expense tracking & budgeting',
      'Smart financial management made simple',
      'Track, budget, and analyze your finances',
    ],
    'competitor_analysis': [
      'Mint',
      'YNAB',
      'PocketGuard',
      'Goodbudget',
      'Spendee',
    ],
  };
  
  // Release Notes Template
  static String generateReleaseNotes(String version, List<String> features, List<String> improvements, List<String> bugFixes) {
    final buffer = StringBuffer();
    
    buffer.writeln('🎉 Rekodi v$version');
    buffer.writeln();
    
    if (features.isNotEmpty) {
      buffer.writeln('NEW FEATURES:');
      for (final feature in features) {
        buffer.writeln('• $feature');
      }
      buffer.writeln();
    }
    
    if (improvements.isNotEmpty) {
      buffer.writeln('IMPROVEMENTS:');
      for (final improvement in improvements) {
        buffer.writeln('• $improvement');
      }
      buffer.writeln();
    }
    
    if (bugFixes.isNotEmpty) {
      buffer.writeln('BUG FIXES:');
      for (final fix in bugFixes) {
        buffer.writeln('• $fix');
      }
      buffer.writeln();
    }
    
    buffer.writeln('Thank you for using Rekodi! 💚');
    
    return buffer.toString();
  }
  
  // App Store Connect Metadata
  static const Map<String, dynamic> appStoreMetadata = {
    'promotional_text': 'Take control of your finances with AI-powered tracking and smart budgeting tools.',
    'marketing_url': websiteUrl,
    'support_url': '$websiteUrl/support',
    'copyright': '© 2024 Rekodi Technologies. All rights reserved.',
    'review_notes': 'Test account credentials available upon request. App uses SMS permission for transaction detection and camera for receipt scanning.',
  };
  
  // Google Play Store Metadata
  static const Map<String, dynamic> playStoreMetadata = {
    'short_description': shortDescription,
    'full_description': appDescription,
    'recent_changes': whatsNew,
    'content_rating': 'Everyone',
    'category': 'FINANCE',
    'tags': keywords,
  };
}
