import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:collection/collection.dart';
import 'dart:convert';
import '../models/recurring_transaction.dart';
import '../models/transaction.dart';
import '../models/account_type.dart';
import '../database/database.dart';
import 'account_service.dart';
import 'personal_transaction_service.dart';

class RecurringTransactionService extends GetxService {
  static RecurringTransactionService get to => Get.find();

  final GetStorage _storage = GetStorage();
  final RxList<RecurringTransaction> _recurringTransactions = <RecurringTransaction>[].obs;
  final RxBool _isLoading = false.obs;

  List<RecurringTransaction> get recurringTransactions => _recurringTransactions;
  bool get isLoading => _isLoading.value;

  @override
  void onInit() {
    super.onInit();
    _loadRecurringTransactions();
    
    // Listen to account changes
    ever(AccountService.to.currentAccountObs, (account) {
      _loadRecurringTransactions();
    });
  }

  /// Load recurring transactions for current account
  Future<void> _loadRecurringTransactions() async {
    final accountId = AccountService.to.currentAccountId;
    if (accountId.isEmpty) {
      _recurringTransactions.clear();
      return;
    }

    try {
      _isLoading.value = true;
      
      final key = 'recurring_transactions_$accountId';
      final data = _storage.read(key);
      
      if (data != null) {
        final List<dynamic> jsonList = jsonDecode(data);
        final transactions = jsonList
            .map((json) => RecurringTransaction.fromJson(json))
            .toList();
        
        _recurringTransactions.assignAll(transactions);
      } else {
        _recurringTransactions.clear();
      }
    } catch (e) {
      print('Error loading recurring transactions: $e');
      _recurringTransactions.clear();
    } finally {
      _isLoading.value = false;
    }
  }

  /// Save recurring transactions to storage
  Future<void> _saveRecurringTransactions() async {
    final accountId = AccountService.to.currentAccountId;
    if (accountId.isEmpty) return;

    try {
      final key = 'recurring_transactions_$accountId';
      final jsonList = _recurringTransactions
          .map((transaction) => transaction.toJson())
          .toList();
      
      await _storage.write(key, jsonEncode(jsonList));
    } catch (e) {
      print('Error saving recurring transactions: $e');
    }
  }

  /// Add a new recurring transaction
  Future<bool> addRecurringTransaction(RecurringTransaction transaction) async {
    try {
      // Ensure the transaction has a valid account ID
      final accountId = AccountService.to.currentAccountId;
      if (accountId.isEmpty) return false;

      // Create a new transaction with the current account ID if needed
      final recurringTransaction = transaction.accountId.isEmpty
          ? transaction.copyWith(accountId: accountId)
          : transaction;

      _recurringTransactions.add(recurringTransaction);
      await _saveRecurringTransactions();

      return true;
    } catch (e) {
      print('Error adding recurring transaction: $e');
      return false;
    }
  }

  /// Add a new recurring transaction with parameters (legacy method)
  Future<bool> addRecurringTransactionWithParams({
    required String title,
    String? description,
    required double amount,
    required String type,
    required String category,
    required RecurrenceType recurrenceType,
    required DateTime startDate,
    DateTime? endDate,
    String? paymentMethod,
    String? location,
    List<String>? tags,
  }) async {
    final accountId = AccountService.to.currentAccountId;
    if (accountId.isEmpty) return false;

    try {
      final id = 'recurring_${DateTime.now().millisecondsSinceEpoch}';
      final nextDueDate = recurrenceType.getNextDate(startDate);

      final recurringTransaction = RecurringTransaction(
        id: id,
        accountId: accountId,
        title: title,
        description: description,
        amount: amount,
        type: type,
        category: category,
        recurrenceType: recurrenceType,
        startDate: startDate,
        endDate: endDate,
        nextDueDate: nextDueDate,
        isActive: true,
        paymentMethod: paymentMethod,
        location: location,
        tags: tags,
        createdAt: DateTime.now(),
      );

      return await addRecurringTransaction(recurringTransaction);
    } catch (e) {
      print('Error adding recurring transaction: $e');
      return false;
    }
  }

  /// Update a recurring transaction
  Future<bool> updateRecurringTransaction(RecurringTransaction transaction) async {
    try {
      final index = _recurringTransactions.indexWhere((t) => t.id == transaction.id);
      if (index == -1) return false;

      final updated = transaction.copyWith(updatedAt: DateTime.now());
      _recurringTransactions[index] = updated;
      await _saveRecurringTransactions();

      return true;
    } catch (e) {
      print('Error updating recurring transaction: $e');
      return false;
    }
  }

  /// Update a recurring transaction with parameters (legacy method)
  Future<bool> updateRecurringTransactionWithParams(String id, {
    String? title,
    String? description,
    double? amount,
    String? type,
    String? category,
    RecurrenceType? recurrenceType,
    DateTime? startDate,
    DateTime? endDate,
    bool? isActive,
    String? paymentMethod,
    String? location,
    List<String>? tags,
  }) async {
    try {
      final index = _recurringTransactions.indexWhere((t) => t.id == id);
      if (index == -1) return false;

      final existing = _recurringTransactions[index];
      final updated = existing.copyWith(
        title: title,
        description: description,
        amount: amount,
        type: type,
        category: category,
        recurrenceType: recurrenceType,
        startDate: startDate,
        endDate: endDate,
        isActive: isActive,
        paymentMethod: paymentMethod,
        location: location,
        tags: tags,
        updatedAt: DateTime.now(),
      );

      return await updateRecurringTransaction(updated);
    } catch (e) {
      print('Error updating recurring transaction: $e');
      return false;
    }
  }

  /// Delete a recurring transaction
  Future<bool> deleteRecurringTransaction(String id) async {
    try {
      _recurringTransactions.removeWhere((t) => t.id == id);
      await _saveRecurringTransactions();
      return true;
    } catch (e) {
      print('Error deleting recurring transaction: $e');
      return false;
    }
  }

  /// Get due recurring transactions
  List<RecurringTransaction> getDueTransactions() {
    return _recurringTransactions
        .where((t) => t.shouldBeActive() && t.isDue())
        .toList();
  }

  /// Process due recurring transactions
  Future<int> processDueTransactions() async {
    final dueTransactions = getDueTransactions();
    int processedCount = 0;

    for (final recurringTransaction in dueTransactions) {
      try {
        // Create the actual transaction
        final success = await _createTransactionFromRecurring(recurringTransaction);
        
        if (success) {
          // Update the next due date
          final nextDueDate = recurringTransaction.getNextDueDate();
          
          // Update the recurring transaction with new next due date
          final index = _recurringTransactions.indexWhere((t) => t.id == recurringTransaction.id);
          if (index != -1) {
            _recurringTransactions[index] = recurringTransaction.copyWith(
              nextDueDate: nextDueDate,
              updatedAt: DateTime.now(),
            );
          }
          
          processedCount++;
        }
      } catch (e) {
        print('Error processing recurring transaction ${recurringTransaction.id}: $e');
      }
    }

    if (processedCount > 0) {
      await _saveRecurringTransactions();
    }

    return processedCount;
  }

  /// Process a specific recurring transaction manually
  Future<bool> processSpecificTransaction(String recurringTransactionId) async {
    try {
      final recurringTransaction = _recurringTransactions
          .firstWhereOrNull((t) => t.id == recurringTransactionId);

      if (recurringTransaction == null) {
        print('Recurring transaction not found: $recurringTransactionId');
        return false;
      }

      if (!recurringTransaction.shouldBeActive()) {
        print('Recurring transaction is not active: $recurringTransactionId');
        return false;
      }

      // Create the actual transaction
      final success = await _createTransactionFromRecurring(recurringTransaction);

      if (success) {
        // Update the next due date
        final nextDueDate = recurringTransaction.getNextDueDate();

        // Update the recurring transaction with new next due date
        final index = _recurringTransactions.indexWhere((t) => t.id == recurringTransaction.id);
        if (index != -1) {
          _recurringTransactions[index] = recurringTransaction.copyWith(
            nextDueDate: nextDueDate,
            updatedAt: DateTime.now(),
          );

          await _saveRecurringTransactions();
        }

        print('Successfully processed recurring transaction: ${recurringTransaction.title}');
        return true;
      } else {
        print('Failed to create transaction from recurring: ${recurringTransaction.title}');
        return false;
      }
    } catch (e) {
      print('Error processing specific recurring transaction: $e');
      return false;
    }
  }

  /// Create actual transaction from recurring transaction
  Future<bool> _createTransactionFromRecurring(RecurringTransaction recurring) async {
    try {
      // Convert type string to TransactionType enum
      TransactionType transactionType;
      switch (recurring.type.toLowerCase()) {
        case 'income':
        case 'sale':
          transactionType = TransactionType.income;
          break;
        case 'expense':
        case 'purchase':
          transactionType = TransactionType.expense;
          break;
        default:
          transactionType = TransactionType.expense;
      }

      // Convert category string to TransactionCategory enum
      TransactionCategory transactionCategory;
      try {
        transactionCategory = TransactionCategory.values
            .firstWhere((c) => c.name.toLowerCase() == recurring.category.toLowerCase());
      } catch (e) {
        // Default to other_income or other_expense based on type
        transactionCategory = transactionType == TransactionType.income 
            ? TransactionCategory.other_income 
            : TransactionCategory.other_expense;
      }

      // Enhance description to indicate it's from recurring transaction
      final enhancedDescription = recurring.description != null
          ? '${recurring.description} (Auto-generated from recurring transaction)'
          : 'Auto-generated from recurring transaction: ${recurring.title}';

      // Add recurring transaction ID to tags for tracking
      final enhancedTags = <String>[
        ...(recurring.tags ?? []),
        'recurring:${recurring.id}',
        'auto-generated',
      ];

      return await PersonalTransactionService.to.addTransaction(
        title: recurring.title,
        description: enhancedDescription,
        amount: recurring.amount,
        type: transactionType,
        category: transactionCategory,
        date: DateTime.now(), // Use current time for manual processing
        paymentMethod: recurring.paymentMethod,
        location: recurring.location,
        tags: enhancedTags,
        isRecurring: true,
        recurringPattern: recurring.recurrenceType.name,
        nextRecurringDate: recurring.getNextDueDate(),
      );
    } catch (e) {
      print('Error creating transaction from recurring: $e');
      return false;
    }
  }

  /// Get templates based on account type
  List<RecurringTransactionTemplate> getTemplates() {
    final accountType = AccountService.to.currentAccountType;
    
    if (accountType == AccountType.business) {
      return RecurringTransactionTemplate.businessTemplates;
    } else {
      return RecurringTransactionTemplate.personalTemplates;
    }
  }

  /// Create recurring transaction from template
  Future<bool> createFromTemplate(
    RecurringTransactionTemplate template, {
    required double amount,
    required DateTime startDate,
    DateTime? endDate,
    String? description,
  }) async {
    return await addRecurringTransactionWithParams(
      title: template.name,
      description: description ?? template.description,
      amount: amount,
      type: template.type,
      category: template.category,
      recurrenceType: template.recurrenceType,
      startDate: startDate,
      endDate: endDate,
    );
  }

  /// Get recurring transactions by type
  List<RecurringTransaction> getByType(String type) {
    return _recurringTransactions
        .where((t) => t.type.toLowerCase() == type.toLowerCase())
        .toList();
  }

  /// Get all transactions created from recurring transactions
  List<PersonalTransaction> getGeneratedTransactions() {
    try {
      final allTransactions = PersonalTransactionService.to.transactions;

      // Filter transactions that have recurring tags
      return allTransactions.where((transaction) {
        final tags = PersonalTransactionService.to.getTagsFromTransaction(transaction);
        return tags.any((tag) => tag.startsWith('recurring:')) ||
               transaction.isRecurring == true;
      }).toList();
    } catch (e) {
      print('Error getting generated transactions: $e');
      return [];
    }
  }

  /// Get transactions generated from a specific recurring transaction
  List<PersonalTransaction> getTransactionsFromRecurring(String recurringId) {
    try {
      final allTransactions = PersonalTransactionService.to.transactions;

      return allTransactions.where((transaction) {
        final tags = PersonalTransactionService.to.getTagsFromTransaction(transaction);
        return tags.contains('recurring:$recurringId');
      }).toList();
    } catch (e) {
      print('Error getting transactions from recurring: $e');
      return [];
    }
  }

  /// Get active recurring transactions
  List<RecurringTransaction> getActiveTransactions() {
    return _recurringTransactions
        .where((t) => t.shouldBeActive())
        .toList();
  }

  /// Clear all recurring transactions for current account
  Future<void> clearRecurringTransactions() async {
    _recurringTransactions.clear();
    await _saveRecurringTransactions();
  }
}
