import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../models/transaction_charge.dart';
import '../models/transaction.dart';
import 'account_service.dart';
import 'personal_transaction_service.dart';

class TransactionChargesService extends GetxService {
  static TransactionChargesService get to => Get.find();

  final GetStorage _storage = GetStorage();
  final RxList<TransactionCharge> _charges = <TransactionCharge>[].obs;
  final RxBool _isLoading = false.obs;
  final RxDouble _totalMonthlyCharges = 0.0.obs;
  final RxDouble _totalYearlyCharges = 0.0.obs;

  // Getters
  List<TransactionCharge> get charges => _charges;
  bool get isLoading => _isLoading.value;
  double get totalMonthlyCharges => _totalMonthlyCharges.value;
  double get totalYearlyCharges => _totalYearlyCharges.value;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _loadCharges();
    _calculateTotals();
  }

  /// Load charges from storage
  Future<void> _loadCharges() async {
    try {
      _isLoading.value = true;
      final accountId = AccountService.to.currentAccountId;
      if (accountId.isEmpty) return;

      final chargesData = _storage.read('transaction_charges_$accountId') as List?;
      if (chargesData != null) {
        _charges.assignAll(
          chargesData.map((data) => TransactionCharge.fromJson(data)).toList(),
        );
      }
    } catch (e) {
      print('Error loading transaction charges: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Save charges to storage
  Future<void> _saveCharges() async {
    try {
      final accountId = AccountService.to.currentAccountId;
      if (accountId.isEmpty) return;

      final chargesData = _charges.map((charge) => charge.toJson()).toList();
      await _storage.write('transaction_charges_$accountId', chargesData);
    } catch (e) {
      print('Error saving transaction charges: $e');
    }
  }

  /// Add a new transaction charge
  Future<bool> addCharge({
    required String name,
    String? description,
    required double amount,
    required ChargeType type,
    required ChargeFrequency frequency,
    String? provider,
    String? paymentMethod,
    String? parentTransactionId,
    DateTime? chargeDate,
    bool isRecurring = false,
    String? recurringPattern,
    DateTime? nextChargeDate,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final accountId = AccountService.to.currentAccountId;
      if (accountId.isEmpty) return false;

      final chargeId = 'charge_${DateTime.now().millisecondsSinceEpoch}';
      
      final charge = TransactionCharge(
        id: chargeId,
        accountId: accountId,
        parentTransactionId: parentTransactionId,
        name: name,
        description: description,
        amount: amount,
        type: type,
        frequency: frequency,
        provider: provider,
        paymentMethod: paymentMethod,
        chargeDate: chargeDate ?? DateTime.now(),
        isRecurring: isRecurring,
        recurringPattern: recurringPattern,
        nextChargeDate: nextChargeDate,
        metadata: metadata,
        createdAt: DateTime.now(),
      );

      _charges.add(charge);
      await _saveCharges();
      _calculateTotals();

      // If this is a charge that should be recorded as a transaction, create it
      if (frequency != ChargeFrequency.perTransaction) {
        await _createChargeTransaction(charge);
      }

      return true;
    } catch (e) {
      print('Error adding transaction charge: $e');
      return false;
    }
  }

  /// Update an existing charge
  Future<bool> updateCharge(TransactionCharge updatedCharge) async {
    try {
      final index = _charges.indexWhere((c) => c.id == updatedCharge.id);
      if (index == -1) return false;

      _charges[index] = updatedCharge.copyWith(updatedAt: DateTime.now());
      await _saveCharges();
      _calculateTotals();
      return true;
    } catch (e) {
      print('Error updating transaction charge: $e');
      return false;
    }
  }

  /// Delete a charge
  Future<bool> deleteCharge(String chargeId) async {
    try {
      _charges.removeWhere((c) => c.id == chargeId);
      await _saveCharges();
      _calculateTotals();
      return true;
    } catch (e) {
      print('Error deleting transaction charge: $e');
      return false;
    }
  }

  /// Get charges by type
  List<TransactionCharge> getChargesByType(ChargeType type) {
    return _charges.where((c) => c.type == type && c.isActive).toList();
  }

  /// Get charges by frequency
  List<TransactionCharge> getChargesByFrequency(ChargeFrequency frequency) {
    return _charges.where((c) => c.frequency == frequency && c.isActive).toList();
  }

  /// Get charges for a specific transaction
  List<TransactionCharge> getChargesForTransaction(String transactionId) {
    return _charges.where((c) => c.parentTransactionId == transactionId).toList();
  }

  /// Calculate total charges for a transaction amount and payment method
  double calculateTransactionCharges(double amount, String paymentMethod) {
    double totalCharges = 0.0;
    
    final perTransactionCharges = getChargesByFrequency(ChargeFrequency.perTransaction);
    
    for (final charge in perTransactionCharges) {
      if (charge.paymentMethod == null || charge.paymentMethod == paymentMethod) {
        // Apply charge based on amount or fixed fee
        if (charge.metadata != null && charge.metadata!['isPercentage'] == true) {
          final percentage = charge.metadata!['percentage'] as double? ?? 0.0;
          totalCharges += amount * (percentage / 100);
        } else {
          totalCharges += charge.amount;
        }
      }
    }
    
    return totalCharges;
  }

  /// Add charges to a transaction
  Future<void> addChargesToTransaction(String transactionId, double transactionAmount, String paymentMethod) async {
    final chargeAmount = calculateTransactionCharges(transactionAmount, paymentMethod);
    
    if (chargeAmount > 0) {
      await addCharge(
        name: 'Transaction Charges',
        description: 'Charges for transaction $transactionId',
        amount: chargeAmount,
        type: ChargeType.transactionFee,
        frequency: ChargeFrequency.oneTime,
        paymentMethod: paymentMethod,
        parentTransactionId: transactionId,
      );
    }
  }

  /// Process due recurring charges
  Future<void> processDueCharges() async {
    final dueCharges = _charges.where((c) => c.isDue).toList();
    
    for (final charge in dueCharges) {
      await _createChargeTransaction(charge);
      
      // Update next charge date
      if (charge.isRecurring && charge.nextChargeDate != null) {
        DateTime nextDate;
        switch (charge.frequency) {
          case ChargeFrequency.monthly:
            nextDate = DateTime(
              charge.nextChargeDate!.year,
              charge.nextChargeDate!.month + 1,
              charge.nextChargeDate!.day,
            );
            break;
          case ChargeFrequency.quarterly:
            nextDate = DateTime(
              charge.nextChargeDate!.year,
              charge.nextChargeDate!.month + 3,
              charge.nextChargeDate!.day,
            );
            break;
          case ChargeFrequency.yearly:
            nextDate = DateTime(
              charge.nextChargeDate!.year + 1,
              charge.nextChargeDate!.month,
              charge.nextChargeDate!.day,
            );
            break;
          default:
            continue;
        }
        
        await updateCharge(charge.copyWith(nextChargeDate: nextDate));
      }
    }
  }

  /// Create a transaction for a charge
  Future<void> _createChargeTransaction(TransactionCharge charge) async {
    await PersonalTransactionService.to.addTransaction(
      title: charge.name,
      description: charge.description ?? 'Automatic charge: ${charge.type.displayName}',
      amount: charge.amount,
      type: TransactionType.expense,
      category: _getTransactionCategoryForCharge(charge.type),
      date: charge.chargeDate,
      paymentMethod: charge.paymentMethod,
    );
  }

  /// Get appropriate transaction category for charge type
  TransactionCategory _getTransactionCategoryForCharge(ChargeType chargeType) {
    switch (chargeType) {
      case ChargeType.bankFee:
        return TransactionCategory.bank_fees;
      case ChargeType.serviceFee:
        return TransactionCategory.service_charges;
      case ChargeType.transactionFee:
      case ChargeType.processingFee:
      case ChargeType.withdrawalFee:
      case ChargeType.transferFee:
      case ChargeType.convenienceFee:
        return TransactionCategory.transaction_charges;
      case ChargeType.other:
        return TransactionCategory.other_expense;
    }
  }

  /// Calculate total charges
  void _calculateTotals() {
    double monthlyTotal = 0.0;
    double yearlyTotal = 0.0;
    
    for (final charge in _charges.where((c) => c.isActive)) {
      monthlyTotal += charge.monthlyAmount;
      yearlyTotal += charge.yearlyAmount;
    }
    
    _totalMonthlyCharges.value = monthlyTotal;
    _totalYearlyCharges.value = yearlyTotal;
  }

  /// Get charges summary
  Map<String, dynamic> getChargesSummary() {
    final activeCharges = _charges.where((c) => c.isActive).toList();
    
    return {
      'totalCharges': activeCharges.length,
      'monthlyTotal': totalMonthlyCharges,
      'yearlyTotal': totalYearlyCharges,
      'byType': ChargeType.values.map((type) => {
        'type': type.displayName,
        'count': activeCharges.where((c) => c.type == type).length,
        'total': activeCharges.where((c) => c.type == type).fold(0.0, (sum, c) => sum + c.amount),
      }).toList(),
      'byFrequency': ChargeFrequency.values.map((freq) => {
        'frequency': freq.displayName,
        'count': activeCharges.where((c) => c.frequency == freq).length,
        'total': activeCharges.where((c) => c.frequency == freq).fold(0.0, (sum, c) => sum + c.amount),
      }).toList(),
    };
  }

  /// Refresh charges data
  Future<void> refreshCharges() async {
    await _loadCharges();
    _calculateTotals();
  }
}
