import 'package:get/get.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'package:google_mlkit_barcode_scanning/google_mlkit_barcode_scanning.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'dart:convert';

class ReceiptScannerService extends GetxService {
  late TextRecognizer _textRecognizer;
  late BarcodeScanner _barcodeScanner;
  final ImagePicker _imagePicker = ImagePicker();

  final RxBool _isProcessing = false.obs;
  final RxList<Map<String, dynamic>> _scannedReceipts = <Map<String, dynamic>>[].obs;

  bool get isProcessing => _isProcessing.value;
  List<Map<String, dynamic>> get scannedReceipts => _scannedReceipts;

  @override
  void onInit() {
    super.onInit();
    _initializeServices();
  }

  void _initializeServices() {
    _textRecognizer = TextRecognizer(script: TextRecognitionScript.latin);
    _barcodeScanner = BarcodeScanner();
  }

  @override
  void onClose() {
    _textRecognizer.close();
    _barcodeScanner.close();
    super.onClose();
  }

  /// Process receipt image and extract transaction data
  Future<Map<String, dynamic>?> processReceiptImage(File imageFile) async {
    try {
      _isProcessing.value = true;

      final inputImage = InputImage.fromFile(imageFile);
      final recognizedText = await _textRecognizer.processImage(inputImage);

      if (recognizedText.text.isEmpty) {
        return null;
      }

      final extractedData = _extractReceiptData(recognizedText.text);
      
      // Store the scanned receipt
      final receiptData = {
        ...extractedData,
        'imagePath': imageFile.path,
        'scannedAt': DateTime.now().toIso8601String(),
        'rawText': recognizedText.text,
      };
      
      _scannedReceipts.add(receiptData);
      
      return extractedData;
    } catch (e) {
      print('Error processing receipt image: $e');
      return null;
    } finally {
      _isProcessing.value = false;
    }
  }

  /// Extract structured data from receipt text
  Map<String, dynamic> _extractReceiptData(String text) {
    final lines = text.split('\n').map((line) => line.trim()).where((line) => line.isNotEmpty).toList();
    
    Map<String, dynamic> data = {
      'amount': null,
      'merchant': null,
      'date': null,
      'items': <Map<String, dynamic>>[],
      'tax': null,
      'total': null,
      'paymentMethod': null,
    };

    // Extract amount/total
    data['amount'] = _extractAmount(lines);
    data['total'] = data['amount'];

    // Extract merchant name (usually first few lines)
    data['merchant'] = _extractMerchant(lines);

    // Extract date
    data['date'] = _extractDate(lines);

    // Extract items
    data['items'] = _extractItems(lines);

    // Extract tax
    data['tax'] = _extractTax(lines);

    // Extract payment method
    data['paymentMethod'] = _extractPaymentMethod(lines);

    return data;
  }

  /// Extract total amount from receipt text
  double? _extractAmount(List<String> lines) {
    final amountPatterns = [
      // Kenyan patterns
      RegExp(r'total[:\s]*ksh[:\s]*(\d+[.,]\d{2})', caseSensitive: false),
      RegExp(r'total[:\s]*(\d+[.,]\d{2})\s*ksh', caseSensitive: false),
      RegExp(r'total[:\s]*(\d+[.,]\d{2})', caseSensitive: false),
      RegExp(r'amount[:\s]*ksh[:\s]*(\d+[.,]\d{2})', caseSensitive: false),
      RegExp(r'amount[:\s]*(\d+[.,]\d{2})\s*ksh', caseSensitive: false),
      RegExp(r'amount[:\s]*(\d+[.,]\d{2})', caseSensitive: false),

      // M-Pesa patterns
      RegExp(r'paid[:\s]*ksh[:\s]*(\d+[.,]\d{2})', caseSensitive: false),
      RegExp(r'paid[:\s]*(\d+[.,]\d{2})', caseSensitive: false),

      // General patterns
      RegExp(r'(\d+[.,]\d{2})\s*total', caseSensitive: false),
      RegExp(r'ksh[:\s]*(\d+[.,]\d{2})', caseSensitive: false),
      RegExp(r'(\d+[.,]\d{2})\s*ksh', caseSensitive: false),
      RegExp(r'(\d+[.,]\d{2})$'), // Amount at end of line

      // International patterns
      RegExp(r'usd[:\s]*(\d+[.,]\d{2})', caseSensitive: false),
      RegExp(r'\$[:\s]*(\d+[.,]\d{2})', caseSensitive: false),
      RegExp(r'(\d+[.,]\d{2})\s*usd', caseSensitive: false),
    ];

    // First pass: look for explicit total/amount keywords
    for (final line in lines.reversed) {
      for (final pattern in amountPatterns.take(10)) { // First 10 patterns are more specific
        final match = pattern.firstMatch(line.toLowerCase());
        if (match != null) {
          final amountStr = match.group(1)?.replaceAll(',', '.');
          final amount = double.tryParse(amountStr ?? '');
          if (amount != null && amount > 0) {
            return amount;
          }
        }
      }
    }

    // Second pass: look for currency patterns
    for (final line in lines.reversed) {
      for (final pattern in amountPatterns.skip(10)) {
        final match = pattern.firstMatch(line.toLowerCase());
        if (match != null) {
          final amountStr = match.group(1)?.replaceAll(',', '.');
          final amount = double.tryParse(amountStr ?? '');
          if (amount != null && amount > 0) {
            return amount;
          }
        }
      }
    }

    // Fallback: look for any number that looks like money (larger amounts first)
    final allAmounts = <double>[];
    for (final line in lines) {
      final numbers = RegExp(r'\d+[.,]\d{2}').allMatches(line);
      for (final match in numbers) {
        final amountStr = match.group(0)?.replaceAll(',', '.');
        final amount = double.tryParse(amountStr ?? '');
        if (amount != null && amount >= 1.0) {
          allAmounts.add(amount);
        }
      }
    }

    // Return the largest amount found (likely to be the total)
    if (allAmounts.isNotEmpty) {
      allAmounts.sort((a, b) => b.compareTo(a));
      return allAmounts.first;
    }

    return null;
  }

  /// Extract merchant name from receipt text
  String? _extractMerchant(List<String> lines) {
    if (lines.isEmpty) return null;

    // Skip common receipt headers and look for merchant name
    final skipPatterns = [
      RegExp(r'receipt', caseSensitive: false),
      RegExp(r'invoice', caseSensitive: false),
      RegExp(r'bill', caseSensitive: false),
      RegExp(r'tax', caseSensitive: false),
      RegExp(r'vat', caseSensitive: false),
      RegExp(r'^\d+$'), // Pure numbers
      RegExp(r'^\s*$'), // Empty lines
    ];

    // Look for merchant name in first 8 lines
    for (int i = 0; i < (lines.length > 8 ? 8 : lines.length); i++) {
      final line = lines[i].trim();

      // Skip if line matches skip patterns
      bool shouldSkip = false;
      for (final pattern in skipPatterns) {
        if (pattern.hasMatch(line)) {
          shouldSkip = true;
          break;
        }
      }
      if (shouldSkip) continue;

      // Skip lines that look like addresses, phone numbers, or dates
      if (line.contains(RegExp(r'\d{3,}'))) continue;
      if (line.contains(RegExp(r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}'))) continue;
      if (line.length < 3 || line.length > 50) continue;

      // Skip common non-merchant words
      final nonMerchantWords = ['welcome', 'thank you', 'thanks', 'customer', 'copy', 'original'];
      if (nonMerchantWords.any((word) => line.toLowerCase().contains(word))) continue;

      // This looks like a merchant name
      if (line.isNotEmpty && RegExp(r'^[a-zA-Z\s&\-\.]+$').hasMatch(line)) {
        return line.toUpperCase(); // Standardize format
      }
    }

    // Fallback: return first non-empty line
    for (final line in lines) {
      if (line.trim().isNotEmpty && line.trim().length > 2) {
        return line.trim();
      }
    }

    return null;
  }

  /// Extract date from receipt text
  String? _extractDate(List<String> lines) {
    final datePatterns = [
      // Common date formats
      RegExp(r'(\d{1,2}[/-]\d{1,2}[/-]\d{4})'), // DD/MM/YYYY or MM/DD/YYYY
      RegExp(r'(\d{4}[/-]\d{1,2}[/-]\d{1,2})'), // YYYY/MM/DD
      RegExp(r'(\d{1,2}[/-]\d{1,2}[/-]\d{2})'), // DD/MM/YY or MM/DD/YY

      // Date with time
      RegExp(r'(\d{1,2}[/-]\d{1,2}[/-]\d{4})\s+(\d{1,2}:\d{2})'),
      RegExp(r'(\d{4}[/-]\d{1,2}[/-]\d{1,2})\s+(\d{1,2}:\d{2})'),

      // Text dates
      RegExp(r'(\d{1,2})\s+(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\s+(\d{4})', caseSensitive: false),
      RegExp(r'(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\s+(\d{1,2}),?\s+(\d{4})', caseSensitive: false),
      RegExp(r'(\d{1,2}\s+\w+\s+\d{2,4})'),
      RegExp(r'(\w+\s+\d{1,2},?\s+\d{2,4})'),
    ];

    for (final line in lines) {
      for (final pattern in datePatterns) {
        final match = pattern.firstMatch(line);
        if (match != null) {
          return match.group(1);
        }
      }
    }

    return null;
  }

  /// Extract items from receipt text
  List<Map<String, dynamic>> _extractItems(List<String> lines) {
    List<Map<String, dynamic>> items = [];

    // Skip lines that are likely headers or footers
    final skipWords = ['total', 'subtotal', 'tax', 'vat', 'change', 'cash', 'card', 'receipt', 'thank', 'welcome'];

    for (final line in lines) {
      final cleanLine = line.trim();
      if (cleanLine.isEmpty) continue;

      // Skip if line contains skip words
      if (skipWords.any((word) => cleanLine.toLowerCase().contains(word))) continue;

      // Multiple patterns for item extraction
      final itemPatterns = [
        // Item name followed by price: "Coca Cola 45.00"
        RegExp(r'^(.+?)\s+(\d+[.,]\d{2})$'),
        // Quantity, item name, price: "2 Bread 120.00"
        RegExp(r'^(\d+)\s+(.+?)\s+(\d+[.,]\d{2})$'),
        // Item name with quantity and price: "Milk (2L) 85.50"
        RegExp(r'^(.+?)\s*\([^)]*\)\s+(\d+[.,]\d{2})$'),
        // Item with @ symbol: "Sugar @ 150.00"
        RegExp(r'^(.+?)\s*@\s*(\d+[.,]\d{2})$'),
      ];

      for (final pattern in itemPatterns) {
        final match = pattern.firstMatch(cleanLine);
        if (match != null) {
          String? name;
          double? price;
          int quantity = 1;

          if (pattern == itemPatterns[1]) { // Pattern with quantity
            quantity = int.tryParse(match.group(1) ?? '1') ?? 1;
            name = match.group(2)?.trim();
            final priceStr = match.group(3)?.replaceAll(',', '.');
            price = double.tryParse(priceStr ?? '');
          } else {
            name = match.group(1)?.trim();
            final priceStr = match.group(2)?.replaceAll(',', '.');
            price = double.tryParse(priceStr ?? '');
          }

          // Validate item
          if (name != null && price != null && name.length > 2 && price > 0) {
            // Clean up item name
            name = name.replaceAll(RegExp(r'[^\w\s\-\(\)]'), '').trim();

            if (name.isNotEmpty) {
              items.add({
                'name': name,
                'price': price,
                'quantity': quantity,
              });
              break; // Found a match, move to next line
            }
          }
        }
      }
    }

    return items;
  }

  /// Extract tax amount from receipt text
  double? _extractTax(List<String> lines) {
    final taxPatterns = [
      RegExp(r'tax[:\s]*(\d+[.,]\d{2})', caseSensitive: false),
      RegExp(r'vat[:\s]*(\d+[.,]\d{2})', caseSensitive: false),
      RegExp(r'(\d+[.,]\d{2})\s*tax', caseSensitive: false),
    ];

    for (final line in lines) {
      for (final pattern in taxPatterns) {
        final match = pattern.firstMatch(line.toLowerCase());
        if (match != null) {
          final taxStr = match.group(1)?.replaceAll(',', '.');
          return double.tryParse(taxStr ?? '');
        }
      }
    }

    return null;
  }

  /// Extract payment method from receipt text
  String? _extractPaymentMethod(List<String> lines) {
    final paymentMethods = ['cash', 'card', 'credit', 'debit', 'mpesa', 'mobile'];
    
    for (final line in lines) {
      final lowerLine = line.toLowerCase();
      for (final method in paymentMethods) {
        if (lowerLine.contains(method)) {
          return method.toUpperCase();
        }
      }
    }

    return null;
  }

  /// Scan barcode or QR code
  Future<String?> scanBarcode() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        preferredCameraDevice: CameraDevice.rear,
      );

      if (image == null) return null;

      final inputImage = InputImage.fromFilePath(image.path);
      final barcodes = await _barcodeScanner.processImage(inputImage);

      if (barcodes.isNotEmpty) {
        return barcodes.first.displayValue;
      }

      return null;
    } catch (e) {
      print('Error scanning barcode: $e');
      return null;
    }
  }

  /// Get receipt history
  List<Map<String, dynamic>> getReceiptHistory() {
    return _scannedReceipts.toList();
  }

  /// Clear receipt history
  void clearHistory() {
    _scannedReceipts.clear();
  }

  /// Save receipt data to local storage
  Future<void> saveReceiptData(Map<String, dynamic> receiptData) async {
    try {
      // This would typically save to a database or local storage
      // For now, we'll just add to the in-memory list
      _scannedReceipts.add(receiptData);
    } catch (e) {
      print('Error saving receipt data: $e');
    }
  }

  /// Validate extracted data
  bool validateReceiptData(Map<String, dynamic> data) {
    // Check if we have at least an amount
    if (data['amount'] == null) return false;
    
    // Check if amount is reasonable
    final amount = data['amount'] as double?;
    if (amount == null || amount <= 0 || amount > 1000000) return false;
    
    return true;
  }

  /// Get confidence score for extracted data
  double getConfidenceScore(Map<String, dynamic> data) {
    double score = 0.0;
    
    // Amount detected
    if (data['amount'] != null) score += 0.4;
    
    // Merchant detected
    if (data['merchant'] != null && data['merchant'].toString().isNotEmpty) score += 0.2;
    
    // Date detected
    if (data['date'] != null) score += 0.2;
    
    // Items detected
    if (data['items'] != null && (data['items'] as List).isNotEmpty) score += 0.1;
    
    // Tax detected
    if (data['tax'] != null) score += 0.1;
    
    return score;
  }

  /// Process multiple receipts in batch
  Future<List<Map<String, dynamic>>> processBatchReceipts(List<File> imageFiles) async {
    List<Map<String, dynamic>> results = [];
    
    for (final file in imageFiles) {
      final result = await processReceiptImage(file);
      if (result != null) {
        results.add(result);
      }
    }
    
    return results;
  }
}
