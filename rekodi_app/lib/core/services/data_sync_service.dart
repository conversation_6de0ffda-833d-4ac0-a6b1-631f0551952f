import 'dart:convert';
import 'dart:io';
import 'package:get/get.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:path_provider/path_provider.dart';
import '../models/transaction.dart';
import '../models/budget.dart';
import '../models/recurring_transaction.dart';
import '../models/inventory_item.dart';
import 'local_storage_service.dart';
import 'appwrite_service.dart';

enum SyncStatus {
  idle,
  syncing,
  success,
  error,
  conflict,
}

class DataSyncService extends GetxService {
  static DataSyncService get to => Get.find();
  
  final LocalStorageService _storageService = Get.find();
  final AppwriteService _appwriteService = Get.find();
  final Connectivity _connectivity = Connectivity();
  
  final RxBool _isOnline = false.obs;
  final Rx<SyncStatus> _syncStatus = SyncStatus.idle.obs;
  final RxString _lastSyncTime = ''.obs;
  final RxDouble _syncProgress = 0.0.obs;
  final RxList<String> _syncErrors = <String>[].obs;
  final RxBool _autoSyncEnabled = true.obs;
  
  // Getters
  bool get isOnline => _isOnline.value;
  SyncStatus get syncStatus => _syncStatus.value;
  String get lastSyncTime => _lastSyncTime.value;
  double get syncProgress => _syncProgress.value;
  List<String> get syncErrors => _syncErrors;
  bool get autoSyncEnabled => _autoSyncEnabled.value;

  @override
  void onInit() {
    super.onInit();
    _initializeConnectivity();
    _loadSyncSettings();
  }

  void _initializeConnectivity() {
    _connectivity.onConnectivityChanged.listen((List<ConnectivityResult> results) {
      _isOnline.value = results.isNotEmpty && results.first != ConnectivityResult.none;

      if (_isOnline.value && _autoSyncEnabled.value) {
        _performAutoSync();
      }
    });
    
    // Check initial connectivity
    _checkConnectivity();
  }

  Future<void> _checkConnectivity() async {
    final results = await _connectivity.checkConnectivity();
    _isOnline.value = results.isNotEmpty && results.first != ConnectivityResult.none;
  }

  void _loadSyncSettings() {
    _autoSyncEnabled.value = _storageService.getSetting('auto_sync_enabled', true) ?? true;
    _lastSyncTime.value = _storageService.getSetting('last_sync_time', '') ?? '';
  }

  // Manual sync trigger
  Future<bool> syncData(String accountId) async {
    if (!_isOnline.value) {
      _addSyncError('No internet connection available');
      return false;
    }

    if (_syncStatus.value == SyncStatus.syncing) {
      return false; // Already syncing
    }

    try {
      _syncStatus.value = SyncStatus.syncing;
      _syncProgress.value = 0.0;
      _syncErrors.clear();

      // Sync transactions
      await _syncTransactions(accountId);
      _syncProgress.value = 0.25;

      // Sync budgets
      await _syncBudgets(accountId);
      _syncProgress.value = 0.5;

      // Sync recurring transactions
      await _syncRecurringTransactions(accountId);
      _syncProgress.value = 0.75;

      // Sync inventory items (for business accounts)
      await _syncInventoryItems(accountId);
      _syncProgress.value = 1.0;

      _syncStatus.value = SyncStatus.success;
      _lastSyncTime.value = DateTime.now().toIso8601String();
      await _storageService.saveSetting('last_sync_time', _lastSyncTime.value);

      return true;
    } catch (e) {
      _syncStatus.value = SyncStatus.error;
      _addSyncError('Sync failed: ${e.toString()}');
      return false;
    }
  }

  // Auto sync (triggered by connectivity changes)
  Future<void> _performAutoSync() async {
    final accountId = _storageService.getCurrentAccountId();
    if (accountId != null) {
      await syncData(accountId);
    }
  }

  // Sync transactions
  Future<void> _syncTransactions(String accountId) async {
    try {
      // Get local transactions
      final localTransactions = _storageService.getTransactions(accountId);
      
      // Get remote transactions (this would be implemented with Appwrite)
      // final remoteTransactions = await _appwriteService.getTransactions(accountId);
      
      // For now, we'll just upload local transactions
      for (final transaction in localTransactions) {
        // await _appwriteService.saveTransaction(transaction);
      }
      
      // In a real implementation, we would:
      // 1. Compare local and remote timestamps
      // 2. Resolve conflicts (last-write-wins or user choice)
      // 3. Merge changes
      // 4. Update local storage with merged data
      
    } catch (e) {
      _addSyncError('Failed to sync transactions: ${e.toString()}');
      rethrow;
    }
  }

  // Sync budgets
  Future<void> _syncBudgets(String accountId) async {
    try {
      final localBudgets = _storageService.getBudgets(accountId);
      
      // Upload local budgets to remote
      for (final budget in localBudgets) {
        // await _appwriteService.saveBudget(budget);
      }
      
    } catch (e) {
      _addSyncError('Failed to sync budgets: ${e.toString()}');
      rethrow;
    }
  }

  // Sync recurring transactions
  Future<void> _syncRecurringTransactions(String accountId) async {
    try {
      final localRecurringTransactions = _storageService.getRecurringTransactions(accountId);
      
      for (final transaction in localRecurringTransactions) {
        // await _appwriteService.saveRecurringTransaction(transaction);
      }
      
    } catch (e) {
      _addSyncError('Failed to sync recurring transactions: ${e.toString()}');
      rethrow;
    }
  }

  // Sync inventory items
  Future<void> _syncInventoryItems(String accountId) async {
    try {
      final localInventoryItems = _storageService.getInventoryItems(accountId);
      
      for (final item in localInventoryItems) {
        // await _appwriteService.saveInventoryItem(item);
      }
      
    } catch (e) {
      _addSyncError('Failed to sync inventory items: ${e.toString()}');
      rethrow;
    }
  }

  // Conflict resolution
  Future<T> _resolveConflict<T>(T local, T remote, String itemType) async {
    // For now, implement last-write-wins strategy
    // In a real app, you might want to show a dialog to the user
    
    if (local is Transaction && remote is Transaction) {
      return (local.updatedAt?.isAfter(remote.updatedAt ?? DateTime(0)) ?? false) 
          ? local : remote;
    }
    
    if (local is Budget && remote is Budget) {
      return (local.updatedAt?.isAfter(remote.updatedAt ?? DateTime(0)) ?? false) 
          ? local : remote;
    }
    
    // Default to local
    return local;
  }

  // Settings management
  Future<void> setAutoSyncEnabled(bool enabled) async {
    _autoSyncEnabled.value = enabled;
    await _storageService.saveSetting('auto_sync_enabled', enabled);
    
    if (enabled && _isOnline.value) {
      _performAutoSync();
    }
  }

  // Force sync (ignores auto-sync setting)
  Future<bool> forcSync(String accountId) async {
    final wasAutoSyncEnabled = _autoSyncEnabled.value;
    _autoSyncEnabled.value = true;
    
    final result = await syncData(accountId);
    
    _autoSyncEnabled.value = wasAutoSyncEnabled;
    return result;
  }

  // Clear sync data
  Future<void> clearSyncData() async {
    _lastSyncTime.value = '';
    _syncErrors.clear();
    _syncStatus.value = SyncStatus.idle;
    _syncProgress.value = 0.0;
    
    await _storageService.saveSetting('last_sync_time', '');
  }

  // Get sync statistics
  Map<String, dynamic> getSyncStats() {
    return {
      'is_online': _isOnline.value,
      'sync_status': _syncStatus.value.name,
      'last_sync_time': _lastSyncTime.value,
      'auto_sync_enabled': _autoSyncEnabled.value,
      'sync_errors_count': _syncErrors.length,
      'sync_progress': _syncProgress.value,
    };
  }

  // Backup before sync
  Future<String?> createPreSyncBackup(String accountId) async {
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'pre_sync_backup_$timestamp';
      
      final data = await _storageService.exportAllData(accountId);
      final jsonString = const JsonEncoder.withIndent('  ').convert(data);
      
      // Save to temporary location
      final directory = await getTemporaryDirectory();
      final file = File('${directory.path}/$fileName.json');
      await file.writeAsString(jsonString);
      
      return file.path;
    } catch (e) {
      _addSyncError('Failed to create pre-sync backup: ${e.toString()}');
      return null;
    }
  }

  // Restore from backup if sync fails
  Future<void> restoreFromBackup(String accountId, String backupPath) async {
    try {
      final file = File(backupPath);
      if (await file.exists()) {
        final jsonString = await file.readAsString();
        final data = jsonDecode(jsonString);
        
        await _storageService.importAllData(accountId, data);
        
        // Clean up backup file
        await file.delete();
      }
    } catch (e) {
      _addSyncError('Failed to restore from backup: ${e.toString()}');
    }
  }

  // Sync specific item types
  Future<bool> syncTransactionsOnly(String accountId) async {
    if (!_isOnline.value) return false;
    
    try {
      _syncStatus.value = SyncStatus.syncing;
      await _syncTransactions(accountId);
      _syncStatus.value = SyncStatus.success;
      return true;
    } catch (e) {
      _syncStatus.value = SyncStatus.error;
      return false;
    }
  }

  Future<bool> syncBudgetsOnly(String accountId) async {
    if (!_isOnline.value) return false;
    
    try {
      _syncStatus.value = SyncStatus.syncing;
      await _syncBudgets(accountId);
      _syncStatus.value = SyncStatus.success;
      return true;
    } catch (e) {
      _syncStatus.value = SyncStatus.error;
      return false;
    }
  }

  // Helper methods
  void _addSyncError(String error) {
    _syncErrors.add('${DateTime.now().toIso8601String()}: $error');
    
    // Keep only last 10 errors
    if (_syncErrors.length > 10) {
      _syncErrors.removeAt(0);
    }
  }

  String getLastSyncTimeFormatted() {
    if (_lastSyncTime.value.isEmpty) return 'Never';
    
    try {
      final lastSync = DateTime.parse(_lastSyncTime.value);
      final now = DateTime.now();
      final difference = now.difference(lastSync);
      
      if (difference.inMinutes < 1) {
        return 'Just now';
      } else if (difference.inHours < 1) {
        return '${difference.inMinutes} minutes ago';
      } else if (difference.inDays < 1) {
        return '${difference.inHours} hours ago';
      } else {
        return '${difference.inDays} days ago';
      }
    } catch (e) {
      return 'Unknown';
    }
  }

  // Cleanup old sync data
  Future<void> cleanupOldSyncData() async {
    // Remove sync errors older than 7 days
    final cutoffDate = DateTime.now().subtract(const Duration(days: 7));
    
    _syncErrors.removeWhere((error) {
      try {
        final errorDate = DateTime.parse(error.split(':').first);
        return errorDate.isBefore(cutoffDate);
      } catch (e) {
        return true; // Remove malformed entries
      }
    });
  }
}


