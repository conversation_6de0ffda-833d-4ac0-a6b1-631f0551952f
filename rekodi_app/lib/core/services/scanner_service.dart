import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import '../models/scanned_receipt.dart';
import 'receipt_scanner_service.dart';

class ScannerService extends GetxService {
  static ScannerService get to => Get.find();

  final ImagePicker _picker = ImagePicker();
  final RxBool _isScanning = false.obs;
  final RxBool _isProcessing = false.obs;

  bool get isScanning => _isScanning.value;
  bool get isProcessing => _isProcessing.value;

  // Scan receipt from camera
  Future<ScannedReceipt?> scanReceiptFromCamera() async {
    try {
      _isScanning.value = true;
      
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        preferredCameraDevice: CameraDevice.rear,
      );
      
      if (image != null) {
        return await _processReceiptImage(image);
      }
      
      return null;
    } catch (e) {
      print('Error scanning receipt from camera: $e');
      Get.snackbar(
        'Scan Error',
        'Failed to capture receipt image',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return null;
    } finally {
      _isScanning.value = false;
    }
  }

  // Scan receipt from gallery
  Future<ScannedReceipt?> scanReceiptFromGallery() async {
    try {
      _isScanning.value = true;
      
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );
      
      if (image != null) {
        return await _processReceiptImage(image);
      }
      
      return null;
    } catch (e) {
      print('Error scanning receipt from gallery: $e');
      Get.snackbar(
        'Scan Error',
        'Failed to load receipt image',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return null;
    } finally {
      _isScanning.value = false;
    }
  }

  // Process receipt image and extract data
  Future<ScannedReceipt?> _processReceiptImage(XFile image) async {
    try {
      _isProcessing.value = true;

      // Use the ReceiptScannerService for actual OCR processing
      final receiptScannerService = Get.find<ReceiptScannerService>();
      final extractedData = await receiptScannerService.processReceiptImage(File(image.path));

      if (extractedData != null) {
        final scannedReceipt = ScannedReceipt(
          id: 'receipt_${DateTime.now().millisecondsSinceEpoch}',
          imagePath: image.path,
          merchantName: extractedData['merchant'] ?? 'Unknown Merchant',
          totalAmount: extractedData['amount'] ?? 0.0,
          date: _parseDate(extractedData['date']) ?? DateTime.now(),
          items: _convertToReceiptItems(extractedData['items'] ?? []),
          confidence: 0.85, // You could calculate this based on extraction quality
          rawText: extractedData['rawText'] ?? '',
          extractedData: extractedData,
        );

        Get.snackbar(
          'Receipt Scanned',
          'Receipt processed successfully - ${scannedReceipt.merchantName}',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );

        return scannedReceipt;
      } else {
        // Fallback to mock data if OCR fails
        Get.snackbar(
          'OCR Failed',
          'Using fallback data - please verify details',
          backgroundColor: Colors.orange,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );

        final scannedReceipt = ScannedReceipt(
          id: 'receipt_${DateTime.now().millisecondsSinceEpoch}',
          imagePath: image.path,
          merchantName: _extractMockMerchantName(),
          totalAmount: _extractMockAmount(),
          date: DateTime.now(),
          items: _extractMockItems(),
          confidence: 0.50, // Lower confidence for mock data
          rawText: _generateMockRawText(),
          extractedData: _generateMockExtractedData(),
        );

        return scannedReceipt;
      }
    } catch (e) {
      print('Error processing receipt image: $e');
      Get.snackbar(
        'Processing Error',
        'Failed to process receipt image: ${e.toString()}',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return null;
    } finally {
      _isProcessing.value = false;
    }
  }

  // Show scanner options dialog
  void showScannerOptions() {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Scan Receipt',
              style: Get.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Choose how you want to scan your receipt',
              style: Get.textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            
            // Camera Option
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.camera_alt_rounded,
                  color: Colors.blue,
                ),
              ),
              title: const Text('Take Photo'),
              subtitle: const Text('Capture receipt with camera'),
              onTap: () {
                Get.back();
                scanReceiptFromCamera().then((receipt) {
                  if (receipt != null) {
                    _handleScannedReceipt(receipt);
                  }
                });
              },
            ),
            
            // Gallery Option
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.photo_library_rounded,
                  color: Colors.green,
                ),
              ),
              title: const Text('Choose from Gallery'),
              subtitle: const Text('Select existing photo'),
              onTap: () {
                Get.back();
                scanReceiptFromGallery().then((receipt) {
                  if (receipt != null) {
                    _handleScannedReceipt(receipt);
                  }
                });
              },
            ),
            
            const SizedBox(height: 16),
          ],
        ),
      ),
      isScrollControlled: true,
    );
  }

  // Handle scanned receipt
  void _handleScannedReceipt(ScannedReceipt receipt) {
    // Navigate to add transaction screen with pre-filled data
    Get.toNamed('/add-transaction', arguments: {
      'scannedReceipt': receipt,
      'title': receipt.merchantName,
      'amount': receipt.totalAmount,
      'date': receipt.date,
      'receiptImagePath': receipt.imagePath,
    });
  }

  // Mock data generation methods (replace with real OCR)
  String _extractMockMerchantName() {
    final merchants = [
      'Walmart', 'Target', 'Starbucks', 'McDonald\'s', 'Subway',
      'CVS Pharmacy', 'Walgreens', 'Home Depot', 'Best Buy', 'Amazon'
    ];
    return merchants[DateTime.now().millisecond % merchants.length];
  }

  double _extractMockAmount() {
    return (DateTime.now().millisecond % 10000) / 100.0 + 5.0;
  }

  // Helper method to parse date from extracted text
  DateTime? _parseDate(String? dateStr) {
    if (dateStr == null || dateStr.isEmpty) return null;

    try {
      // Try different date formats
      final dateFormats = [
        RegExp(r'(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})'), // MM/DD/YYYY or DD/MM/YYYY
        RegExp(r'(\d{4})[\/\-](\d{1,2})[\/\-](\d{1,2})'), // YYYY/MM/DD
        RegExp(r'(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2})'), // MM/DD/YY or DD/MM/YY
      ];

      for (final format in dateFormats) {
        final match = format.firstMatch(dateStr);
        if (match != null) {
          final part1 = int.parse(match.group(1)!);
          final part2 = int.parse(match.group(2)!);
          final part3 = int.parse(match.group(3)!);

          // Assume MM/DD/YYYY format for now
          if (part3 > 31) { // Year is in third position
            return DateTime(part3, part1, part2);
          } else if (part1 > 31) { // Year is in first position
            return DateTime(part1, part2, part3);
          } else {
            // Assume current format is MM/DD/YY or DD/MM/YY
            final year = part3 < 50 ? 2000 + part3 : 1900 + part3;
            return DateTime(year, part1, part2);
          }
        }
      }
    } catch (e) {
      print('Error parsing date: $e');
    }

    return null;
  }

  // Helper method to convert extracted items to ReceiptItem objects
  List<ReceiptItem> _convertToReceiptItems(List<dynamic> items) {
    return items.map((item) {
      if (item is Map<String, dynamic>) {
        return ReceiptItem(
          name: item['name'] ?? 'Unknown Item',
          price: (item['price'] ?? 0.0).toDouble(),
          quantity: (item['quantity'] ?? 1).toInt(),
        );
      }
      return ReceiptItem(name: 'Unknown Item', price: 0.0, quantity: 1);
    }).toList();
  }

  List<ReceiptItem> _extractMockItems() {
    return [
      ReceiptItem(
        name: 'Coffee',
        quantity: 1,
        price: 4.50,
      ),
      ReceiptItem(
        name: 'Sandwich',
        quantity: 1,
        price: 8.99,
      ),
    ];
  }

  String _generateMockRawText() {
    return '''
STARBUCKS STORE #12345
123 MAIN ST
ANYTOWN, ST 12345

PIKE PLACE ROAST     4.50
TURKEY SANDWICH      8.99

SUBTOTAL            13.49
TAX                  1.08
TOTAL               14.57

THANK YOU!
''';
  }

  Map<String, dynamic> _generateMockExtractedData() {
    return {
      'merchant_name': 'Starbucks',
      'address': '123 Main St, Anytown, ST 12345',
      'phone': '(*************',
      'date': DateTime.now().toIso8601String(),
      'total': 14.57,
      'subtotal': 13.49,
      'tax': 1.08,
      'payment_method': 'Credit Card',
    };
  }
}
