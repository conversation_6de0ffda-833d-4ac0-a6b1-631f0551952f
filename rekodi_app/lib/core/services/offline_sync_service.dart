import 'package:get/get.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get_storage/get_storage.dart';
import 'dart:convert';
import 'dart:async';
import '../models/sync_operation.dart';
import '../models/transaction.dart';
import '../models/budget.dart';
import 'account_service.dart';
import 'appwrite_service.dart';

class OfflineSyncService extends GetxService {
  final GetStorage _storage = GetStorage();
  final Connectivity _connectivity = Connectivity();
  final AppwriteService _appwriteService = Get.find();
  final AccountService _accountService = Get.find();

  final RxBool _isOnline = false.obs;
  final RxBool _isSyncing = false.obs;
  final RxList<SyncOperation> _pendingOperations = <SyncOperation>[].obs;
  final RxInt _lastSyncTimestamp = 0.obs;

  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  bool get isOnline => _isOnline.value;
  bool get isSyncing => _isSyncing.value;
  List<SyncOperation> get pendingOperations => _pendingOperations;
  DateTime? get lastSyncTime => _lastSyncTimestamp.value > 0 
      ? DateTime.fromMillisecondsSinceEpoch(_lastSyncTimestamp.value)
      : null;

  @override
  void onInit() {
    super.onInit();
    _initializeConnectivity();
    _loadPendingOperations();
    _setupPeriodicSync();
  }

  @override
  void onClose() {
    _connectivitySubscription?.cancel();
    super.onClose();
  }

  /// Initialize connectivity monitoring
  void _initializeConnectivity() async {
    // Check initial connectivity
    final connectivityResults = await _connectivity.checkConnectivity();
    _updateConnectivityStatus(connectivityResults);

    // Listen for connectivity changes
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      _updateConnectivityStatus,
    );
  }

  /// Update connectivity status and trigger sync if online
  void _updateConnectivityStatus(List<ConnectivityResult> results) {
    final wasOnline = _isOnline.value;
    _isOnline.value = results.isNotEmpty && results.first != ConnectivityResult.none;

    if (!wasOnline && _isOnline.value) {
      // Just came online, trigger sync
      _triggerSync();
    }
  }

  /// Load pending operations from storage
  void _loadPendingOperations() {
    try {
      final accountId = _accountService.currentAccount?.id;
      if (accountId == null) return;

      final key = 'pending_operations_$accountId';
      final data = _storage.read(key);
      
      if (data != null) {
        final List<dynamic> jsonList = jsonDecode(data);
        _pendingOperations.value = jsonList
            .map((json) => SyncOperation.fromJson(json))
            .toList();
      }

      final lastSync = _storage.read('last_sync_$accountId');
      if (lastSync != null) {
        _lastSyncTimestamp.value = lastSync;
      }
    } catch (e) {
      print('Error loading pending operations: $e');
    }
  }

  /// Save pending operations to storage
  Future<void> _savePendingOperations() async {
    try {
      final accountId = _accountService.currentAccount?.id;
      if (accountId == null) return;

      final key = 'pending_operations_$accountId';
      final jsonList = _pendingOperations.map((op) => op.toJson()).toList();
      await _storage.write(key, jsonEncode(jsonList));
    } catch (e) {
      print('Error saving pending operations: $e');
    }
  }

  /// Add operation to sync queue
  Future<void> queueOperation(SyncOperation operation) async {
    _pendingOperations.add(operation);
    await _savePendingOperations();

    // If online, try to sync immediately
    if (_isOnline.value && !_isSyncing.value) {
      _triggerSync();
    }
  }

  /// Queue transaction operation
  Future<void> queueTransactionOperation(
    String operationType,
    Transaction transaction,
  ) async {
    final operation = SyncOperation(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: operationType,
      entityType: 'transaction',
      entityId: transaction.id,
      data: transaction.toJson(),
      timestamp: DateTime.now(),
      accountId: transaction.accountId ?? '',
    );

    await queueOperation(operation);
  }

  /// Queue budget operation
  Future<void> queueBudgetOperation(
    String operationType,
    Budget budget,
  ) async {
    final operation = SyncOperation(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: operationType,
      entityType: 'budget',
      entityId: budget.id,
      data: budget.toJson(),
      timestamp: DateTime.now(),
      accountId: budget.accountId,
    );

    await queueOperation(operation);
  }

  /// Trigger sync process
  Future<void> _triggerSync() async {
    if (_isSyncing.value || !_isOnline.value) return;

    try {
      _isSyncing.value = true;
      await _performSync();
    } catch (e) {
      print('Sync error: $e');
    } finally {
      _isSyncing.value = false;
    }
  }

  /// Perform the actual sync process
  Future<void> _performSync() async {
    if (_pendingOperations.isEmpty) return;

    final operationsToSync = List<SyncOperation>.from(_pendingOperations);
    final successfulOperations = <SyncOperation>[];

    for (final operation in operationsToSync) {
      try {
        final success = await _syncOperation(operation);
        if (success) {
          successfulOperations.add(operation);
        }
      } catch (e) {
        print('Error syncing operation ${operation.id}: $e');
        // Continue with other operations
      }
    }

    // Remove successful operations
    for (final operation in successfulOperations) {
      _pendingOperations.remove(operation);
    }

    if (successfulOperations.isNotEmpty) {
      await _savePendingOperations();
      _lastSyncTimestamp.value = DateTime.now().millisecondsSinceEpoch;
      
      final accountId = _accountService.currentAccount?.id;
      if (accountId != null) {
        await _storage.write('last_sync_$accountId', _lastSyncTimestamp.value);
      }
    }
  }

  /// Sync individual operation
  Future<bool> _syncOperation(SyncOperation operation) async {
    try {
      switch (operation.entityType) {
        case 'transaction':
          return await _syncTransactionOperation(operation);
        case 'budget':
          return await _syncBudgetOperation(operation);
        default:
          print('Unknown entity type: ${operation.entityType}');
          return false;
      }
    } catch (e) {
      print('Error syncing operation ${operation.id}: $e');
      return false;
    }
  }

  /// Sync transaction operation
  Future<bool> _syncTransactionOperation(SyncOperation operation) async {
    try {
      switch (operation.type) {
        case 'create':
          await _appwriteService.createTransaction(operation.data);
          break;
        case 'update':
          await _appwriteService.updateTransaction(operation.entityId, operation.data);
          break;
        case 'delete':
          await _appwriteService.deleteTransaction(operation.entityId);
          break;
        default:
          return false;
      }

      return true;
    } catch (e) {
      print('Error syncing transaction operation: $e');
      return false;
    }
  }

  /// Sync budget operation
  Future<bool> _syncBudgetOperation(SyncOperation operation) async {
    try {
      switch (operation.type) {
        case 'create':
          await _appwriteService.createBudget(operation.data);
          break;
        case 'update':
          await _appwriteService.updateBudget(operation.entityId, operation.data);
          break;
        case 'delete':
          await _appwriteService.deleteBudget(operation.entityId);
          break;
        default:
          return false;
      }

      return true;
    } catch (e) {
      print('Error syncing budget operation: $e');
      return false;
    }
  }

  /// Force sync all pending operations
  Future<void> forceSyncAll() async {
    if (!_isOnline.value) {
      Get.snackbar(
        'Offline',
        'Cannot sync while offline. Please check your internet connection.',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    await _triggerSync();
    
    Get.snackbar(
      'Sync Complete',
      'All pending changes have been synchronized.',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// Setup periodic sync (every 5 minutes when online)
  void _setupPeriodicSync() {
    Timer.periodic(const Duration(minutes: 5), (timer) {
      if (_isOnline.value && !_isSyncing.value && _pendingOperations.isNotEmpty) {
        _triggerSync();
      }
    });
  }

  /// Get sync status summary
  Map<String, dynamic> getSyncStatus() {
    return {
      'isOnline': _isOnline.value,
      'isSyncing': _isSyncing.value,
      'pendingOperations': _pendingOperations.length,
      'lastSyncTime': lastSyncTime?.toIso8601String(),
      'operationsByType': _getOperationsByType(),
    };
  }

  /// Get operations grouped by type
  Map<String, int> _getOperationsByType() {
    final Map<String, int> counts = {};
    
    for (final operation in _pendingOperations) {
      final key = '${operation.entityType}_${operation.type}';
      counts[key] = (counts[key] ?? 0) + 1;
    }
    
    return counts;
  }

  /// Clear all pending operations (for development)
  Future<void> clearPendingOperations() async {
    _pendingOperations.clear();
    await _savePendingOperations();
    
    Get.snackbar(
      'Cleared',
      'All pending sync operations have been cleared.',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// Download latest data from server
  Future<void> downloadLatestData() async {
    if (!_isOnline.value) {
      Get.snackbar(
        'Offline',
        'Cannot download data while offline.',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    try {
      _isSyncing.value = true;
      
      // Download transactions
      final transactions = await _appwriteService.getTransactions();
      // Save to local storage
      // This would be handled by individual services
      
      // Download budgets
      final budgets = await _appwriteService.getBudgets();
      // Save to local storage
      
      _lastSyncTimestamp.value = DateTime.now().millisecondsSinceEpoch;
      
      Get.snackbar(
        'Download Complete',
        'Latest data has been downloaded and saved locally.',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      Get.snackbar(
        'Download Error',
        'Failed to download latest data: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      _isSyncing.value = false;
    }
  }
}
