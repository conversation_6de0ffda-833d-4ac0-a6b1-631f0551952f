import 'dart:convert';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../models/transaction.dart';
import '../models/budget.dart';
import '../models/recurring_transaction.dart';
import '../models/inventory_item.dart';
import '../models/app_user.dart';

class LocalStorageService extends GetxService {
  static LocalStorageService get to => Get.find();
  
  late GetStorage _storage;
  
  // Storage keys
  static const String _userKey = 'app_user';
  static const String _currentAccountKey = 'current_account_id';
  static const String _accountsKey = 'user_accounts';
  static const String _transactionsKey = 'transactions';
  static const String _budgetsKey = 'budgets';
  static const String _recurringTransactionsKey = 'recurring_transactions';
  static const String _inventoryItemsKey = 'inventory_items';
  static const String _settingsKey = 'app_settings';
  static const String _themeKey = 'theme_settings';
  static const String _notificationKey = 'notification_settings';
  static const String _securityKey = 'security_settings';
  static const String _backupKey = 'backup_metadata';

  @override
  Future<void> onInit() async {
    super.onInit();
    await GetStorage.init();
    _storage = GetStorage();
  }

  // Generic storage methods
  Future<void> write(String key, dynamic value) async {
    await _storage.write(key, value);
  }

  T? read<T>(String key) {
    return _storage.read<T>(key);
  }

  Future<void> remove(String key) async {
    await _storage.remove(key);
  }

  Future<void> clear() async {
    await _storage.erase();
  }

  // User management
  Future<void> saveUser(AppUser user) async {
    await write(_userKey, user.toJson());
  }

  AppUser? getUser() {
    final userData = read<Map<String, dynamic>>(_userKey);
    if (userData != null) {
      return AppUser.fromJson(userData);
    }
    return null;
  }

  Future<void> removeUser() async {
    await remove(_userKey);
  }

  // Account management
  Future<void> setCurrentAccount(String accountId) async {
    await write(_currentAccountKey, accountId);
  }

  String? getCurrentAccountId() {
    return read<String>(_currentAccountKey);
  }

  Future<void> saveAccounts(List<Map<String, dynamic>> accounts) async {
    await write(_accountsKey, accounts);
  }

  List<Map<String, dynamic>> getAccounts() {
    final accountsData = read<List>(_accountsKey);
    if (accountsData != null) {
      return List<Map<String, dynamic>>.from(accountsData);
    }
    return [];
  }

  // Transaction management
  Future<void> saveTransactions(String accountId, List<Transaction> transactions) async {
    final key = '${_transactionsKey}_$accountId';
    final transactionsJson = transactions.map((t) => t.toJson()).toList();
    await write(key, transactionsJson);
  }

  List<Transaction> getTransactions(String accountId) {
    final key = '${_transactionsKey}_$accountId';
    final transactionsData = read<List>(key);
    if (transactionsData != null) {
      return transactionsData
          .map((data) => Transaction.fromJson(Map<String, dynamic>.from(data)))
          .toList();
    }
    return [];
  }

  Future<void> addTransaction(String accountId, Transaction transaction) async {
    final transactions = getTransactions(accountId);
    transactions.add(transaction);
    await saveTransactions(accountId, transactions);
  }

  Future<void> updateTransaction(String accountId, Transaction transaction) async {
    final transactions = getTransactions(accountId);
    final index = transactions.indexWhere((t) => t.id == transaction.id);
    if (index != -1) {
      transactions[index] = transaction;
      await saveTransactions(accountId, transactions);
    }
  }

  Future<void> deleteTransaction(String accountId, String transactionId) async {
    final transactions = getTransactions(accountId);
    transactions.removeWhere((t) => t.id == transactionId);
    await saveTransactions(accountId, transactions);
  }

  // Budget management
  Future<void> saveBudgets(String accountId, List<Budget> budgets) async {
    final key = '${_budgetsKey}_$accountId';
    final budgetsJson = budgets.map((b) => b.toJson()).toList();
    await write(key, budgetsJson);
  }

  List<Budget> getBudgets(String accountId) {
    final key = '${_budgetsKey}_$accountId';
    final budgetsData = read<List>(key);
    if (budgetsData != null) {
      return budgetsData
          .map((data) => Budget.fromJson(Map<String, dynamic>.from(data)))
          .toList();
    }
    return [];
  }

  Future<void> addBudget(String accountId, Budget budget) async {
    final budgets = getBudgets(accountId);
    budgets.add(budget);
    await saveBudgets(accountId, budgets);
  }

  Future<void> updateBudget(String accountId, Budget budget) async {
    final budgets = getBudgets(accountId);
    final index = budgets.indexWhere((b) => b.id == budget.id);
    if (index != -1) {
      budgets[index] = budget;
      await saveBudgets(accountId, budgets);
    }
  }

  Future<void> deleteBudget(String accountId, String budgetId) async {
    final budgets = getBudgets(accountId);
    budgets.removeWhere((b) => b.id == budgetId);
    await saveBudgets(accountId, budgets);
  }

  // Recurring transactions management
  Future<void> saveRecurringTransactions(String accountId, List<RecurringTransaction> transactions) async {
    final key = '${_recurringTransactionsKey}_$accountId';
    final transactionsJson = transactions.map((t) => t.toJson()).toList();
    await write(key, transactionsJson);
  }

  List<RecurringTransaction> getRecurringTransactions(String accountId) {
    final key = '${_recurringTransactionsKey}_$accountId';
    final transactionsData = read<List>(key);
    if (transactionsData != null) {
      return transactionsData
          .map((data) => RecurringTransaction.fromJson(Map<String, dynamic>.from(data)))
          .toList();
    }
    return [];
  }

  Future<void> addRecurringTransaction(String accountId, RecurringTransaction transaction) async {
    final transactions = getRecurringTransactions(accountId);
    transactions.add(transaction);
    await saveRecurringTransactions(accountId, transactions);
  }

  Future<void> updateRecurringTransaction(String accountId, RecurringTransaction transaction) async {
    final transactions = getRecurringTransactions(accountId);
    final index = transactions.indexWhere((t) => t.id == transaction.id);
    if (index != -1) {
      transactions[index] = transaction;
      await saveRecurringTransactions(accountId, transactions);
    }
  }

  Future<void> deleteRecurringTransaction(String accountId, String transactionId) async {
    final transactions = getRecurringTransactions(accountId);
    transactions.removeWhere((t) => t.id == transactionId);
    await saveRecurringTransactions(accountId, transactions);
  }

  // Inventory management (for business accounts)
  Future<void> saveInventoryItems(String accountId, List<InventoryItem> items) async {
    final key = '${_inventoryItemsKey}_$accountId';
    final itemsJson = items.map((i) => i.toJson()).toList();
    await write(key, itemsJson);
  }

  List<InventoryItem> getInventoryItems(String accountId) {
    final key = '${_inventoryItemsKey}_$accountId';
    final itemsData = read<List>(key);
    if (itemsData != null) {
      return itemsData
          .map((data) => InventoryItem.fromJson(Map<String, dynamic>.from(data)))
          .toList();
    }
    return [];
  }

  Future<void> addInventoryItem(String accountId, InventoryItem item) async {
    final items = getInventoryItems(accountId);
    items.add(item);
    await saveInventoryItems(accountId, items);
  }

  Future<void> updateInventoryItem(String accountId, InventoryItem item) async {
    final items = getInventoryItems(accountId);
    final index = items.indexWhere((i) => i.id == item.id);
    if (index != -1) {
      items[index] = item;
      await saveInventoryItems(accountId, items);
    }
  }

  Future<void> deleteInventoryItem(String accountId, String itemId) async {
    final items = getInventoryItems(accountId);
    items.removeWhere((i) => i.id == itemId);
    await saveInventoryItems(accountId, items);
  }

  // Settings management
  Future<void> saveSettings(Map<String, dynamic> settings) async {
    await write(_settingsKey, settings);
  }

  Map<String, dynamic> getSettings() {
    return read<Map<String, dynamic>>(_settingsKey) ?? {};
  }

  Future<void> saveSetting(String key, dynamic value) async {
    final settings = getSettings();
    settings[key] = value;
    await saveSettings(settings);
  }

  T? getSetting<T>(String key, [T? defaultValue]) {
    final settings = getSettings();
    return settings[key] as T? ?? defaultValue;
  }

  // Theme settings
  Future<void> saveThemeSettings(Map<String, dynamic> themeSettings) async {
    await write(_themeKey, themeSettings);
  }

  Map<String, dynamic> getThemeSettings() {
    return read<Map<String, dynamic>>(_themeKey) ?? {};
  }

  // Notification settings
  Future<void> saveNotificationSettings(Map<String, dynamic> notificationSettings) async {
    await write(_notificationKey, notificationSettings);
  }

  Map<String, dynamic> getNotificationSettings() {
    return read<Map<String, dynamic>>(_notificationKey) ?? {};
  }

  // Security settings
  Future<void> saveSecuritySettings(Map<String, dynamic> securitySettings) async {
    await write(_securityKey, securitySettings);
  }

  Map<String, dynamic> getSecuritySettings() {
    return read<Map<String, dynamic>>(_securityKey) ?? {};
  }

  // Backup metadata
  Future<void> saveBackupMetadata(Map<String, dynamic> metadata) async {
    await write(_backupKey, metadata);
  }

  Map<String, dynamic> getBackupMetadata() {
    return read<Map<String, dynamic>>(_backupKey) ?? {};
  }

  // Data export/import
  Future<Map<String, dynamic>> exportAllData(String accountId) async {
    return {
      'account_id': accountId,
      'transactions': getTransactions(accountId).map((t) => t.toJson()).toList(),
      'budgets': getBudgets(accountId).map((b) => b.toJson()).toList(),
      'recurring_transactions': getRecurringTransactions(accountId).map((t) => t.toJson()).toList(),
      'inventory_items': getInventoryItems(accountId).map((i) => i.toJson()).toList(),
      'settings': getSettings(),
      'theme_settings': getThemeSettings(),
      'notification_settings': getNotificationSettings(),
      'security_settings': getSecuritySettings(),
      'exported_at': DateTime.now().toIso8601String(),
    };
  }

  Future<void> importAllData(String accountId, Map<String, dynamic> data) async {
    // Import transactions
    if (data['transactions'] != null) {
      final transactions = (data['transactions'] as List)
          .map((t) => Transaction.fromJson(t))
          .toList();
      await saveTransactions(accountId, transactions);
    }

    // Import budgets
    if (data['budgets'] != null) {
      final budgets = (data['budgets'] as List)
          .map((b) => Budget.fromJson(b))
          .toList();
      await saveBudgets(accountId, budgets);
    }

    // Import recurring transactions
    if (data['recurring_transactions'] != null) {
      final recurringTransactions = (data['recurring_transactions'] as List)
          .map((t) => RecurringTransaction.fromJson(t))
          .toList();
      await saveRecurringTransactions(accountId, recurringTransactions);
    }

    // Import inventory items
    if (data['inventory_items'] != null) {
      final inventoryItems = (data['inventory_items'] as List)
          .map((i) => InventoryItem.fromJson(i))
          .toList();
      await saveInventoryItems(accountId, inventoryItems);
    }

    // Import settings
    if (data['settings'] != null) {
      await saveSettings(Map<String, dynamic>.from(data['settings']));
    }

    if (data['theme_settings'] != null) {
      await saveThemeSettings(Map<String, dynamic>.from(data['theme_settings']));
    }

    if (data['notification_settings'] != null) {
      await saveNotificationSettings(Map<String, dynamic>.from(data['notification_settings']));
    }

    if (data['security_settings'] != null) {
      await saveSecuritySettings(Map<String, dynamic>.from(data['security_settings']));
    }
  }

  // Clear account-specific data
  Future<void> clearAccountData(String accountId) async {
    await remove('${_transactionsKey}_$accountId');
    await remove('${_budgetsKey}_$accountId');
    await remove('${_recurringTransactionsKey}_$accountId');
    await remove('${_inventoryItemsKey}_$accountId');
  }

  // Get storage statistics
  Map<String, dynamic> getStorageStats() {
    final allKeys = _storage.getKeys();
    final stats = <String, dynamic>{
      'total_keys': allKeys.length,
      'user_data': getUser() != null,
      'current_account': getCurrentAccountId(),
      'accounts_count': getAccounts().length,
    };

    final currentAccountId = getCurrentAccountId();
    if (currentAccountId != null) {
      stats['transactions_count'] = getTransactions(currentAccountId).length;
      stats['budgets_count'] = getBudgets(currentAccountId).length;
      stats['recurring_transactions_count'] = getRecurringTransactions(currentAccountId).length;
      stats['inventory_items_count'] = getInventoryItems(currentAccountId).length;
    }

    return stats;
  }
}
