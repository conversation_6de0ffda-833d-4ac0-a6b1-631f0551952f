import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/business_transaction.dart';
import '../models/business_product.dart';
import 'business_product_service.dart';
import 'account_service.dart';

class BusinessAnalyticsService extends GetxService {
  final BusinessProductService _productService = Get.find();
  final AccountService _accountService = Get.find();

  final RxBool _isAnalyzing = false.obs;
  final Rx<BusinessAnalyticsSummary?> _summary = Rx<BusinessAnalyticsSummary?>(null);
  final RxList<FlSpot> _salesTrend = <FlSpot>[].obs;
  final RxList<FlSpot> _purchasesTrend = <FlSpot>[].obs;
  final RxList<FlSpot> _profitTrend = <FlSpot>[].obs;
  final RxList<PieChartSectionData> _categoryBreakdown = <PieChartSectionData>[].obs;
  final RxList<BusinessInsight> _insights = <BusinessInsight>[].obs;

  bool get isAnalyzing => _isAnalyzing.value;
  BusinessAnalyticsSummary? get summary => _summary.value;
  List<FlSpot> get salesTrend => _salesTrend;
  List<FlSpot> get purchasesTrend => _purchasesTrend;
  List<FlSpot> get profitTrend => _profitTrend;
  List<PieChartSectionData> get categoryBreakdown => _categoryBreakdown;
  List<BusinessInsight> get insights => _insights;

  @override
  void onInit() {
    super.onInit();
    _generateAnalytics();

    // Set up periodic refresh for analytics
    Timer.periodic(const Duration(seconds: 30), (_) => _generateAnalytics());
  }

  Future<void> _generateAnalytics() async {
    if (_isAnalyzing.value) return;
    
    _isAnalyzing.value = true;
    
    try {
      final transactions = _productService.transactions;
      final products = _productService.products;
      
      // Generate summary
      _summary.value = _generateSummary(transactions);
      
      // Generate trends
      _generateTrends(transactions);
      
      // Generate category breakdown
      _generateCategoryBreakdown(transactions);
      
      // Generate insights
      _generateInsights(transactions, products);
      
    } catch (e) {
      print('Error generating business analytics: $e');
    } finally {
      _isAnalyzing.value = false;
    }
  }

  BusinessAnalyticsSummary _generateSummary(List<BusinessTransaction> transactions) {
    final now = DateTime.now();
    final thisMonth = DateTime(now.year, now.month, 1);
    final lastMonth = DateTime(now.year, now.month - 1, 1);
    
    final thisMonthTransactions = transactions.where((t) => 
        t.transactionDate.isAfter(thisMonth)).toList();
    final lastMonthTransactions = transactions.where((t) => 
        t.transactionDate.isAfter(lastMonth) && t.transactionDate.isBefore(thisMonth)).toList();
    
    // This month calculations
    final thisMonthSales = thisMonthTransactions
        .where((t) => t.type.isIncome)
        .fold(0.0, (sum, t) => sum + t.totalAmount);
    final thisMonthPurchases = thisMonthTransactions
        .where((t) => t.type.isExpense)
        .fold(0.0, (sum, t) => sum + t.totalAmount);
    final thisMonthProfit = thisMonthTransactions
        .where((t) => t.isSale)
        .fold(0.0, (sum, t) => sum + t.profit);
    
    // Last month calculations
    final lastMonthSales = lastMonthTransactions
        .where((t) => t.type.isIncome)
        .fold(0.0, (sum, t) => sum + t.totalAmount);
    final lastMonthPurchases = lastMonthTransactions
        .where((t) => t.type.isExpense)
        .fold(0.0, (sum, t) => sum + t.totalAmount);
    final lastMonthProfit = lastMonthTransactions
        .where((t) => t.isSale)
        .fold(0.0, (sum, t) => sum + t.profit);
    
    // Calculate growth rates
    final salesGrowth = lastMonthSales > 0 
        ? ((thisMonthSales - lastMonthSales) / lastMonthSales) * 100 
        : 0.0;
    final purchasesGrowth = lastMonthPurchases > 0 
        ? ((thisMonthPurchases - lastMonthPurchases) / lastMonthPurchases) * 100 
        : 0.0;
    final profitGrowth = lastMonthProfit > 0 
        ? ((thisMonthProfit - lastMonthProfit) / lastMonthProfit) * 100 
        : 0.0;
    
    return BusinessAnalyticsSummary(
      totalSales: thisMonthSales,
      totalPurchases: thisMonthPurchases,
      totalProfit: thisMonthProfit,
      netRevenue: thisMonthSales - thisMonthPurchases,
      salesGrowth: salesGrowth,
      purchasesGrowth: purchasesGrowth,
      profitGrowth: profitGrowth,
      transactionCount: thisMonthTransactions.length,
      averageOrderValue: thisMonthTransactions.isNotEmpty 
          ? thisMonthSales / thisMonthTransactions.where((t) => t.isSale).length
          : 0.0,
      topSellingProduct: _getTopSellingProduct(thisMonthTransactions),
      profitMargin: thisMonthSales > 0 ? (thisMonthProfit / thisMonthSales) * 100 : 0.0,
    );
  }

  String? _getTopSellingProduct(List<BusinessTransaction> transactions) {
    final salesMap = <String, int>{};
    
    for (final transaction in transactions.where((t) => t.isSale)) {
      salesMap[transaction.product.name] = 
          (salesMap[transaction.product.name] ?? 0) + transaction.quantity;
    }
    
    if (salesMap.isEmpty) return null;
    
    return salesMap.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  void _generateTrends(List<BusinessTransaction> transactions) {
    final now = DateTime.now();
    final last30Days = now.subtract(const Duration(days: 30));
    
    final recentTransactions = transactions.where((t) => 
        t.transactionDate.isAfter(last30Days)).toList();
    
    // Group by day
    final salesByDay = <int, double>{};
    final purchasesByDay = <int, double>{};
    final profitByDay = <int, double>{};
    
    for (final transaction in recentTransactions) {
      final dayIndex = transaction.transactionDate.difference(last30Days).inDays;
      
      if (transaction.type.isIncome) {
        salesByDay[dayIndex] = (salesByDay[dayIndex] ?? 0) + transaction.totalAmount;
      } else if (transaction.type.isExpense) {
        purchasesByDay[dayIndex] = (purchasesByDay[dayIndex] ?? 0) + transaction.totalAmount;
      }
      
      if (transaction.isSale) {
        profitByDay[dayIndex] = (profitByDay[dayIndex] ?? 0) + transaction.profit;
      }
    }
    
    // Convert to FlSpot lists
    _salesTrend.value = salesByDay.entries
        .map((e) => FlSpot(e.key.toDouble(), e.value))
        .toList()..sort((a, b) => a.x.compareTo(b.x));
    
    _purchasesTrend.value = purchasesByDay.entries
        .map((e) => FlSpot(e.key.toDouble(), e.value))
        .toList()..sort((a, b) => a.x.compareTo(b.x));
    
    _profitTrend.value = profitByDay.entries
        .map((e) => FlSpot(e.key.toDouble(), e.value))
        .toList()..sort((a, b) => a.x.compareTo(b.x));
  }

  void _generateCategoryBreakdown(List<BusinessTransaction> transactions) {
    final now = DateTime.now();
    final thisMonth = DateTime(now.year, now.month, 1);
    
    final thisMonthSales = transactions.where((t) => 
        t.transactionDate.isAfter(thisMonth) && t.isSale).toList();
    
    final categoryTotals = <String, double>{};
    
    for (final transaction in thisMonthSales) {
      final category = transaction.product.category ?? 'Uncategorized';
      categoryTotals[category] = (categoryTotals[category] ?? 0) + transaction.totalAmount;
    }
    
    if (categoryTotals.isEmpty) {
      _categoryBreakdown.value = [];
      return;
    }
    
    final total = categoryTotals.values.fold(0.0, (sum, value) => sum + value);
    final colors = [
      const Color(0xFF5CC9B6),
      const Color(0xFF4D9DE0),
      const Color(0xFF15616D),
      const Color(0xFF78C6A0),
      const Color(0xFF7209B7),
      const Color(0xFFFF7F6F),
    ];
    
    _categoryBreakdown.value = categoryTotals.entries
        .map((e) {
          final index = categoryTotals.keys.toList().indexOf(e.key);
          final percentage = (e.value / total) * 100;
          
          return PieChartSectionData(
            color: colors[index % colors.length],
            value: e.value,
            title: '${percentage.toStringAsFixed(1)}%',
            radius: 60,
            titleStyle: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          );
        })
        .toList();
  }

  void _generateInsights(List<BusinessTransaction> transactions, List<BusinessProduct> products) {
    final insights = <BusinessInsight>[];
    
    // Low stock alerts
    final lowStockProducts = products.where((p) => p.isLowStock).toList();
    if (lowStockProducts.isNotEmpty) {
      insights.add(BusinessInsight(
        id: 'low_stock',
        type: BusinessInsightType.warning,
        title: 'Low Stock Alert',
        description: '${lowStockProducts.length} products are running low on stock',
        value: lowStockProducts.length.toDouble(),
        priority: 9,
        actionable: true,
        action: 'Restock these products soon',
      ));
    }
    
    // Out of stock alerts
    final outOfStockProducts = products.where((p) => p.isOutOfStock).toList();
    if (outOfStockProducts.isNotEmpty) {
      insights.add(BusinessInsight(
        id: 'out_of_stock',
        type: BusinessInsightType.critical,
        title: 'Out of Stock',
        description: '${outOfStockProducts.length} products are out of stock',
        value: outOfStockProducts.length.toDouble(),
        priority: 10,
        actionable: true,
        action: 'Restock immediately to avoid lost sales',
      ));
    }
    
    // Profit margin insights
    final now = DateTime.now();
    final thisMonth = DateTime(now.year, now.month, 1);
    final thisMonthSales = transactions.where((t) => 
        t.transactionDate.isAfter(thisMonth) && t.isSale).toList();
    
    if (thisMonthSales.isNotEmpty) {
      final avgProfitMargin = thisMonthSales
          .map((t) => t.profitMargin)
          .reduce((a, b) => a + b) / thisMonthSales.length;
      
      insights.add(BusinessInsight(
        id: 'profit_margin',
        type: avgProfitMargin > 30 ? BusinessInsightType.positive : 
              avgProfitMargin > 15 ? BusinessInsightType.info : BusinessInsightType.warning,
        title: 'Average Profit Margin',
        description: 'Your average profit margin this month is ${avgProfitMargin.toStringAsFixed(1)}%',
        value: avgProfitMargin,
        priority: avgProfitMargin < 15 ? 8 : 5,
        actionable: avgProfitMargin < 20,
        action: avgProfitMargin < 15 ? 'Consider reviewing your pricing strategy' : null,
      ));
    }
    
    // Top performing products
    final productSales = <String, double>{};
    for (final transaction in thisMonthSales) {
      productSales[transaction.product.name] = 
          (productSales[transaction.product.name] ?? 0) + transaction.totalAmount;
    }
    
    if (productSales.isNotEmpty) {
      final topProduct = productSales.entries
          .reduce((a, b) => a.value > b.value ? a : b);
      
      insights.add(BusinessInsight(
        id: 'top_product',
        type: BusinessInsightType.positive,
        title: 'Top Performing Product',
        description: '${topProduct.key} generated \$${topProduct.value.toStringAsFixed(2)} this month',
        value: topProduct.value,
        priority: 6,
        actionable: false,
      ));
    }
    
    _insights.value = insights..sort((a, b) => b.priority.compareTo(a.priority));
  }

  Future<void> refreshAnalytics() async {
    await _generateAnalytics();
  }

  BusinessAnalyticsSummary? getSummaryForDateRange(DateTime start, DateTime end) {
    final transactions = _productService.transactions.where((t) => 
        t.transactionDate.isAfter(start) && t.transactionDate.isBefore(end)).toList();
    
    if (transactions.isEmpty) return null;
    
    return _generateSummary(transactions);
  }
}

class BusinessAnalyticsSummary {
  final double totalSales;
  final double totalPurchases;
  final double totalProfit;
  final double netRevenue;
  final double salesGrowth;
  final double purchasesGrowth;
  final double profitGrowth;
  final int transactionCount;
  final double averageOrderValue;
  final String? topSellingProduct;
  final double profitMargin;

  const BusinessAnalyticsSummary({
    required this.totalSales,
    required this.totalPurchases,
    required this.totalProfit,
    required this.netRevenue,
    required this.salesGrowth,
    required this.purchasesGrowth,
    required this.profitGrowth,
    required this.transactionCount,
    required this.averageOrderValue,
    this.topSellingProduct,
    required this.profitMargin,
  });
}

enum BusinessInsightType {
  positive,
  info,
  warning,
  critical,
}

class BusinessInsight {
  final String id;
  final BusinessInsightType type;
  final String title;
  final String description;
  final double value;
  final int priority;
  final bool actionable;
  final String? action;

  const BusinessInsight({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.value,
    required this.priority,
    this.actionable = false,
    this.action,
  });
}
