import 'package:get/get.dart';
import 'account_service.dart';

enum PremiumFeature {
  unlimitedTransactions,
  advancedAnalytics,
  multipleBusinessAccounts,
  dataExport,
  cloudSync,
  prioritySupport,
  customCategories,
  recurringTransactions,
  budgetTracking,
  inventoryManagement;

  String get displayName {
    switch (this) {
      case PremiumFeature.unlimitedTransactions:
        return 'Unlimited Transactions';
      case PremiumFeature.advancedAnalytics:
        return 'Advanced Analytics';
      case PremiumFeature.multipleBusinessAccounts:
        return 'Multiple Business Accounts';
      case PremiumFeature.dataExport:
        return 'Data Export';
      case PremiumFeature.cloudSync:
        return 'Cloud Sync';
      case PremiumFeature.prioritySupport:
        return 'Priority Support';
      case PremiumFeature.customCategories:
        return 'Custom Categories';
      case PremiumFeature.recurringTransactions:
        return 'Recurring Transactions';
      case PremiumFeature.budgetTracking:
        return 'Budget Tracking';
      case PremiumFeature.inventoryManagement:
        return 'Inventory Management';
    }
  }

  String get description {
    switch (this) {
      case PremiumFeature.unlimitedTransactions:
        return 'Add unlimited transactions without restrictions';
      case PremiumFeature.advancedAnalytics:
        return 'Detailed reports, charts, and financial insights';
      case PremiumFeature.multipleBusinessAccounts:
        return 'Create and manage multiple business accounts';
      case PremiumFeature.dataExport:
        return 'Export your data to CSV, PDF, and Excel formats';
      case PremiumFeature.cloudSync:
        return 'Sync your data across all devices';
      case PremiumFeature.prioritySupport:
        return 'Get priority customer support';
      case PremiumFeature.customCategories:
        return 'Create custom transaction categories';
      case PremiumFeature.recurringTransactions:
        return 'Set up automatic recurring transactions';
      case PremiumFeature.budgetTracking:
        return 'Create and track budgets with alerts';
      case PremiumFeature.inventoryManagement:
        return 'Full inventory and product management';
    }
  }
}

class SubscriptionService extends GetxService {
  static SubscriptionService get to => Get.find();

  // Development mode - set to false for production
  static const bool isDevelopmentMode = true;

  // Free tier limits
  static const int freeTransactionLimit = 100;
  static const int freeBusinessAccountLimit = 0;
  static const int freeCategoryLimit = 20;

  final RxBool _isPremium = false.obs;
  final Rx<DateTime?> _premiumExpiresAt = Rx<DateTime?>(null);

  bool get isPremium => _isPremium.value;
  DateTime? get premiumExpiresAt => _premiumExpiresAt.value;
  bool get isPremiumExpired => 
    _premiumExpiresAt.value != null && DateTime.now().isAfter(_premiumExpiresAt.value!);

  @override
  void onInit() {
    super.onInit();
    _updatePremiumStatus();
    
    // Listen to account changes
    ever(AccountService.to.currentAccountObs, (account) {
      _updatePremiumStatus();
    });
  }

  void _updatePremiumStatus() {
    final account = AccountService.to.currentAccount;
    if (account != null) {
      _isPremium.value = account.isPremium && !isPremiumExpired;
      _premiumExpiresAt.value = account.premiumExpiresAt;
    } else {
      _isPremium.value = false;
      _premiumExpiresAt.value = null;
    }
  }

  bool isFeatureAvailable(PremiumFeature feature) {
    // Development mode - allow all features
    if (isDevelopmentMode) {
      return true;
    }

    // If user is premium and not expired, all features are available
    if (_isPremium.value && !isPremiumExpired) {
      return true;
    }

    // Free tier feature availability
    switch (feature) {
      case PremiumFeature.unlimitedTransactions:
      case PremiumFeature.advancedAnalytics:
      case PremiumFeature.multipleBusinessAccounts:
      case PremiumFeature.dataExport:
      case PremiumFeature.cloudSync:
      case PremiumFeature.prioritySupport:
      case PremiumFeature.customCategories:
      case PremiumFeature.recurringTransactions:
      case PremiumFeature.budgetTracking:
      case PremiumFeature.inventoryManagement:
        return false;
    }
  }

  bool canAddTransaction() {
    // Development mode - allow unlimited transactions
    if (isDevelopmentMode) {
      return true;
    }

    if (_isPremium.value && !isPremiumExpired) {
      return true;
    }

    // Check free tier limit
    // This would need to be implemented with actual transaction count
    return true; // For now, allow transactions in free tier
  }

  bool canCreateBusinessAccount() {
    // Development mode - allow business accounts
    if (isDevelopmentMode) {
      return true;
    }

    if (_isPremium.value && !isPremiumExpired) {
      return true;
    }

    // Free tier doesn't allow business accounts
    return false;
  }

  bool canAddCustomCategory() {
    // Development mode - allow custom categories
    if (isDevelopmentMode) {
      return true;
    }

    if (_isPremium.value && !isPremiumExpired) {
      return true;
    }

    // Check free tier limit
    return false; // For now, don't allow custom categories in free tier
  }

  String getFeatureRestrictionMessage(PremiumFeature feature) {
    switch (feature) {
      case PremiumFeature.unlimitedTransactions:
        return 'Upgrade to Premium to add unlimited transactions';
      case PremiumFeature.advancedAnalytics:
        return 'Upgrade to Premium to access advanced analytics and reports';
      case PremiumFeature.multipleBusinessAccounts:
        return 'Upgrade to Premium to create multiple business accounts';
      case PremiumFeature.dataExport:
        return 'Upgrade to Premium to export your data';
      case PremiumFeature.cloudSync:
        return 'Upgrade to Premium to sync your data across devices';
      case PremiumFeature.prioritySupport:
        return 'Upgrade to Premium to get priority support';
      case PremiumFeature.customCategories:
        return 'Upgrade to Premium to create custom categories';
      case PremiumFeature.recurringTransactions:
        return 'Upgrade to Premium to set up recurring transactions';
      case PremiumFeature.budgetTracking:
        return 'Upgrade to Premium to create and track budgets';
      case PremiumFeature.inventoryManagement:
        return 'Upgrade to Premium to access full inventory management';
    }
  }

  List<PremiumFeature> get allPremiumFeatures => PremiumFeature.values;

  List<PremiumFeature> get personalAccountFeatures => [
    PremiumFeature.unlimitedTransactions,
    PremiumFeature.advancedAnalytics,
    PremiumFeature.dataExport,
    PremiumFeature.cloudSync,
    PremiumFeature.customCategories,
    PremiumFeature.recurringTransactions,
    PremiumFeature.budgetTracking,
    PremiumFeature.prioritySupport,
  ];

  List<PremiumFeature> get businessAccountFeatures => [
    PremiumFeature.multipleBusinessAccounts,
    PremiumFeature.inventoryManagement,
    PremiumFeature.advancedAnalytics,
    PremiumFeature.dataExport,
    PremiumFeature.cloudSync,
    PremiumFeature.prioritySupport,
  ];

  Future<bool> upgradeToPremium({
    required Duration duration,
  }) async {
    try {
      final accountId = AccountService.to.currentAccountId;
      if (accountId.isEmpty) return false;

      final expiresAt = DateTime.now().add(duration);
      
      final success = await AccountService.to.upgradeToPremium(accountId, expiresAt);
      if (success) {
        _updatePremiumStatus();
      }
      
      return success;
    } catch (e) {
      print('Error upgrading to premium: $e');
      return false;
    }
  }

  String get premiumStatusText {
    if (!_isPremium.value) {
      return 'Free Plan';
    }
    
    if (isPremiumExpired) {
      return 'Premium Expired';
    }
    
    final daysLeft = _premiumExpiresAt.value!.difference(DateTime.now()).inDays;
    if (daysLeft <= 7) {
      return 'Premium (Expires in $daysLeft days)';
    }
    
    return 'Premium Active';
  }

  bool get shouldShowUpgradePrompt {
    return !_isPremium.value || isPremiumExpired;
  }

  bool get shouldShowExpirationWarning {
    if (!_isPremium.value || _premiumExpiresAt.value == null) return false;
    
    final daysLeft = _premiumExpiresAt.value!.difference(DateTime.now()).inDays;
    return daysLeft <= 7 && daysLeft > 0;
  }

  void showUpgradeDialog() {
    // This would show the upgrade dialog
    // Implementation depends on your UI framework
    Get.toNamed('/subscription'); // Example navigation
  }

  void showFeatureLockedDialog(PremiumFeature feature) {
    // This would show the feature locked dialog
    // Implementation depends on your UI framework
    Get.snackbar(
      'Premium Feature',
      getFeatureRestrictionMessage(feature),
      duration: const Duration(seconds: 3),
    );
  }

  /// Start a premium trial for the current account
  Future<bool> startPremiumTrial() async {
    try {
      // Start a 7-day trial
      const trialDuration = Duration(days: 7);
      return await upgradeToPremium(duration: trialDuration);
    } catch (e) {
      print('Error starting premium trial: $e');
      return false;
    }
  }
}
