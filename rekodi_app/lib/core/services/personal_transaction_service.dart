import 'dart:convert';
import 'package:get/get.dart';
import 'package:drift/drift.dart' as drift;
import 'package:appwrite/appwrite.dart';
import '../database/database.dart';
import '../models/transaction.dart';
import '../config/appwrite_config.dart';
import 'account_service.dart';
import 'appwrite_service.dart';
import 'auth_service.dart';
import 'database_service.dart';

class PersonalTransactionService extends GetxService {
  static PersonalTransactionService get to => Get.find();

  AppDatabase get _database => DatabaseService.to.database;
  late AppwriteService _appwriteService;
  final RxList<PersonalTransaction> _transactions = <PersonalTransaction>[].obs;
  final RxList<PersonalTransaction> _recentTransactions = <PersonalTransaction>[].obs;
  final RxBool _isLoading = false.obs;
  final RxBool _isSyncing = false.obs;
  final Rx<double> _totalIncome = 0.0.obs;
  final Rx<double> _totalExpenses = 0.0.obs;
  final Rx<double> _balance = 0.0.obs;

  List<PersonalTransaction> get transactions => _transactions;
  List<PersonalTransaction> get recentTransactions => _recentTransactions;
  bool get isLoading => _isLoading.value;
  bool get isSyncing => _isSyncing.value;
  double get totalIncome => _totalIncome.value;
  double get totalExpenses => _totalExpenses.value;
  double get balance => _balance.value;
  double get netIncome => _totalIncome.value - _totalExpenses.value;

  @override
  Future<void> onInit() async {
    super.onInit();
    _appwriteService = AppwriteService.to;

    try {
      // Listen to account changes
      ever(AccountService.to.currentAccountObs, (UserAccount? account) {
        if (account != null) {
          _loadTransactionsForCurrentAccount();
        }
      });

      // Load initial data if account is already available
      if (AccountService.to.hasActiveAccount) {
        await _loadTransactionsForCurrentAccount();
      }
    } catch (e) {
      print('Error initializing PersonalTransactionService: $e');
    }
  }

  Future<void> _loadTransactionsForCurrentAccount() async {
    try {
      final accountId = AccountService.to.currentAccountId;
      if (accountId.isEmpty) {
        _isLoading.value = false;
        return;
      }

      _isLoading.value = true;
      try {
        await Future.wait([
          _loadTransactions(accountId),
          _loadRecentTransactions(accountId),
          _calculateTotals(accountId),
        ]);
      } catch (e) {
        print('Error loading transactions: $e');
      } finally {
        _isLoading.value = false;
      }
    } catch (e) {
      print('Error accessing AccountService: $e');
      _isLoading.value = false; // Ensure loading is set to false even on error
    }
  }

  Future<void> _loadTransactions(String accountId) async {
    final transactions = await _database.getTransactionsForAccount(accountId);
    _transactions.assignAll(transactions);
  }

  Future<void> _loadRecentTransactions(String accountId) async {
    final recentTransactions = await _database.getRecentTransactions(accountId, limit: 10);
    _recentTransactions.assignAll(recentTransactions);
  }

  Future<void> _calculateTotals(String accountId) async {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);

    final income = await _database.getTotalIncomeForAccount(
      accountId,
      startDate: startOfMonth,
      endDate: endOfMonth,
    );
    
    final expenses = await _database.getTotalExpensesForAccount(
      accountId,
      startDate: startOfMonth,
      endDate: endOfMonth,
    );

    _totalIncome.value = income;
    _totalExpenses.value = expenses;
    _balance.value = income - expenses;
  }

  Future<bool> addTransaction({
    required String title,
    String? description,
    required double amount,
    required TransactionType type,
    required TransactionCategory category,
    DateTime? date,
    String? paymentMethod,
    double transactionCost = 0.0,
    String? location,
    List<String>? tags,
    String? receiptImageUrl,
    bool isRecurring = false,
    String? recurringPattern,
    DateTime? nextRecurringDate,
  }) async {
    print('🔄 Starting transaction addition process...');

    // Validate input parameters
    if (title.trim().isEmpty) {
      print('❌ Transaction addition failed: Title is empty');
      return false;
    }

    if (amount <= 0) {
      print('❌ Transaction addition failed: Amount must be greater than 0 (received: $amount)');
      return false;
    }

    // Check account access
    String accountId;
    try {
      accountId = AccountService.to.currentAccountId;
      if (accountId.isEmpty) {
        print('❌ Transaction addition failed: No active account found');
        return false;
      }
      print('✅ Active account ID: $accountId');
    } catch (e) {
      print('❌ Error accessing AccountService in addTransaction: $e');
      return false;
    }

    try {
      _isLoading.value = true;
      print('🔄 Setting loading state to true');

      final transactionId = 'txn_${DateTime.now().millisecondsSinceEpoch}';
      print('📝 Generated transaction ID: $transactionId');

      final transaction = PersonalTransactionsCompanion.insert(
        id: transactionId,
        accountId: accountId,
        title: title.trim(),
        description: drift.Value(description?.trim()),
        amount: amount,
        type: type.name,
        category: category.name,
        date: date ?? DateTime.now(),
        paymentMethod: drift.Value(paymentMethod),
        transactionCost: drift.Value(transactionCost),
        location: drift.Value(location?.trim()),
        tags: drift.Value(tags != null ? jsonEncode(tags) : null),
        receiptImageUrl: drift.Value(receiptImageUrl),
        isRecurring: drift.Value(isRecurring),
        recurringPattern: drift.Value(recurringPattern),
        nextRecurringDate: drift.Value(nextRecurringDate),
        createdAt: DateTime.now(),
      );

      print('📊 Transaction object created with type: ${type.name}, category: ${category.name}, amount: $amount');

      // Add transaction to local database
      print('💾 Adding transaction to local database...');
      await _database.addTransaction(transaction);
      print('✅ Transaction added to local database successfully');

      // Verify transaction was added
      final addedTransactions = await _database.getTransactionsForAccount(accountId);
      final newTransaction = addedTransactions.where((t) => t.id == transactionId).firstOrNull;

      if (newTransaction == null) {
        print('❌ Transaction verification failed: Transaction not found in database after insertion');
        return false;
      }
      print('✅ Transaction verified in database: ${newTransaction.id}');

      // Try to sync to Appwrite if online (non-blocking)
      try {
        print('🌐 Attempting to sync transaction to Appwrite...');
        final syncSuccess = await syncTransactionToAppwrite(newTransaction);
        if (syncSuccess) {
          print('✅ Transaction synced to Appwrite successfully');
        } else {
          print('⚠️ Transaction sync to Appwrite failed, but local save was successful');
        }
      } catch (e) {
        print('⚠️ Appwrite sync error (non-critical): $e');
        // Don't fail the entire operation if sync fails
      }

      // Reload transactions to update UI
      print('🔄 Reloading transactions for current account...');
      await _loadTransactionsForCurrentAccount();
      print('✅ Transactions reloaded successfully');

      print('🎉 Transaction addition completed successfully!');
      return true;
    } catch (e, stackTrace) {
      print('❌ Critical error adding transaction: $e');
      print('📍 Stack trace: $stackTrace');
      return false;
    } finally {
      _isLoading.value = false;
      print('🔄 Loading state set to false');
    }
  }

  Future<bool> updateTransaction({
    required String transactionId,
    String? title,
    String? description,
    double? amount,
    TransactionType? type,
    TransactionCategory? category,
    DateTime? date,
    String? paymentMethod,
    double? transactionCost,
    String? location,
    List<String>? tags,
    String? receiptImageUrl,
    bool? isRecurring,
    String? recurringPattern,
    DateTime? nextRecurringDate,
  }) async {
    try {
      _isLoading.value = true;
      
      final updates = PersonalTransactionsCompanion(
        title: title != null ? drift.Value(title) : const drift.Value.absent(),
        description: description != null ? drift.Value(description) : const drift.Value.absent(),
        amount: amount != null ? drift.Value(amount) : const drift.Value.absent(),
        type: type != null ? drift.Value(type.name) : const drift.Value.absent(),
        category: category != null ? drift.Value(category.name) : const drift.Value.absent(),
        date: date != null ? drift.Value(date) : const drift.Value.absent(),
        paymentMethod: paymentMethod != null ? drift.Value(paymentMethod) : const drift.Value.absent(),
        transactionCost: transactionCost != null ? drift.Value(transactionCost) : const drift.Value.absent(),
        location: location != null ? drift.Value(location) : const drift.Value.absent(),
        tags: tags != null ? drift.Value(jsonEncode(tags)) : const drift.Value.absent(),
        receiptImageUrl: receiptImageUrl != null ? drift.Value(receiptImageUrl) : const drift.Value.absent(),
        isRecurring: isRecurring != null ? drift.Value(isRecurring) : const drift.Value.absent(),
        recurringPattern: recurringPattern != null ? drift.Value(recurringPattern) : const drift.Value.absent(),
        nextRecurringDate: nextRecurringDate != null ? drift.Value(nextRecurringDate) : const drift.Value.absent(),
        updatedAt: drift.Value(DateTime.now()),
      );

      final success = await _database.updateTransaction(transactionId, updates);
      if (success) {
        await _loadTransactionsForCurrentAccount();
      }
      
      return success;
    } catch (e) {
      print('Error updating transaction: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> deleteTransaction(String transactionId) async {
    try {
      _isLoading.value = true;
      
      final deletedCount = await _database.deleteTransaction(transactionId);
      if (deletedCount > 0) {
        await _loadTransactionsForCurrentAccount();
        return true;
      }
      
      return false;
    } catch (e) {
      print('Error deleting transaction: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<List<PersonalTransaction>> getTransactionsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final accountId = AccountService.to.currentAccountId;
    if (accountId.isEmpty) return [];

    try {
      return await _database.getTransactionsByDateRange(accountId, startDate, endDate);
    } catch (e) {
      print('Error getting transactions by date range: $e');
      return [];
    }
  }

  Future<List<PersonalTransaction>> getTransactionsByCategory(
    TransactionCategory category,
  ) async {
    final accountId = AccountService.to.currentAccountId;
    if (accountId.isEmpty) return [];

    try {
      return _transactions.where((txn) => txn.category == category.name).toList();
    } catch (e) {
      print('Error getting transactions by category: $e');
      return [];
    }
  }

  Future<List<PersonalTransaction>> getTransactionsByType(
    TransactionType type,
  ) async {
    final accountId = AccountService.to.currentAccountId;
    if (accountId.isEmpty) return [];

    try {
      return _transactions.where((txn) => txn.type == type.name).toList();
    } catch (e) {
      print('Error getting transactions by type: $e');
      return [];
    }
  }

  Future<Map<String, double>> getCategoryTotals({
    DateTime? startDate,
    DateTime? endDate,
    TransactionType? type,
  }) async {
    final accountId = AccountService.to.currentAccountId;
    if (accountId.isEmpty) return {};

    try {
      List<PersonalTransaction> filteredTransactions = _transactions;

      if (startDate != null && endDate != null) {
        filteredTransactions = filteredTransactions
            .where((txn) => txn.date.isAfter(startDate) && txn.date.isBefore(endDate))
            .toList();
      }

      if (type != null) {
        filteredTransactions = filteredTransactions
            .where((txn) => txn.type == type.name)
            .toList();
      }

      final Map<String, double> categoryTotals = {};
      for (final transaction in filteredTransactions) {
        categoryTotals[transaction.category] = 
            (categoryTotals[transaction.category] ?? 0) + transaction.amount;
      }

      return categoryTotals;
    } catch (e) {
      print('Error getting category totals: $e');
      return {};
    }
  }

  Future<void> refreshData() async {
    await _loadTransactionsForCurrentAccount();
  }

  List<String> getTagsFromTransaction(PersonalTransaction transaction) {
    if (transaction.tags == null || transaction.tags!.isEmpty) return [];
    
    try {
      final List<dynamic> tagsList = jsonDecode(transaction.tags!);
      return tagsList.cast<String>();
    } catch (e) {
      print('Error parsing tags: $e');
      return [];
    }
  }

  Future<List<String>> getAllTags() async {
    final Set<String> allTags = {};

    for (final transaction in _transactions) {
      final tags = getTagsFromTransaction(transaction);
      allTags.addAll(tags);
    }

    return allTags.toList()..sort();
  }

  // Date-based filtering methods
  Future<List<PersonalTransaction>> getTransactionsForToday() async {
    final now = DateTime.now();
    final startOfDay = DateTime(now.year, now.month, now.day);
    final endOfDay = DateTime(now.year, now.month, now.day, 23, 59, 59);

    return await getTransactionsByDateRange(startOfDay, endOfDay);
  }

  Future<List<PersonalTransaction>> getTransactionsForWeek([DateTime? date]) async {
    final targetDate = date ?? DateTime.now();
    final startOfWeek = targetDate.subtract(Duration(days: targetDate.weekday - 1));
    final startOfWeekDay = DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day);
    final endOfWeekDay = DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day + 6, 23, 59, 59);

    return await getTransactionsByDateRange(startOfWeekDay, endOfWeekDay);
  }

  Future<List<PersonalTransaction>> getTransactionsForMonth([DateTime? date]) async {
    final targetDate = date ?? DateTime.now();
    final startOfMonth = DateTime(targetDate.year, targetDate.month, 1);
    final endOfMonth = DateTime(targetDate.year, targetDate.month + 1, 0, 23, 59, 59);

    return await getTransactionsByDateRange(startOfMonth, endOfMonth);
  }

  Future<List<PersonalTransaction>> getTransactionsForYear([DateTime? date]) async {
    final targetDate = date ?? DateTime.now();
    final startOfYear = DateTime(targetDate.year, 1, 1);
    final endOfYear = DateTime(targetDate.year, 12, 31, 23, 59, 59);

    return await getTransactionsByDateRange(startOfYear, endOfYear);
  }

  // Summary methods for different time periods
  Future<Map<String, double>> getTodaySummary() async {
    final transactions = await getTransactionsForToday();
    return _calculateSummaryFromTransactions(transactions);
  }

  Future<Map<String, double>> getWeekSummary([DateTime? date]) async {
    final transactions = await getTransactionsForWeek(date);
    return _calculateSummaryFromTransactions(transactions);
  }

  Future<Map<String, double>> getMonthSummary([DateTime? date]) async {
    final transactions = await getTransactionsForMonth(date);
    return _calculateSummaryFromTransactions(transactions);
  }

  Future<Map<String, double>> getYearSummary([DateTime? date]) async {
    final transactions = await getTransactionsForYear(date);
    return _calculateSummaryFromTransactions(transactions);
  }

  Map<String, double> _calculateSummaryFromTransactions(List<PersonalTransaction> transactions) {
    double income = 0.0;
    double expenses = 0.0;

    for (final transaction in transactions) {
      if (transaction.type == TransactionType.income.name) {
        income += transaction.amount;
      } else if (transaction.type == TransactionType.expense.name) {
        expenses += transaction.amount;
      }
    }

    return {
      'income': income,
      'expenses': expenses,
      'balance': income - expenses,
      'count': transactions.length.toDouble(),
    };
  }

  // Appwrite sync methods
  Future<bool> syncTransactionToAppwrite(PersonalTransaction transaction) async {
    print('🌐 Starting Appwrite sync for transaction: ${transaction.id}');

    if (!_appwriteService.isOnline) {
      print('⚠️ Appwrite sync skipped: Service is offline');
      return false;
    }

    if (!_appwriteService.isInitialized) {
      print('⚠️ Appwrite sync skipped: Service not initialized');
      return false;
    }

    // Check if user is authenticated
    try {
      final authService = Get.find<AuthService>();
      if (!authService.isAuthenticated) {
        print('⚠️ Appwrite sync skipped: User not authenticated');
        return false;
      }
    } catch (e) {
      print('⚠️ Appwrite sync skipped: AuthService not available');
      return false;
    }

    try {
      _isSyncing.value = true;
      print('🔄 Setting sync state to true');

      final data = {
        'accountId': transaction.accountId,
        'title': transaction.title,
        'description': transaction.description,
        'amount': transaction.amount,
        'type': transaction.type,
        'category': transaction.category,
        'date': transaction.date.toIso8601String(),
        'paymentMethod': transaction.paymentMethod,
        'transactionCost': transaction.transactionCost,
        'location': transaction.location,
        'tags': transaction.tags,
        'receiptImageUrl': transaction.receiptImageUrl,
        'isRecurring': transaction.isRecurring,
        'recurringPattern': transaction.recurringPattern,
        'nextRecurringDate': transaction.nextRecurringDate?.toIso8601String(),
        'createdAt': transaction.createdAt.toIso8601String(),
        'updatedAt': transaction.updatedAt?.toIso8601String(),
      };

      print('📤 Sending transaction data to Appwrite...');
      print('📊 Data payload: ${data.keys.join(', ')}');

      // Use the createTransaction method from AppwriteService which handles permissions
      final result = await _appwriteService.createTransaction(data);

      if (result != null) {
        print('✅ Transaction synced to Appwrite successfully');

        // Mark transaction as synced in local database
        await _database.updateTransaction(transaction.id, PersonalTransactionsCompanion(
          isSynced: const drift.Value(true),
          updatedAt: drift.Value(DateTime.now()),
        ));

        return true;
      } else {
        print('❌ Failed to sync transaction to Appwrite: No result returned');
        return false;
      }
    } catch (e, stackTrace) {
      print('❌ Error syncing transaction to Appwrite: $e');
      print('📍 Stack trace: $stackTrace');

      // Check if it's a duplicate document error (which is actually OK)
      if (e.toString().contains('Document with the requested ID already exists')) {
        print('ℹ️ Transaction already exists in Appwrite, treating as success');

        // Mark transaction as synced in local database
        await _database.updateTransaction(transaction.id, PersonalTransactionsCompanion(
          isSynced: const drift.Value(true),
          updatedAt: drift.Value(DateTime.now()),
        ));

        return true;
      }

      return false;
    } finally {
      _isSyncing.value = false;
      print('🔄 Sync state set to false');
    }
  }

  Future<void> syncAllTransactionsToAppwrite() async {
    if (!_appwriteService.isOnline) return;

    try {
      _isSyncing.value = true;

      for (final transaction in _transactions) {
        await syncTransactionToAppwrite(transaction);
        // Small delay to avoid rate limiting
        await Future.delayed(const Duration(milliseconds: 100));
      }
    } catch (e) {
      print('Error syncing all transactions: $e');
    } finally {
      _isSyncing.value = false;
    }
  }

  Future<void> loadTransactionsFromAppwrite() async {
    if (!_appwriteService.isOnline) return;

    final accountId = AccountService.to.currentAccountId;
    if (accountId.isEmpty) return;

    // Check if user is authenticated
    try {
      final authService = Get.find<AuthService>();
      if (!authService.isAuthenticated) {
        print('⚠️ Cannot load transactions from Appwrite: User not authenticated');
        return;
      }
    } catch (e) {
      print('⚠️ Cannot load transactions from Appwrite: AuthService not available');
      return;
    }

    try {
      _isSyncing.value = true;

      final response = await _appwriteService.databases.listDocuments(
        databaseId: AppwriteConfig.databaseId,
        collectionId: AppwriteConfig.transactionsCollectionId,
        queries: [
          Query.equal('accountId', accountId),
          Query.orderDesc('date'),
        ],
      );

      print('📥 Loaded ${response.documents.length} transactions from Appwrite');

      // Sync transactions to local database
      for (final doc in response.documents) {
        await _syncTransactionFromAppwrite(doc.data);
      }

      // Reload local transactions
      await _loadTransactionsForCurrentAccount();
    } catch (e) {
      print('Error loading transactions from Appwrite: $e');
    } finally {
      _isSyncing.value = false;
    }
  }

  Future<void> _syncTransactionFromAppwrite(Map<String, dynamic> data) async {
    try {
      final transaction = PersonalTransactionsCompanion.insert(
        id: data['id'],
        accountId: data['accountId'],
        title: data['title'],
        description: drift.Value(data['description']),
        amount: data['amount'].toDouble(),
        type: data['type'],
        category: data['category'],
        date: DateTime.parse(data['date']),
        paymentMethod: drift.Value(data['paymentMethod']),
        location: drift.Value(data['location']),
        tags: drift.Value(data['tags']),
        receiptImageUrl: drift.Value(data['receiptImageUrl']),
        isRecurring: drift.Value(data['isRecurring'] ?? false),
        recurringPattern: drift.Value(data['recurringPattern']),
        nextRecurringDate: data['nextRecurringDate'] != null
            ? drift.Value(DateTime.parse(data['nextRecurringDate']))
            : const drift.Value.absent(),
        createdAt: DateTime.parse(data['createdAt']),
        updatedAt: data['updatedAt'] != null
            ? drift.Value(DateTime.parse(data['updatedAt']))
            : const drift.Value.absent(),
      );

      await _database.addTransaction(transaction);
    } catch (e) {
      print('Error syncing transaction from Appwrite: $e');
    }
  }
}
