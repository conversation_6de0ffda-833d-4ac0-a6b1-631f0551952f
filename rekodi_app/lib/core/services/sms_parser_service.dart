import 'dart:convert';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/sms_transaction.dart';
import '../models/transaction.dart';
import '../models/sms_pattern.dart';
import '../config/appwrite_config.dart';
import 'appwrite_service.dart';
import 'auth_service.dart';
import 'local_storage_service.dart';

enum SmsParserStatus {
  idle,
  parsing,
  success,
  error,
}

/// SMS Parser Service - Enhanced Implementation
/// Handles SMS reading, parsing, and automatic transaction classification
/// with comprehensive pattern matching for Kenyan financial services
class SmsParserService extends GetxService {
  static SmsParserService get to => Get.find();

  final AppwriteService _appwriteService = Get.find<AppwriteService>();
  final AuthService _authService = Get.find<AuthService>();
  final GetStorage _storage = GetStorage();
  final LocalStorageService _localStorageService = Get.find();

  final RxList<SmsTransaction> _pendingTransactions = <SmsTransaction>[].obs;
  final RxList<SmsTransaction> _parsedTransactions = <SmsTransaction>[].obs;
  final RxList<SmsPattern> _learnedPatterns = <SmsPattern>[].obs;
  final RxList<SmsPattern> _patterns = <SmsPattern>[].obs;
  final RxBool _isListening = false.obs;
  final RxBool _hasPermission = false.obs;
  final Rx<SmsParserStatus> _status = SmsParserStatus.idle.obs;
  final RxInt _totalMessagesParsed = 0.obs;
  final RxInt _successfullyParsed = 0.obs;
  final RxDouble _parsingAccuracy = 0.0.obs;

  List<SmsTransaction> get pendingTransactions => _pendingTransactions;
  List<SmsTransaction> get parsedTransactions => _parsedTransactions;
  List<SmsPattern> get learnedPatterns => _learnedPatterns;
  List<SmsPattern> get patterns => _patterns;
  bool get isListening => _isListening.value;
  bool get hasPermission => _hasPermission.value;
  SmsParserStatus get status => _status.value;
  int get totalMessagesParsed => _totalMessagesParsed.value;
  int get successfullyParsed => _successfullyParsed.value;
  double get parsingAccuracy => _parsingAccuracy.value;

  // Known financial service senders with payment method classification
  final Map<String, String> _senderPaymentMethods = {
    // Mobile Money / Digital Wallets
    'MPESA': 'mobile_money',
    'SAFARICOM': 'mobile_money',
    'AIRTELMONEY': 'mobile_money',
    'ORANGE': 'mobile_money',
    'TKASH': 'mobile_money',

    // Banks
    'KCB-BANK': 'bank_transfer',
    'KCB': 'bank_transfer',
    'EQUITY': 'bank_transfer',
    'COOP-BANK': 'bank_transfer',
    'NCBA': 'bank_transfer',
    'NCBA-BANK': 'bank_transfer',
    'ABSA': 'bank_transfer',
    'STANBIC': 'bank_transfer',
    'CHASE': 'bank_transfer',
    'LOOP': 'bank_transfer',
    'LOOP-BANK': 'bank_transfer',
    'FAMILY-BANK': 'bank_transfer',
    'DTB': 'bank_transfer',
    'STANDARD': 'bank_transfer',
    'BARCLAYS': 'bank_transfer',

    // Card Payments
    'CARD-ALERT': 'card',
    'VISA-ALERT': 'card',
    'MASTERCARD': 'card',
    'VISA': 'card',
    'MASTER': 'card',

    // Digital Payments
    'PAYPAL': 'digital_wallet',
    'PESALINK': 'digital_wallet',
    'JENGA': 'digital_wallet',

    // Crypto (if any SMS notifications)
    'BINANCE': 'crypto',
    'COINBASE': 'crypto',

    // Utilities
    'KPLC': 'utility',
    'NAIROBI-WTR': 'utility',
    'NAIROBI-WATER': 'utility',
  };

  // Get list of known senders
  List<String> get _knownSenders => _senderPaymentMethods.keys.toList();

  @override
  Future<void> onInit() async {
    super.onInit();
    await _checkPermissions();
    await _loadLearnedPatterns();
    _loadPatterns();
    _loadParsingStats();
    print('SMS Parser Service initialized with ${_patterns.length} patterns');
  }

  /// Load predefined SMS patterns for different financial services
  void _loadPatterns() {
    final savedPatterns = _storage.read<List>('sms_patterns');
    if (savedPatterns != null) {
      _patterns.assignAll(
        savedPatterns.map((pattern) => SmsPattern.fromJson(pattern)).toList(),
      );
    } else {
      _initializeDefaultPatterns();
    }
  }

  /// Load parsing statistics
  void _loadParsingStats() {
    _totalMessagesParsed.value = _storage.read('total_messages_parsed') ?? 0;
    _successfullyParsed.value = _storage.read('successfully_parsed') ?? 0;
    _updateParsingAccuracy();
  }

  /// Initialize default SMS patterns for Kenyan financial services
  void _initializeDefaultPatterns() {
    final defaultPatterns = [
      // M-Pesa patterns
      SmsPattern(
        id: 'mpesa_received',
        name: 'M-Pesa Money Received',
        sender: 'MPESA',
        pattern: r'([A-Z0-9]+) Confirmed\. You have received Ksh([\d,]+\.\d{2}) from (.+?) on (\d{1,2}\/\d{1,2}\/\d{2}) at (\d{1,2}:\d{2} [AP]M)',
        transactionType: TransactionType.income,
        amountGroup: 2,
        senderGroup: 3,
        dateGroup: 4,
        timeGroup: 5,
        referenceGroup: 1,
        category: 'Mobile Money',
        description: 'M-Pesa payment received',
      ),

      SmsPattern(
        id: 'mpesa_sent',
        name: 'M-Pesa Money Sent',
        sender: 'MPESA',
        pattern: r'([A-Z0-9]+) Confirmed\. Ksh([\d,]+\.\d{2}) sent to (.+?) on (\d{1,2}\/\d{1,2}\/\d{2}) at (\d{1,2}:\d{2} [AP]M)',
        transactionType: TransactionType.expense,
        amountGroup: 2,
        recipientGroup: 3,
        dateGroup: 4,
        timeGroup: 5,
        referenceGroup: 1,
        category: 'Mobile Money',
        description: 'M-Pesa payment sent',
      ),

      // M-Pesa Buy Goods
      SmsPattern(
        id: 'mpesa_buy_goods',
        name: 'M-Pesa Buy Goods',
        sender: 'MPESA',
        pattern: r'([A-Z0-9]+) Confirmed\. Ksh([\d,]+\.\d{2}) paid to (.+?)\. on (\d{1,2}\/\d{1,2}\/\d{2}) at (\d{1,2}:\d{2} [AP]M)',
        transactionType: TransactionType.expense,
        amountGroup: 2,
        recipientGroup: 3,
        dateGroup: 4,
        timeGroup: 5,
        referenceGroup: 1,
        category: 'Shopping',
        description: 'M-Pesa buy goods payment',
      ),

      // M-Pesa Withdraw
      SmsPattern(
        id: 'mpesa_withdraw',
        name: 'M-Pesa Cash Withdrawal',
        sender: 'MPESA',
        pattern: r'([A-Z0-9]+) Confirmed\. Ksh([\d,]+\.\d{2}) withdrawn from (.+?) on (\d{1,2}\/\d{1,2}\/\d{2}) at (\d{1,2}:\d{2} [AP]M)',
        transactionType: TransactionType.expense,
        amountGroup: 2,
        recipientGroup: 3,
        dateGroup: 4,
        timeGroup: 5,
        referenceGroup: 1,
        category: 'Cash Withdrawal',
        description: 'M-Pesa cash withdrawal',
      ),

      // KCB Bank patterns
      SmsPattern(
        id: 'kcb_debit',
        name: 'KCB Account Debit',
        sender: 'KCB-BANK',
        pattern: r'Dear Customer, your A\/C (.+?) has been debited with KES ([\d,]+\.\d{2}) on (\d{2}-\w{3}-\d{4}) (.+?) Bal: KES ([\d,]+\.\d{2})',
        transactionType: TransactionType.expense,
        accountGroup: 1,
        amountGroup: 2,
        dateGroup: 3,
        descriptionGroup: 4,
        balanceGroup: 5,
        category: 'Bank Transaction',
        description: 'Bank account debit',
      ),

      SmsPattern(
        id: 'kcb_credit',
        name: 'KCB Account Credit',
        sender: 'KCB-BANK',
        pattern: r'Dear Customer, your A\/C (.+?) has been credited with KES ([\d,]+\.\d{2}) on (\d{2}-\w{3}-\d{4}) (.+?) Bal: KES ([\d,]+\.\d{2})',
        transactionType: TransactionType.income,
        accountGroup: 1,
        amountGroup: 2,
        dateGroup: 3,
        descriptionGroup: 4,
        balanceGroup: 5,
        category: 'Bank Transaction',
        description: 'Bank account credit',
      ),

      // NCBA Bank patterns
      SmsPattern(
        id: 'ncba_debit',
        name: 'NCBA Account Debit',
        sender: 'NCBA',
        pattern: r'NCBA: Your account (.+?) has been debited KES ([\d,]+\.\d{2}) on (\d{2}\/\d{2}\/\d{4}) (.+?) Balance: KES ([\d,]+\.\d{2})',
        transactionType: TransactionType.expense,
        accountGroup: 1,
        amountGroup: 2,
        dateGroup: 3,
        descriptionGroup: 4,
        balanceGroup: 5,
        category: 'Bank Transaction',
        description: 'NCBA account debit',
      ),

      SmsPattern(
        id: 'ncba_credit',
        name: 'NCBA Account Credit',
        sender: 'NCBA',
        pattern: r'NCBA: Your account (.+?) has been credited KES ([\d,]+\.\d{2}) on (\d{2}\/\d{2}\/\d{4}) (.+?) Balance: KES ([\d,]+\.\d{2})',
        transactionType: TransactionType.income,
        accountGroup: 1,
        amountGroup: 2,
        dateGroup: 3,
        descriptionGroup: 4,
        balanceGroup: 5,
        category: 'Bank Transaction',
        description: 'NCBA account credit',
      ),

      // Loop Bank patterns
      SmsPattern(
        id: 'loop_debit',
        name: 'Loop Account Debit',
        sender: 'LOOP',
        pattern: r'Loop: Account (.+?) debited KES ([\d,]+\.\d{2}) on (\d{2}-\d{2}-\d{4}) (.+?) Bal: KES ([\d,]+\.\d{2})',
        transactionType: TransactionType.expense,
        accountGroup: 1,
        amountGroup: 2,
        dateGroup: 3,
        descriptionGroup: 4,
        balanceGroup: 5,
        category: 'Bank Transaction',
        description: 'Loop account debit',
      ),

      SmsPattern(
        id: 'loop_credit',
        name: 'Loop Account Credit',
        sender: 'LOOP',
        pattern: r'Loop: Account (.+?) credited KES ([\d,]+\.\d{2}) on (\d{2}-\d{2}-\d{4}) (.+?) Bal: KES ([\d,]+\.\d{2})',
        transactionType: TransactionType.income,
        accountGroup: 1,
        amountGroup: 2,
        dateGroup: 3,
        descriptionGroup: 4,
        balanceGroup: 5,
        category: 'Bank Transaction',
        description: 'Loop account credit',
      ),
    ];

    _patterns.assignAll(defaultPatterns);
    _savePatterns();
  }

  /// Parse a single SMS message
  SmsTransaction? parseSmsMessage(String message, String sender, DateTime timestamp) {
    _status.value = SmsParserStatus.parsing;

    try {
      // Find matching pattern
      final pattern = _findMatchingPattern(message, sender);
      if (pattern == null) {
        _status.value = SmsParserStatus.error;
        return null;
      }

      // Extract transaction data using the pattern
      final transaction = _extractTransactionData(message, pattern, timestamp);
      if (transaction != null) {
        _parsedTransactions.add(transaction);
        _successfullyParsed.value++;
        _updateParsingAccuracy();
        _status.value = SmsParserStatus.success;
      } else {
        _status.value = SmsParserStatus.error;
      }

      _totalMessagesParsed.value++;
      _saveParsingStats();

      return transaction;
    } catch (e) {
      _status.value = SmsParserStatus.error;
      _totalMessagesParsed.value++;
      _saveParsingStats();
      return null;
    }
  }

  /// Find matching pattern for SMS
  SmsPattern? _findMatchingPattern(String message, String sender) {
    // First try exact sender match
    for (final pattern in _patterns) {
      if (pattern.matchesSender(sender) && pattern.matchesMessage(message)) {
        return pattern;
      }
    }

    // Try learned patterns
    for (final pattern in _learnedPatterns) {
      if (pattern.matchesSender(sender) && pattern.matchesMessage(message)) {
        return pattern;
      }
    }

    return null;
  }

  /// Extract transaction data from SMS using pattern
  SmsTransaction? _extractTransactionData(String message, SmsPattern pattern, DateTime timestamp) {
    try {
      final match = pattern.extractMatch(message);
      if (match == null) return null;

      // Extract amount
      final amountStr = match.group(pattern.amountGroup)?.replaceAll(',', '') ?? '0';
      final amount = double.tryParse(amountStr) ?? 0.0;

      // Extract other fields
      final reference = pattern.referenceGroup != null ? match.group(pattern.referenceGroup!) : null;
      final sender = pattern.senderGroup != null ? match.group(pattern.senderGroup!) : null;
      final recipient = pattern.recipientGroup != null ? match.group(pattern.recipientGroup!) : null;
      final merchant = pattern.merchantGroup != null ? match.group(pattern.merchantGroup!) : null;
      final location = pattern.locationGroup != null ? match.group(pattern.locationGroup!) : null;
      final account = pattern.accountGroup != null ? match.group(pattern.accountGroup!) : null;
      final balance = pattern.balanceGroup != null ?
          double.tryParse(match.group(pattern.balanceGroup!)?.replaceAll(',', '') ?? '0') : null;

      // Extract date and time if available
      DateTime? transactionDate;
      if (pattern.dateGroup != null) {
        final dateStr = match.group(pattern.dateGroup!);
        transactionDate = _parseDate(dateStr);
      }
      transactionDate ??= timestamp;

      return SmsTransaction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        rawMessage: message,
        sender: pattern.sender,
        transactionType: pattern.transactionType.name,
        amount: amount,
        category: pattern.category,
        description: pattern.description,
        referenceNumber: reference,
        merchantName: merchant,
        accountNumber: account,
        balance: balance,
        confidenceScore: _calculateConfidence(match, pattern),
        createdAt: DateTime.now(),
        isProcessed: false,
        receivedAt: timestamp,
        parsedData: {
          'paymentMethod': getPaymentMethod(pattern.sender),
          'senderType': _getSenderType(pattern.sender),
        },
      );
    } catch (e) {
      print('Error extracting transaction data: $e');
      return null;
    }
  }

  /// Check and request SMS permissions
  Future<void> _checkPermissions() async {
    try {
      final status = await Permission.sms.status;
      if (status.isGranted) {
        _hasPermission.value = true;
      } else {
        final result = await Permission.sms.request();
        _hasPermission.value = result.isGranted;
      }
    } catch (e) {
      print('Error checking SMS permissions: $e');
      _hasPermission.value = false;
    }
  }

  /// Start listening for new SMS messages (stub)
  Future<void> startListening() async {
    if (!_hasPermission.value) {
      await _checkPermissions();
    }
    
    if (_hasPermission.value) {
      _isListening.value = true;
      print('SMS listening started (stub mode)');
    }
  }

  /// Stop listening for SMS messages
  void stopListening() {
    _isListening.value = false;
    print('SMS listening stopped');
  }

  /// Manually add a test SMS transaction (for development)
  void addTestTransaction(String rawMessage, String sender) {
    try {
      final smsTransaction = SmsTransaction.fromRawSms(
        rawMessage: rawMessage,
        sender: sender,
        receivedAt: DateTime.now(),
        userId: _authService.currentUser?.id,
      );
      
      _pendingTransactions.add(smsTransaction);
      print('Test SMS transaction added: $rawMessage');
    } catch (e) {
      print('Error adding test transaction: $e');
    }
  }

  /// Load learned patterns from storage
  Future<void> _loadLearnedPatterns() async {
    try {
      // TODO: Load from Appwrite database
      print('Learned patterns loaded (stub)');
    } catch (e) {
      print('Error loading learned patterns: $e');
    }
  }

  /// Save learned pattern
  Future<void> savePattern(SmsPattern pattern) async {
    try {
      _learnedPatterns.add(pattern);
      // TODO: Save to Appwrite database
      print('Pattern saved: ${pattern.pattern}');
    } catch (e) {
      print('Error saving pattern: $e');
    }
  }

  /// Check if SMS is from a financial service
  bool _isFinancialSms(String sender, String message) {
    // Check known senders
    for (final knownSender in _knownSenders) {
      if (sender.toUpperCase().contains(knownSender)) {
        return true;
      }
    }

    // Check message content for financial keywords
    final financialKeywords = [
      'transaction',
      'payment',
      'transfer',
      'balance',
      'deposit',
      'withdrawal',
      'mpesa',
      'received',
      'sent',
      'ksh',
      'usd',
      'eur',
    ];

    final lowerMessage = message.toLowerCase();
    return financialKeywords.any((keyword) => lowerMessage.contains(keyword));
  }

  /// Train the system with user feedback
  Future<void> trainWithUserFeedback(
    String transactionId,
    String category,
    bool isCorrect,
  ) async {
    try {
      // TODO: Implement ML training logic
      print('Training with feedback: $transactionId -> $category (correct: $isCorrect)');
    } catch (e) {
      print('Error training with feedback: $e');
    }
  }

  /// Get confidence score for auto-classification
  double getConfidenceScore(SmsTransaction transaction) {
    // TODO: Implement ML confidence scoring
    return 0.8; // Placeholder
  }

  /// Clear pending transactions
  void clearPendingTransactions() {
    _pendingTransactions.clear();
  }

  /// Get pending transactions count
  int get pendingCount => _pendingTransactions.length;

  // Helper methods for parsing

  /// Parse date from various formats
  DateTime? _parseDate(String? dateStr) {
    if (dateStr == null) return null;

    try {
      // Try different date formats
      final formats = [
        RegExp(r'(\d{1,2})\/(\d{1,2})\/(\d{2})'), // DD/MM/YY
        RegExp(r'(\d{1,2})\/(\d{1,2})\/(\d{4})'), // DD/MM/YYYY
        RegExp(r'(\d{2})-(\w{3})-(\d{4})'), // DD-MMM-YYYY
      ];

      for (final format in formats) {
        final match = format.firstMatch(dateStr);
        if (match != null) {
          if (format.pattern.contains(r'\w{3}')) {
            // Handle DD-MMM-YYYY format
            final day = int.parse(match.group(1)!);
            final monthStr = match.group(2)!;
            final year = int.parse(match.group(3)!);
            final month = _parseMonth(monthStr);
            if (month != null) {
              return DateTime(year, month, day);
            }
          } else {
            // Handle DD/MM/YY or DD/MM/YYYY format
            final day = int.parse(match.group(1)!);
            final month = int.parse(match.group(2)!);
            var year = int.parse(match.group(3)!);

            // Convert 2-digit year to 4-digit
            if (year < 100) {
              year += (year < 50) ? 2000 : 1900;
            }

            return DateTime(year, month, day);
          }
        }
      }
    } catch (e) {
      print('Error parsing date: $e');
    }

    return null;
  }

  /// Parse month abbreviation to number
  int? _parseMonth(String monthStr) {
    const months = {
      'jan': 1, 'feb': 2, 'mar': 3, 'apr': 4, 'may': 5, 'jun': 6,
      'jul': 7, 'aug': 8, 'sep': 9, 'oct': 10, 'nov': 11, 'dec': 12,
    };
    return months[monthStr.toLowerCase()];
  }

  /// Calculate confidence score for parsed transaction
  double _calculateConfidence(RegExpMatch match, SmsPattern pattern) {
    double confidence = pattern.confidence; // Base confidence from pattern

    // Increase confidence based on extracted fields
    if (match.group(pattern.amountGroup) != null) confidence += 0.05;
    if (pattern.referenceGroup != null && match.group(pattern.referenceGroup!) != null) confidence += 0.03;
    if (pattern.dateGroup != null && match.group(pattern.dateGroup!) != null) confidence += 0.02;

    return confidence.clamp(0.0, 1.0);
  }

  /// Update parsing accuracy statistics
  void _updateParsingAccuracy() {
    if (_totalMessagesParsed.value > 0) {
      _parsingAccuracy.value = _successfullyParsed.value / _totalMessagesParsed.value;
    }
  }

  /// Save patterns to storage
  void _savePatterns() {
    final patternsJson = _patterns.map((pattern) => pattern.toJson()).toList();
    _storage.write('sms_patterns', patternsJson);
  }

  /// Save parsing statistics
  void _saveParsingStats() {
    _storage.write('total_messages_parsed', _totalMessagesParsed.value);
    _storage.write('successfully_parsed', _successfullyParsed.value);
  }

  /// Add custom pattern
  void addCustomPattern(SmsPattern pattern) {
    _patterns.add(pattern);
    _savePatterns();
  }

  /// Remove pattern
  void removePattern(String patternId) {
    _patterns.removeWhere((pattern) => pattern.id == patternId);
    _savePatterns();
  }

  /// Update pattern
  void updatePattern(SmsPattern updatedPattern) {
    final index = _patterns.indexWhere((pattern) => pattern.id == updatedPattern.id);
    if (index != -1) {
      _patterns[index] = updatedPattern;
      _savePatterns();
    }
  }

  /// Get parsing statistics
  Map<String, dynamic> getParsingStats() {
    return {
      'total_messages': _totalMessagesParsed.value,
      'successfully_parsed': _successfullyParsed.value,
      'accuracy': _parsingAccuracy.value,
      'patterns_count': _patterns.length,
      'learned_patterns_count': _learnedPatterns.length,
      'recent_transactions': _parsedTransactions.take(10).toList(),
    };
  }

  /// Clear all parsed transactions
  void clearParsedTransactions() {
    _parsedTransactions.clear();
  }

  /// Reset parsing statistics
  void resetStats() {
    _totalMessagesParsed.value = 0;
    _successfullyParsed.value = 0;
    _parsingAccuracy.value = 0.0;
    _saveParsingStats();
  }

  /// Test a pattern against a sample message
  bool testPattern(SmsPattern pattern, String message) {
    try {
      final match = pattern.extractMatch(message);
      return match != null && match.group(pattern.amountGroup) != null;
    } catch (e) {
      return false;
    }
  }

  /// Check if sender is a known financial service
  bool isFinancialSender(String sender) {
    final senderLower = sender.toLowerCase();
    return _knownSenders.any((knownSender) =>
        senderLower.contains(knownSender.toLowerCase()));
  }

  /// Get payment method for a sender
  String getPaymentMethod(String sender) {
    final senderUpper = sender.toUpperCase();

    // Check exact matches first
    if (_senderPaymentMethods.containsKey(senderUpper)) {
      return _senderPaymentMethods[senderUpper]!;
    }

    // Check partial matches
    for (final entry in _senderPaymentMethods.entries) {
      if (senderUpper.contains(entry.key) || entry.key.contains(senderUpper)) {
        return entry.value;
      }
    }

    // Default to cash if unknown
    return 'cash';
  }

  /// Get all supported payment methods
  List<String> get supportedPaymentMethods => [
    'mobile_money',
    'bank_transfer',
    'card',
    'digital_wallet',
    'crypto',
    'cash',
    'utility',
  ];

  /// Get senders by payment method
  List<String> getSendersByPaymentMethod(String paymentMethod) {
    return _senderPaymentMethods.entries
        .where((entry) => entry.value == paymentMethod)
        .map((entry) => entry.key)
        .toList();
  }

  /// Get sender type for classification
  String _getSenderType(String sender) {
    final paymentMethod = getPaymentMethod(sender);
    switch (paymentMethod) {
      case 'mobile_money':
        return 'Mobile Money';
      case 'bank_transfer':
        return 'Bank';
      case 'card':
        return 'Card Payment';
      case 'digital_wallet':
        return 'Digital Wallet';
      case 'crypto':
        return 'Cryptocurrency';
      case 'utility':
        return 'Utility';
      default:
        return 'Other';
    }
  }
}
