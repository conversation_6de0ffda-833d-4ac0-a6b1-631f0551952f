import 'package:get/get.dart';
import '../database/database.dart';

class DatabaseService extends GetxService {
  static DatabaseService get to => Get.find();

  static AppDatabase? _instance;
  AppDatabase get database => _instance!;

  @override
  Future<void> onInit() async {
    super.onInit();
    // Use singleton pattern to prevent multiple database instances
    _instance ??= AppDatabase();
  }

  @override
  void onClose() {
    _instance?.close();
    _instance = null;
    super.onClose();
  }
}
