import 'package:get/get.dart';
import '../config/appwrite_config.dart';
import 'security_service.dart';

/// Service for managing API keys securely
class ApiKeyService extends GetxService {
  static ApiKeyService get to => Get.find();
  
  SecurityService? _securityService;
  
  final RxBool _isInitialized = false.obs;
  final RxString _currentApiKey = ''.obs;
  
  bool get isInitialized => _isInitialized.value;
  bool get hasValidApiKey => _currentApiKey.value.isNotEmpty;
  
  @override
  Future<void> onInit() async {
    super.onInit();
    _securityService ??= Get.find<SecurityService>();
    await _initializeApiKey();
  }
  
  /// Initialize API key from secure storage or configuration
  Future<void> _initializeApiKey() async {
    try {
      if (_securityService == null) {
        throw Exception('SecurityService not initialized');
      }

      // First try to get from secure storage
      String? storedApiKey = await _securityService!.secureRetrieve<String>('appwrite_api_key');

      if (storedApiKey != null && storedApiKey.isNotEmpty) {
        _currentApiKey.value = storedApiKey;
      } else {
        // Fall back to configuration
        _currentApiKey.value = AppwriteConfig.apiKey;

        // Store it securely for future use
        await _securityService!.secureStore('appwrite_api_key', _currentApiKey.value);
      }
      
      _isInitialized.value = true;
      print('API Key Service initialized successfully');
    } catch (e) {
      print('Error initializing API key service: $e');
      _isInitialized.value = false;
    }
  }
  
  /// Get the current API key
  String get apiKey => _currentApiKey.value;
  
  /// Update the API key
  Future<bool> updateApiKey(String newApiKey) async {
    try {
      if (newApiKey.isEmpty) {
        throw Exception('API key cannot be empty');
      }
      
      // Validate API key format (basic validation)
      if (!_isValidApiKeyFormat(newApiKey)) {
        throw Exception('Invalid API key format');
      }
      
      // Store securely
      if (_securityService == null) {
        throw Exception('SecurityService not initialized');
      }
      await _securityService!.secureStore('appwrite_api_key', newApiKey);
      _currentApiKey.value = newApiKey;
      
      Get.snackbar(
        'Success',
        'API key updated successfully',
        snackPosition: SnackPosition.BOTTOM,
      );
      
      return true;
    } catch (e) {
      print('Error updating API key: $e');
      Get.snackbar(
        'Error',
        'Failed to update API key: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    }
  }
  
  /// Remove the stored API key
  Future<void> removeApiKey() async {
    try {
      if (_securityService == null) {
        throw Exception('SecurityService not initialized');
      }
      await _securityService!.secureRemove('appwrite_api_key');
      _currentApiKey.value = '';
      
      Get.snackbar(
        'Success',
        'API key removed successfully',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      print('Error removing API key: $e');
    }
  }
  
  /// Get API headers for requests
  Map<String, String> get apiHeaders => {
    'X-Appwrite-Project': AppwriteConfig.projectId,
    'X-Appwrite-Key': apiKey,
    'Content-Type': 'application/json',
  };
  
  /// Get API headers with custom content type
  Map<String, String> getApiHeaders({String contentType = 'application/json'}) => {
    'X-Appwrite-Project': AppwriteConfig.projectId,
    'X-Appwrite-Key': apiKey,
    'Content-Type': contentType,
  };
  
  /// Validate API key format
  bool _isValidApiKeyFormat(String apiKey) {
    // Basic validation - should start with 'standard_' and be long enough
    return apiKey.startsWith('standard_') && apiKey.length > 50;
  }
  
  /// Test API key validity by making a test request
  Future<bool> testApiKey([String? testKey]) async {
    try {
      final keyToTest = testKey ?? apiKey;
      if (keyToTest.isEmpty) return false;
      
      // TODO: Implement actual API test call to Appwrite
      // For now, just validate format
      return _isValidApiKeyFormat(keyToTest);
    } catch (e) {
      print('Error testing API key: $e');
      return false;
    }
  }
  
  /// Get masked API key for display purposes
  String get maskedApiKey {
    if (_currentApiKey.value.isEmpty) return 'Not configured';
    
    final key = _currentApiKey.value;
    if (key.length < 20) return key;
    
    return '${key.substring(0, 10)}...${key.substring(key.length - 10)}';
  }
  
  /// Check if API key is from configuration or custom
  bool get isDefaultApiKey => _currentApiKey.value == AppwriteConfig.apiKey;
  
  /// Get API key source information
  String get apiKeySource => isDefaultApiKey ? 'Configuration' : 'Custom';
}
