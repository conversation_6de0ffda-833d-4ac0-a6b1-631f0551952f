import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'dart:io';
import 'dart:convert';
import '../models/transaction.dart';
import '../models/budget.dart';
import '../models/export_data.dart';
import 'personal_transaction_service.dart';
import 'budget_service.dart';
import 'account_service.dart';

class ExportService extends GetxService {
  static ExportService get to => Get.find();

  final PersonalTransactionService _transactionService = Get.find();
  final BudgetService _budgetService = Get.find();
  final AccountService _accountService = Get.find();

  final RxBool _isExporting = false.obs;
  final RxString _exportProgress = ''.obs;

  bool get isExporting => _isExporting.value;
  String get exportProgress => _exportProgress.value;

  /// Export transactions to CSV
  Future<String?> exportTransactionsToCSV({
    DateTime? startDate,
    DateTime? endDate,
    List<String>? categories,
    String? type,
  }) async {
    try {
      _isExporting.value = true;
      _exportProgress.value = 'Preparing transaction data...';

      final transactions = _getFilteredTransactions(
        startDate: startDate,
        endDate: endDate,
        categories: categories,
        type: type,
      );

      if (transactions.isEmpty) {
        Get.snackbar('No Data', 'No transactions found for the selected criteria');
        return null;
      }

      _exportProgress.value = 'Generating CSV file...';

      final csv = _generateTransactionCSV(transactions);
      final file = await _saveToFile(csv, 'transactions_export.csv');

      _exportProgress.value = 'Export completed';
      
      Get.snackbar(
        'Export Successful',
        'Transactions exported to ${file.path}',
        snackPosition: SnackPosition.BOTTOM,
      );

      return file.path;
    } catch (e) {
      Get.snackbar(
        'Export Error',
        'Failed to export transactions: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
      return null;
    } finally {
      _isExporting.value = false;
      _exportProgress.value = '';
    }
  }

  /// Export transactions to PDF
  Future<String?> exportTransactionsToPDF({
    DateTime? startDate,
    DateTime? endDate,
    List<String>? categories,
    String? type,
  }) async {
    try {
      _isExporting.value = true;
      _exportProgress.value = 'Preparing transaction data...';

      final transactions = _getFilteredTransactions(
        startDate: startDate,
        endDate: endDate,
        categories: categories,
        type: type,
      );

      if (transactions.isEmpty) {
        Get.snackbar('No Data', 'No transactions found for the selected criteria');
        return null;
      }

      _exportProgress.value = 'Generating PDF report...';

      final pdf = await _generateTransactionPDF(transactions, startDate, endDate);
      final file = await _savePDFToFile(pdf, 'transactions_report.pdf');

      _exportProgress.value = 'Export completed';
      
      Get.snackbar(
        'Export Successful',
        'Transaction report exported to ${file.path}',
        snackPosition: SnackPosition.BOTTOM,
      );

      return file.path;
    } catch (e) {
      Get.snackbar(
        'Export Error',
        'Failed to export PDF: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
      return null;
    } finally {
      _isExporting.value = false;
      _exportProgress.value = '';
    }
  }

  /// Export budgets to CSV
  Future<String?> exportBudgetsToCSV() async {
    try {
      _isExporting.value = true;
      _exportProgress.value = 'Preparing budget data...';

      final budgets = _budgetService.budgets;

      if (budgets.isEmpty) {
        Get.snackbar('No Data', 'No budgets found to export');
        return null;
      }

      _exportProgress.value = 'Generating CSV file...';

      final csv = _generateBudgetCSV(budgets);
      final file = await _saveToFile(csv, 'budgets_export.csv');

      _exportProgress.value = 'Export completed';
      
      Get.snackbar(
        'Export Successful',
        'Budgets exported to ${file.path}',
        snackPosition: SnackPosition.BOTTOM,
      );

      return file.path;
    } catch (e) {
      Get.snackbar(
        'Export Error',
        'Failed to export budgets: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
      return null;
    } finally {
      _isExporting.value = false;
      _exportProgress.value = '';
    }
  }

  /// Create complete backup of all data
  Future<String?> createCompleteBackup() async {
    try {
      _isExporting.value = true;
      _exportProgress.value = 'Collecting all data...';

      // Convert PersonalTransaction to Transaction
      final transactions = _transactionService.transactions.map((pt) => Transaction(
        id: pt.id,
        accountId: pt.accountId,
        title: pt.title,
        description: pt.description,
        amount: pt.amount,
        type: TransactionType.values.firstWhere(
          (e) => e.name == pt.type,
          orElse: () => TransactionType.expense,
        ),
        category: TransactionCategory.values.firstWhere(
          (e) => e.name == pt.category,
          orElse: () => TransactionCategory.other,
        ),
        date: pt.date,
        paymentMethod: pt.paymentMethod,
        location: pt.location,
        tags: pt.tags != null && pt.tags!.isNotEmpty
            ? List<String>.from(jsonDecode(pt.tags!))
            : null,
        createdAt: pt.createdAt,
        updatedAt: pt.updatedAt,
      )).toList();

      final exportData = ExportData(
        transactions: transactions,
        budgets: _budgetService.budgets,
        accountId: _accountService.currentAccount?.id,
        exportDate: DateTime.now(),
        version: '1.0',
      );

      _exportProgress.value = 'Creating backup file...';

      final json = jsonEncode(exportData.toJson());
      final file = await _saveToFile(json, 'rekodi_backup.json');

      _exportProgress.value = 'Backup completed';
      
      Get.snackbar(
        'Backup Successful',
        'Complete backup created at ${file.path}',
        snackPosition: SnackPosition.BOTTOM,
      );

      return file.path;
    } catch (e) {
      Get.snackbar(
        'Backup Error',
        'Failed to create backup: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
      return null;
    } finally {
      _isExporting.value = false;
      _exportProgress.value = '';
    }
  }

  /// Share exported file
  Future<void> shareFile(String filePath) async {
    try {
      await Share.shareXFiles([XFile(filePath)]);
    } catch (e) {
      Get.snackbar(
        'Share Error',
        'Failed to share file: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// Import data from backup file
  Future<bool> importFromBackup(String filePath) async {
    try {
      _isExporting.value = true;
      _exportProgress.value = 'Reading backup file...';

      final file = File(filePath);
      if (!await file.exists()) {
        Get.snackbar('Error', 'Backup file not found');
        return false;
      }

      final content = await file.readAsString();
      final json = jsonDecode(content);
      final exportData = ExportData.fromJson(json);

      _exportProgress.value = 'Importing transactions...';
      
      // Import transactions
      for (final transaction in exportData.transactions) {
        await _transactionService.addTransaction(
          title: transaction.title,
          description: transaction.description,
          amount: transaction.amount,
          type: transaction.type,
          category: transaction.category,
          date: transaction.date,
          paymentMethod: transaction.paymentMethod,
          location: transaction.location,
          tags: transaction.tags,
        );
      }

      _exportProgress.value = 'Importing budgets...';
      
      // Import budgets
      for (final budget in exportData.budgets) {
        await _budgetService.addBudget(
          name: budget.name,
          amount: budget.budgetAmount,
          category: budget.category,
          period: budget.period,
        );
      }

      _exportProgress.value = 'Import completed';
      
      Get.snackbar(
        'Import Successful',
        'Data imported successfully from backup',
        snackPosition: SnackPosition.BOTTOM,
      );

      return true;
    } catch (e) {
      Get.snackbar(
        'Import Error',
        'Failed to import backup: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      _isExporting.value = false;
      _exportProgress.value = '';
    }
  }

  /// Get filtered transactions
  List<Transaction> _getFilteredTransactions({
    DateTime? startDate,
    DateTime? endDate,
    List<String>? categories,
    String? type,
  }) {
    // Convert PersonalTransaction to Transaction
    var transactions = _transactionService.transactions.map((pt) => Transaction(
      id: pt.id,
      accountId: pt.accountId,
      title: pt.title,
      description: pt.description,
      amount: pt.amount,
      type: TransactionType.values.firstWhere(
        (e) => e.name == pt.type,
        orElse: () => TransactionType.expense,
      ),
      category: TransactionCategory.values.firstWhere(
        (e) => e.name == pt.category,
        orElse: () => TransactionCategory.other,
      ),
      date: pt.date,
      paymentMethod: pt.paymentMethod,
      location: pt.location,
      tags: pt.tags != null && pt.tags!.isNotEmpty
          ? List<String>.from(jsonDecode(pt.tags!))
          : null,
      createdAt: pt.createdAt,
      updatedAt: pt.updatedAt,
    )).toList();

    if (startDate != null) {
      transactions = transactions.where((t) => t.date.isAfter(startDate) || t.date.isAtSameMomentAs(startDate)).toList();
    }

    if (endDate != null) {
      transactions = transactions.where((t) => t.date.isBefore(endDate) || t.date.isAtSameMomentAs(endDate)).toList();
    }

    if (categories != null && categories.isNotEmpty) {
      transactions = transactions.where((t) => categories.contains(t.category.name)).toList();
    }

    if (type != null) {
      transactions = transactions.where((t) => t.type.name == type).toList();
    }

    return transactions;
  }

  /// Generate CSV content for transactions
  String _generateTransactionCSV(List<Transaction> transactions) {
    final buffer = StringBuffer();
    
    // Header
    buffer.writeln('Date,Type,Category,Description,Amount,Payment Method,Location,Tags');
    
    // Data rows
    for (final transaction in transactions) {
      buffer.writeln([
        transaction.date.toIso8601String().split('T')[0],
        transaction.type,
        transaction.category,
        '"${transaction.description}"',
        transaction.amount.toStringAsFixed(2),
        transaction.paymentMethod ?? '',
        transaction.location ?? '',
        transaction.tags?.join(';') ?? '',
      ].join(','));
    }
    
    return buffer.toString();
  }

  /// Generate CSV content for budgets
  String _generateBudgetCSV(List<Budget> budgets) {
    final buffer = StringBuffer();
    
    // Header
    buffer.writeln('Category,Amount,Period,Start Date,End Date,Is Active,Created Date');
    
    // Data rows
    for (final budget in budgets) {
      buffer.writeln([
        budget.category,
        budget.budgetAmount.toStringAsFixed(2),
        budget.period.toString(),
        budget.startDate.toIso8601String().split('T')[0],
        budget.endDate?.toIso8601String().split('T')[0] ?? '',
        budget.isActive.toString(),
        budget.createdAt.toIso8601String().split('T')[0],
      ].join(','));
    }
    
    return buffer.toString();
  }

  /// Generate PDF for transactions
  Future<pw.Document> _generateTransactionPDF(
    List<Transaction> transactions,
    DateTime? startDate,
    DateTime? endDate,
  ) async {
    final pdf = pw.Document();

    // Calculate summary
    final totalIncome = transactions.where((t) => t.type == 'income').fold(0.0, (sum, t) => sum + t.amount);
    final totalExpenses = transactions.where((t) => t.type == 'expense').fold(0.0, (sum, t) => sum + t.amount);
    final netAmount = totalIncome - totalExpenses;

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return [
            // Header
            pw.Header(
              level: 0,
              child: pw.Text(
                'Transaction Report',
                style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
              ),
            ),
            
            // Date range
            if (startDate != null || endDate != null)
              pw.Paragraph(
                text: 'Period: ${startDate?.toIso8601String().split('T')[0] ?? 'All'} to ${endDate?.toIso8601String().split('T')[0] ?? 'All'}',
                style: const pw.TextStyle(fontSize: 12),
              ),
            
            pw.SizedBox(height: 20),
            
            // Summary
            pw.Container(
              padding: const pw.EdgeInsets.all(10),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(),
                borderRadius: pw.BorderRadius.circular(5),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text('Summary', style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold)),
                  pw.SizedBox(height: 10),
                  pw.Text('Total Income: KES ${totalIncome.toStringAsFixed(2)}'),
                  pw.Text('Total Expenses: KES ${totalExpenses.toStringAsFixed(2)}'),
                  pw.Text('Net Amount: KES ${netAmount.toStringAsFixed(2)}'),
                  pw.Text('Total Transactions: ${transactions.length}'),
                ],
              ),
            ),
            
            pw.SizedBox(height: 20),
            
            // Transactions table
            pw.Text('Transactions', style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold)),
            pw.SizedBox(height: 10),
            
            pw.Table(
              border: pw.TableBorder.all(),
              children: [
                // Header row
                pw.TableRow(
                  children: [
                    pw.Padding(padding: const pw.EdgeInsets.all(5), child: pw.Text('Date', style: pw.TextStyle(fontWeight: pw.FontWeight.bold))),
                    pw.Padding(padding: const pw.EdgeInsets.all(5), child: pw.Text('Type', style: pw.TextStyle(fontWeight: pw.FontWeight.bold))),
                    pw.Padding(padding: const pw.EdgeInsets.all(5), child: pw.Text('Category', style: pw.TextStyle(fontWeight: pw.FontWeight.bold))),
                    pw.Padding(padding: const pw.EdgeInsets.all(5), child: pw.Text('Description', style: pw.TextStyle(fontWeight: pw.FontWeight.bold))),
                    pw.Padding(padding: const pw.EdgeInsets.all(5), child: pw.Text('Amount', style: pw.TextStyle(fontWeight: pw.FontWeight.bold))),
                  ],
                ),
                // Data rows
                ...transactions.map((transaction) => pw.TableRow(
                  children: [
                    pw.Padding(padding: const pw.EdgeInsets.all(5), child: pw.Text(transaction.date.toIso8601String().split('T')[0])),
                    pw.Padding(padding: const pw.EdgeInsets.all(5), child: pw.Text(transaction.type.name.toUpperCase())),
                    pw.Padding(padding: const pw.EdgeInsets.all(5), child: pw.Text(transaction.category.name.toUpperCase())),
                    pw.Padding(padding: const pw.EdgeInsets.all(5), child: pw.Text(transaction.description.toString(), maxLines: 2)),
                    pw.Padding(padding: const pw.EdgeInsets.all(5), child: pw.Text('KES ${transaction.amount.toStringAsFixed(2)}')),
                  ],
                )).toList(),
              ],
            ),
          ];
        },
      ),
    );

    return pdf;
  }

  /// Save content to file
  Future<File> _saveToFile(String content, String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fileName');
    return await file.writeAsString(content);
  }

  /// Save PDF to file
  Future<File> _savePDFToFile(pw.Document pdf, String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fileName');
    await file.writeAsBytes(await pdf.save());
    return file;
  }

  /// Get export statistics
  Map<String, dynamic> getExportStatistics() {
    final personalTransactions = _transactionService.transactions;
    final budgets = _budgetService.budgets;

    // Convert PersonalTransaction to Transaction for statistics
    final transactions = personalTransactions.map((pt) => Transaction(
      id: pt.id,
      accountId: pt.accountId,
      title: pt.title,
      description: pt.description,
      amount: pt.amount,
      type: TransactionType.values.firstWhere(
        (e) => e.name == pt.type,
        orElse: () => TransactionType.expense,
      ),
      category: TransactionCategory.values.firstWhere(
        (e) => e.name == pt.category,
        orElse: () => TransactionCategory.other,
      ),
      date: pt.date,
      paymentMethod: pt.paymentMethod,
      location: pt.location,
      tags: pt.tags != null && pt.tags!.isNotEmpty
          ? List<String>.from(jsonDecode(pt.tags!))
          : null,
      createdAt: pt.createdAt,
      updatedAt: pt.updatedAt,
    )).toList();
    
    return {
      'totalTransactions': transactions.length,
      'totalBudgets': budgets.length,
      'dateRange': {
        'earliest': transactions.isNotEmpty 
            ? transactions.map((t) => t.date).reduce((a, b) => a.isBefore(b) ? a : b).toIso8601String()
            : null,
        'latest': transactions.isNotEmpty 
            ? transactions.map((t) => t.date).reduce((a, b) => a.isAfter(b) ? a : b).toIso8601String()
            : null,
      },
      'categories': transactions.map((t) => t.category).toSet().length,
      'estimatedFileSize': _estimateFileSize(transactions, budgets),
    };
  }

  /// Estimate file size for export
  String _estimateFileSize(List<Transaction> transactions, List<Budget> budgets) {
    // Rough estimation: ~100 bytes per transaction, ~50 bytes per budget
    final sizeBytes = (transactions.length * 100) + (budgets.length * 50);
    
    if (sizeBytes < 1024) {
      return '${sizeBytes}B';
    } else if (sizeBytes < 1024 * 1024) {
      return '${(sizeBytes / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(sizeBytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }
}
