import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
// import 'package:sms_advanced/sms_advanced.dart';  // TODO: Fix namespace issues
import 'package:get_storage/get_storage.dart';
import '../models/sms_transaction.dart';
import 'sms_parser_service.dart';
import 'transaction_classifier_service.dart';
import 'local_storage_service.dart';

enum SmsPermissionStatus {
  notRequested,
  denied,
  granted,
  permanentlyDenied,
}

enum SmsProcessingStatus {
  idle,
  processing,
  error,
  completed,
}

class SmsPermissionsService extends GetxService {
  static SmsPermissionsService get to => Get.find();
  
  final GetStorage _storage = GetStorage();
  final LocalStorageService _localStorageService = Get.find();
  final SmsParserService _smsParserService = Get.find();
  final TransactionClassifierService _classifierService = Get.find();
  
  // final SmsAdvanced _smsAdvanced = SmsAdvanced();  // TODO: Fix namespace issues
  
  final Rx<SmsPermissionStatus> _permissionStatus = SmsPermissionStatus.notRequested.obs;
  final RxBool _isBackgroundProcessingEnabled = false.obs;
  final RxBool _isProcessingMessages = false.obs;
  final Rx<SmsProcessingStatus> _processingStatus = SmsProcessingStatus.idle.obs;
  final RxInt _totalMessagesProcessed = 0.obs;
  final RxInt _newTransactionsFound = 0.obs;
  final RxString _lastProcessingError = ''.obs;
  final Rx<DateTime> _lastProcessingTime = DateTime.now().obs;
  
  // Getters
  SmsPermissionStatus get permissionStatus => _permissionStatus.value;
  bool get isBackgroundProcessingEnabled => _isBackgroundProcessingEnabled.value;
  bool get isProcessingMessages => _isProcessingMessages.value;
  SmsProcessingStatus get processingStatus => _processingStatus.value;
  int get totalMessagesProcessed => _totalMessagesProcessed.value;
  int get newTransactionsFound => _newTransactionsFound.value;
  String get lastProcessingError => _lastProcessingError.value;
  DateTime get lastProcessingTime => _lastProcessingTime.value;
  
  // SMS processing settings
  final RxBool _autoProcessNewSms = true.obs;
  final RxBool _processOnlyFinancialSms = true.obs;
  final RxInt _maxMessagesToProcess = 100.obs;
  final RxInt _processingIntervalMinutes = 30.obs;
  
  bool get autoProcessNewSms => _autoProcessNewSms.value;
  bool get processOnlyFinancialSms => _processOnlyFinancialSms.value;
  int get maxMessagesToProcess => _maxMessagesToProcess.value;
  int get processingIntervalMinutes => _processingIntervalMinutes.value;
  
  Timer? _backgroundProcessingTimer;
  StreamSubscription? _smsSubscription;  // TODO: Fix SmsMessage type when SMS is re-enabled

  @override
  void onInit() {
    super.onInit();
    _loadSettings();
    _checkInitialPermissions();
    _initializeBackgroundProcessing();
  }

  @override
  void onClose() {
    _backgroundProcessingTimer?.cancel();
    _smsSubscription?.cancel();
    super.onClose();
  }

  /// Load settings from storage
  void _loadSettings() {
    _isBackgroundProcessingEnabled.value = _storage.read('background_processing_enabled') ?? false;
    _autoProcessNewSms.value = _storage.read('auto_process_new_sms') ?? true;
    _processOnlyFinancialSms.value = _storage.read('process_only_financial_sms') ?? true;
    _maxMessagesToProcess.value = _storage.read('max_messages_to_process') ?? 100;
    _processingIntervalMinutes.value = _storage.read('processing_interval_minutes') ?? 30;
    _totalMessagesProcessed.value = _storage.read('total_messages_processed') ?? 0;
    _newTransactionsFound.value = _storage.read('new_transactions_found') ?? 0;
    
    final lastProcessingTimeStr = _storage.read('last_processing_time');
    if (lastProcessingTimeStr != null) {
      _lastProcessingTime.value = DateTime.parse(lastProcessingTimeStr);
    }
  }

  /// Save settings to storage
  void _saveSettings() {
    _storage.write('background_processing_enabled', _isBackgroundProcessingEnabled.value);
    _storage.write('auto_process_new_sms', _autoProcessNewSms.value);
    _storage.write('process_only_financial_sms', _processOnlyFinancialSms.value);
    _storage.write('max_messages_to_process', _maxMessagesToProcess.value);
    _storage.write('processing_interval_minutes', _processingIntervalMinutes.value);
    _storage.write('total_messages_processed', _totalMessagesProcessed.value);
    _storage.write('new_transactions_found', _newTransactionsFound.value);
    _storage.write('last_processing_time', _lastProcessingTime.value.toIso8601String());
  }

  /// Check initial permissions
  Future<void> _checkInitialPermissions() async {
    if (!Platform.isAndroid) {
      _permissionStatus.value = SmsPermissionStatus.denied;
      return;
    }

    try {
      final status = await Permission.sms.status;
      _updatePermissionStatus(status);
    } catch (e) {
      print('Error checking SMS permissions: $e');
      _permissionStatus.value = SmsPermissionStatus.denied;
    }
  }

  /// Update permission status
  void _updatePermissionStatus(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        _permissionStatus.value = SmsPermissionStatus.granted;
        break;
      case PermissionStatus.denied:
        _permissionStatus.value = SmsPermissionStatus.denied;
        break;
      case PermissionStatus.permanentlyDenied:
        _permissionStatus.value = SmsPermissionStatus.permanentlyDenied;
        break;
      default:
        _permissionStatus.value = SmsPermissionStatus.notRequested;
    }
  }

  /// Request SMS permissions
  Future<bool> requestSmsPermissions() async {
    if (!Platform.isAndroid) {
      Get.snackbar(
        'Not Supported',
        'SMS processing is only available on Android devices',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    }

    try {
      final status = await Permission.sms.request();
      _updatePermissionStatus(status);
      
      if (status.isGranted) {
        Get.snackbar(
          'Permission Granted',
          'SMS processing is now enabled',
          snackPosition: SnackPosition.BOTTOM,
        );
        return true;
      } else if (status.isPermanentlyDenied) {
        _showPermissionDeniedDialog();
        return false;
      } else {
        Get.snackbar(
          'Permission Denied',
          'SMS processing requires permission to read SMS messages',
          snackPosition: SnackPosition.BOTTOM,
        );
        return false;
      }
    } catch (e) {
      print('Error requesting SMS permissions: $e');
      Get.snackbar(
        'Error',
        'Failed to request SMS permissions: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    }
  }

  /// Show permission denied dialog
  void _showPermissionDeniedDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Permission Required'),
        content: const Text(
          'SMS processing requires permission to read SMS messages. '
          'Please enable SMS permission in app settings to use this feature.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              openAppSettings();
            },
            child: const Text('Open Settings'),
          ),
        ],
      ),
    );
  }

  /// Initialize background processing
  void _initializeBackgroundProcessing() {
    if (_isBackgroundProcessingEnabled.value && _permissionStatus.value == SmsPermissionStatus.granted) {
      _startBackgroundProcessing();
    }
  }

  /// Check if SMS permissions are granted
  Future<bool> hasPermissions() async {
    return _permissionStatus.value == SmsPermissionStatus.granted;
  }

  /// Enable background processing
  Future<void> enableBackgroundProcessing() async {
    if (_permissionStatus.value != SmsPermissionStatus.granted) {
      final granted = await requestSmsPermissions();
      if (!granted) return;
    }

    _isBackgroundProcessingEnabled.value = true;
    _saveSettings();
    _startBackgroundProcessing();

    Get.snackbar(
      'Background Processing Enabled',
      'SMS messages will be automatically processed for transactions',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// Disable background processing
  void disableBackgroundProcessing() {
    _isBackgroundProcessingEnabled.value = false;
    _saveSettings();
    _stopBackgroundProcessing();
    
    Get.snackbar(
      'Background Processing Disabled',
      'SMS messages will no longer be automatically processed',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// Start background processing
  void _startBackgroundProcessing() {
    _stopBackgroundProcessing(); // Stop any existing processing
    
    // Set up periodic processing
    _backgroundProcessingTimer = Timer.periodic(
      Duration(minutes: _processingIntervalMinutes.value),
      (_) => _processRecentSmsMessages(),
    );
    
    // Set up real-time SMS listening
    if (_autoProcessNewSms.value) {
      _startSmsListening();
    }
    
    // Process any existing unprocessed messages
    _processRecentSmsMessages();
  }

  /// Stop background processing
  void _stopBackgroundProcessing() {
    _backgroundProcessingTimer?.cancel();
    _backgroundProcessingTimer = null;
    _smsSubscription?.cancel();
    _smsSubscription = null;
  }

  /// Start listening for new SMS messages
  void _startSmsListening() {
    try {
      // TODO: Implement SMS listening when namespace issues are resolved
      print('SMS listening temporarily disabled due to namespace issues');
      /*
      final receiver = SmsReceiver();
      _smsSubscription = receiver.onSmsReceived?.listen((SmsMessage message) {
        if (_shouldProcessMessage(message)) {
          _processSingleMessage(message);
        }
      });
      */
    } catch (e) {
      print('Error starting SMS listening: $e');
      _lastProcessingError.value = 'Failed to start SMS listening: ${e.toString()}';
    }
  }

  /// Process recent SMS messages
  Future<void> _processRecentSmsMessages() async {
    if (_isProcessingMessages.value) return;

    _isProcessingMessages.value = true;
    _processingStatus.value = SmsProcessingStatus.processing;
    _lastProcessingError.value = '';

    try {
      // TODO: Implement SMS processing when namespace issues are resolved
      print('SMS processing temporarily disabled due to namespace issues');
      /*
      final query = SmsQuery();
      final messages = await query.querySms(
        kinds: [SmsQueryKind.inbox],
        count: _maxMessagesToProcess.value,
      );

      final recentMessages = messages.take(_maxMessagesToProcess.value).toList();
      int processedCount = 0;
      int newTransactions = 0;

      for (final message in recentMessages) {
        if (_shouldProcessMessage(message)) {
          final transaction = await _processSingleMessage(message);
          if (transaction != null) {
            newTransactions++;
          }
          processedCount++;
        }
      }
      */
      
      // TODO: Implement actual SMS processing logic
      const processedCount = 0; // Placeholder
      const newTransactions = 0; // Placeholder

      _totalMessagesProcessed.value += processedCount;
      _newTransactionsFound.value += newTransactions;
      _lastProcessingTime.value = DateTime.now();
      _processingStatus.value = SmsProcessingStatus.completed;
      
      _saveSettings();
      
      if (newTransactions > 0) {
        Get.snackbar(
          'SMS Processing Complete',
          'Found $newTransactions new transactions from $processedCount messages',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      print('Error processing SMS messages: $e');
      _lastProcessingError.value = e.toString();
      _processingStatus.value = SmsProcessingStatus.error;
    } finally {
      _isProcessingMessages.value = false;
    }
  }

  /// Check if message should be processed
  bool _shouldProcessMessage(dynamic message) {  // TODO: Fix SmsMessage type when SMS is re-enabled
    // TODO: Implement when SMS functionality is restored
    return true;
  }

  /// Process a single SMS message
  Future<SmsTransaction?> _processSingleMessage(dynamic message) async {  // TODO: Fix SmsMessage type when SMS is re-enabled
    try {
      // TODO: Implement when SMS functionality is restored
      return null;
      /*
      final transaction = _smsParserService.parseSmsMessage(
        message.body ?? '',
        message.address ?? '',
        message.date ?? DateTime.now(),
      );
      */
      
      // TODO: Implement when SMS functionality is restored
      final SmsTransaction? transaction = null; // Placeholder

      if (transaction != null) {
        // Auto-classify the transaction
        final classification = _classifierService.classifySmsTransaction(transaction);

        // If high confidence, auto-process
        if (classification.confidence >= 0.8) {
          // transaction.isProcessed = true; // TODO: Add these fields to SmsTransaction
          // transaction.isVerified = true;
          
          // Train the classifier
          _classifierService.trainWithFeedback(
            transactionId: transaction.id,
            correctCategory: classification.category,
            correctType: classification.suggestedType,
            features: {
              'amount': transaction.amount,
              'description': transaction.description,
              'sender': transaction.sender,
            },
            wasCorrect: true,
          );
        }
        
        return transaction;
      }
    } catch (e) {
      print('Error processing SMS message: $e');
    }
    
    return null;
  }

  /// Manual SMS processing trigger
  Future<void> manualProcessSms() async {
    if (_permissionStatus.value != SmsPermissionStatus.granted) {
      final granted = await requestSmsPermissions();
      if (!granted) return;
    }
    
    await _processRecentSmsMessages();
  }

  /// Update processing settings
  void updateProcessingSettings({
    bool? autoProcessNewSms,
    bool? processOnlyFinancialSms,
    int? maxMessagesToProcess,
    int? processingIntervalMinutes,
  }) {
    if (autoProcessNewSms != null) {
      _autoProcessNewSms.value = autoProcessNewSms;
    }
    if (processOnlyFinancialSms != null) {
      _processOnlyFinancialSms.value = processOnlyFinancialSms;
    }
    if (maxMessagesToProcess != null) {
      _maxMessagesToProcess.value = maxMessagesToProcess;
    }
    if (processingIntervalMinutes != null) {
      _processingIntervalMinutes.value = processingIntervalMinutes;
    }
    
    _saveSettings();
    
    // Restart background processing with new settings
    if (_isBackgroundProcessingEnabled.value) {
      _startBackgroundProcessing();
    }
  }

  /// Get processing statistics
  Map<String, dynamic> getProcessingStats() {
    return {
      'permission_status': _permissionStatus.value.name,
      'background_processing_enabled': _isBackgroundProcessingEnabled.value,
      'total_messages_processed': _totalMessagesProcessed.value,
      'new_transactions_found': _newTransactionsFound.value,
      'last_processing_time': _lastProcessingTime.value.toIso8601String(),
      'processing_status': _processingStatus.value.name,
      'last_error': _lastProcessingError.value,
      'auto_process_new_sms': _autoProcessNewSms.value,
      'process_only_financial_sms': _processOnlyFinancialSms.value,
      'max_messages_to_process': _maxMessagesToProcess.value,
      'processing_interval_minutes': _processingIntervalMinutes.value,
    };
  }

  /// Reset processing statistics
  void resetProcessingStats() {
    _totalMessagesProcessed.value = 0;
    _newTransactionsFound.value = 0;
    _lastProcessingError.value = '';
    _lastProcessingTime.value = DateTime.now();
    _saveSettings();
  }
}
