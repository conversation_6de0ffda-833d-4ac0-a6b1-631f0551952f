import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/currency.dart';
import 'account_service.dart';

class CurrencyService extends GetxService {
  static CurrencyService get to => Get.find();

  final GetStorage _storage = GetStorage();
  final RxString _baseCurrency = 'USD'.obs;
  final RxMap<String, double> _exchangeRates = <String, double>{}.obs;
  final RxBool _isLoading = false.obs;
  final Rx<DateTime> _lastUpdated = DateTime.now().obs;

  // New properties for enhanced currency management
  final Rx<Currency> currentCurrency = Currency(
    code: 'KES',
    name: 'Kenyan Shilling',
    symbol: 'KSh',
    flag: '🇰🇪',
  ).obs;
  final RxBool useCountryBasedCurrency = false.obs;
  final RxString selectedCountry = 'Kenya'.obs;

  String get baseCurrency => _baseCurrency.value;
  Map<String, double> get exchangeRates => _exchangeRates;
  bool get isLoading => _isLoading.value;
  DateTime get lastUpdated => _lastUpdated.value;

  // New getters
  List<Currency> get availableCurrencies => supportedCurrencies;

  // Supported currencies
  final List<Currency> supportedCurrencies = [
    Currency(code: 'USD', name: 'US Dollar', symbol: '\$', flag: '🇺🇸'),
    Currency(code: 'EUR', name: 'Euro', symbol: '€', flag: '🇪🇺'),
    Currency(code: 'GBP', name: 'British Pound', symbol: '£', flag: '🇬🇧'),
    Currency(code: 'JPY', name: 'Japanese Yen', symbol: '¥', flag: '🇯🇵'),
    Currency(code: 'CAD', name: 'Canadian Dollar', symbol: 'C\$', flag: '🇨🇦'),
    Currency(code: 'AUD', name: 'Australian Dollar', symbol: 'A\$', flag: '🇦🇺'),
    Currency(code: 'CHF', name: 'Swiss Franc', symbol: 'CHF', flag: '🇨🇭'),
    Currency(code: 'CNY', name: 'Chinese Yuan', symbol: '¥', flag: '🇨🇳'),
    Currency(code: 'KES', name: 'Kenyan Shilling', symbol: 'KSh', flag: '🇰🇪'),
    Currency(code: 'NGN', name: 'Nigerian Naira', symbol: '₦', flag: '🇳🇬'),
    Currency(code: 'ZAR', name: 'South African Rand', symbol: 'R', flag: '🇿🇦'),
    Currency(code: 'EGP', name: 'Egyptian Pound', symbol: 'E£', flag: '🇪🇬'),
    Currency(code: 'GHS', name: 'Ghanaian Cedi', symbol: '₵', flag: '🇬🇭'),
    Currency(code: 'UGX', name: 'Ugandan Shilling', symbol: 'USh', flag: '🇺🇬'),
    Currency(code: 'TZS', name: 'Tanzanian Shilling', symbol: 'TSh', flag: '🇹🇿'),
    Currency(code: 'INR', name: 'Indian Rupee', symbol: '₹', flag: '🇮🇳'),
    Currency(code: 'BRL', name: 'Brazilian Real', symbol: 'R\$', flag: '🇧🇷'),
    Currency(code: 'MXN', name: 'Mexican Peso', symbol: '\$', flag: '🇲🇽'),
    Currency(code: 'RUB', name: 'Russian Ruble', symbol: '₽', flag: '🇷🇺'),
    Currency(code: 'KRW', name: 'South Korean Won', symbol: '₩', flag: '🇰🇷'),
  ];

  @override
  Future<void> onInit() async {
    super.onInit();
    await _loadBaseCurrency();
    await _loadExchangeRates();
    await _loadCurrencySettings();
    _startPeriodicUpdate();
  }

  // Load base currency from account settings
  Future<void> _loadBaseCurrency() async {
    try {
      final accountCurrency = await AccountService.to.getAccountSetting('currency');
      if (accountCurrency != null && accountCurrency.isNotEmpty) {
        _baseCurrency.value = accountCurrency;
        // Update current currency to match base currency
        final currency = getCurrency(accountCurrency);
        if (currency != null) {
          currentCurrency.value = currency;
        }
      } else {
        // Default to KES if no currency set (Kenya-focused app)
        _baseCurrency.value = 'KES';
        await setBaseCurrency('KES');
        currentCurrency.value = getCurrency('KES') ?? currentCurrency.value;
      }
    } catch (e) {
      print('Error loading base currency: $e');
      _baseCurrency.value = 'KES';
      currentCurrency.value = getCurrency('KES') ?? currentCurrency.value;
    }
  }

  // Load exchange rates from storage
  Future<void> _loadExchangeRates() async {
    try {
      final storedRates = _storage.read('exchange_rates');
      final lastUpdate = _storage.read('rates_last_updated');
      
      if (storedRates != null) {
        _exchangeRates.value = Map<String, double>.from(storedRates);
      }
      
      if (lastUpdate != null) {
        _lastUpdated.value = DateTime.parse(lastUpdate);
      }
      
      // Update rates if they're older than 1 hour
      if (DateTime.now().difference(_lastUpdated.value).inHours >= 1) {
        await updateExchangeRates();
      }
    } catch (e) {
      print('Error loading exchange rates: $e');
      await updateExchangeRates();
    }
  }

  // Set base currency
  Future<bool> setBaseCurrency(String currencyCode) async {
    try {
      _baseCurrency.value = currencyCode;
      await AccountService.to.setAccountSetting('currency', currencyCode);
      await updateExchangeRates();
      return true;
    } catch (e) {
      print('Error setting base currency: $e');
      return false;
    }
  }

  // Update exchange rates from API
  Future<bool> updateExchangeRates() async {
    if (_isLoading.value) return false;

    try {
      _isLoading.value = true;
      
      // Using a free exchange rate API (replace with your preferred service)
      final response = await http.get(
        Uri.parse('https://api.exchangerate-api.com/v4/latest/${_baseCurrency.value}'),
        headers: {'Accept': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final rates = Map<String, double>.from(
          data['rates'].map((key, value) => MapEntry(key, value.toDouble()))
        );
        
        _exchangeRates.value = rates;
        _lastUpdated.value = DateTime.now();
        
        // Save to storage
        _storage.write('exchange_rates', rates);
        _storage.write('rates_last_updated', _lastUpdated.value.toIso8601String());
        
        return true;
      } else {
        print('Failed to fetch exchange rates: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('Error updating exchange rates: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Convert amount between currencies
  double convertCurrency(double amount, String fromCurrency, String toCurrency) {
    if (fromCurrency == toCurrency) return amount;
    
    try {
      if (fromCurrency == _baseCurrency.value) {
        // Converting from base currency
        final rate = _exchangeRates[toCurrency];
        return rate != null ? amount * rate : amount;
      } else if (toCurrency == _baseCurrency.value) {
        // Converting to base currency
        final rate = _exchangeRates[fromCurrency];
        return rate != null ? amount / rate : amount;
      } else {
        // Converting between two non-base currencies
        final fromRate = _exchangeRates[fromCurrency];
        final toRate = _exchangeRates[toCurrency];
        if (fromRate != null && toRate != null) {
          final baseAmount = amount / fromRate;
          return baseAmount * toRate;
        }
        return amount;
      }
    } catch (e) {
      print('Error converting currency: $e');
      return amount;
    }
  }

  // Format amount with currency symbol
  String formatAmount(double amount, String currencyCode, {bool showSymbol = true}) {
    final currency = getCurrency(currencyCode);
    final formattedAmount = amount.toStringAsFixed(2);
    
    if (!showSymbol) return formattedAmount;
    
    if (currency != null) {
      return '${currency.symbol}$formattedAmount';
    }
    
    return '$currencyCode $formattedAmount';
  }

  // Get currency by code
  Currency? getCurrency(String code) {
    try {
      return supportedCurrencies.firstWhere((c) => c.code == code);
    } catch (e) {
      return null;
    }
  }

  // Get popular currencies
  List<Currency> getPopularCurrencies() {
    return supportedCurrencies.where((c) => [
      'USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'KES', 'NGN', 'ZAR', 'INR'
    ].contains(c.code)).toList();
  }

  // Get African currencies
  List<Currency> getAfricanCurrencies() {
    return supportedCurrencies.where((c) => [
      'KES', 'NGN', 'ZAR', 'EGP', 'GHS', 'UGX', 'TZS'
    ].contains(c.code)).toList();
  }

  // Get exchange rate for a specific currency
  double? getExchangeRate(String currencyCode) {
    return _exchangeRates[currencyCode];
  }

  // Check if currency is supported
  bool isCurrencySupported(String code) {
    return supportedCurrencies.any((c) => c.code == code);
  }

  // Get currency symbol
  String getCurrencySymbol(String code) {
    final currency = getCurrency(code);
    return currency?.symbol ?? code;
  }

  // Start periodic updates (every hour)
  void _startPeriodicUpdate() {
    // Update exchange rates every hour
    Stream.periodic(const Duration(hours: 1)).listen((_) {
      updateExchangeRates();
    });
  }

  // Get formatted exchange rate
  String getFormattedExchangeRate(String fromCurrency, String toCurrency) {
    final rate = getExchangeRate(toCurrency);
    if (rate == null) return 'N/A';
    
    final convertedRate = convertCurrency(1.0, fromCurrency, toCurrency);
    return '1 $fromCurrency = ${convertedRate.toStringAsFixed(4)} $toCurrency';
  }

  // Check if rates need update
  bool get needsUpdate {
    return DateTime.now().difference(_lastUpdated.value).inHours >= 1;
  }

  // Get time since last update
  String get timeSinceLastUpdate {
    final difference = DateTime.now().difference(_lastUpdated.value);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} minutes ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} hours ago';
    } else {
      return '${difference.inDays} days ago';
    }
  }

  // Convert amount to base currency for calculations
  double toBaseCurrency(double amount, String fromCurrency) {
    return convertCurrency(amount, fromCurrency, _baseCurrency.value);
  }

  // Convert amount from base currency
  double fromBaseCurrency(double amount, String toCurrency) {
    return convertCurrency(amount, _baseCurrency.value, toCurrency);
  }

  // New methods for enhanced currency management
  Future<void> _loadCurrencySettings() async {
    try {
      final savedCurrency = _storage.read('current_currency');
      if (savedCurrency != null) {
        final currency = supportedCurrencies.firstWhere(
          (c) => c.code == savedCurrency,
          orElse: () => supportedCurrencies.first,
        );
        currentCurrency.value = currency;
      }

      useCountryBasedCurrency.value = _storage.read('use_country_currency') ?? false;
      selectedCountry.value = _storage.read('selected_country') ?? 'Kenya';
    } catch (e) {
      print('Error loading currency settings: $e');
    }
  }

  Future<void> setCurrentCurrency(Currency currency) async {
    currentCurrency.value = currency;
    await _storage.write('current_currency', currency.code);
    // Also update base currency to keep them in sync
    await setBaseCurrency(currency.code);
  }

  void setUseCountryBasedCurrency(bool value) {
    useCountryBasedCurrency.value = value;
    _storage.write('use_country_currency', value);
  }

  void setSelectedCountry(String country) {
    selectedCountry.value = country;
    _storage.write('selected_country', country);
  }



  Currency? getCurrencyByCode(String code) {
    try {
      return supportedCurrencies.firstWhere((c) => c.code == code);
    } catch (e) {
      return null;
    }
  }
}
