import 'dart:convert';
import 'dart:math';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../models/transaction.dart';
import '../models/sms_transaction.dart';
import '../models/classification_rule.dart';
import 'local_storage_service.dart';
import 'account_service.dart';

enum ClassificationConfidence {
  low,
  medium,
  high,
  veryHigh,
}

class TransactionClassifierService extends GetxService {
  static TransactionClassifierService get to => Get.find();
  
  final GetStorage _storage = GetStorage();
  final LocalStorageService _localStorageService = Get.find();
  final AccountService _accountService = Get.find();
  
  final RxList<ClassificationRule> _rules = <ClassificationRule>[].obs;
  final RxMap<String, int> _categoryFrequency = <String, int>{}.obs;
  final RxMap<String, double> _amountPatterns = <String, double>{}.obs;
  final RxMap<String, String> _senderCategoryMap = <String, String>{}.obs;
  final RxInt _totalClassifications = 0.obs;
  final RxInt _correctClassifications = 0.obs;
  final RxDouble _accuracyRate = 0.0.obs;
  final RxDouble _accuracy = 0.0.obs;
  
  // Getters
  List<ClassificationRule> get rules => _rules;
  Map<String, int> get categoryFrequency => _categoryFrequency;
  Map<String, double> get amountPatterns => _amountPatterns;
  Map<String, String> get senderCategoryMap => _senderCategoryMap;
  int get totalClassifications => _totalClassifications.value;
  int get correctClassifications => _correctClassifications.value;
  double get accuracy => _accuracy.value;

  @override
  void onInit() {
    super.onInit();
    _loadClassificationData();
    _initializeDefaultRules();
  }

  /// Load classification data from storage
  void _loadClassificationData() {
    // Load rules
    final savedRules = _storage.read<List>('classification_rules');
    if (savedRules != null) {
      _rules.assignAll(
        savedRules.map((rule) => ClassificationRule.fromJson(rule)).toList(),
      );
    }

    // Load frequency data
    final frequency = _storage.read<Map>('category_frequency');
    if (frequency != null) {
      _categoryFrequency.assignAll(Map<String, int>.from(frequency));
    }

    // Load amount patterns
    final amounts = _storage.read<Map>('amount_patterns');
    if (amounts != null) {
      _amountPatterns.assignAll(Map<String, double>.from(amounts));
    }

    // Load sender mappings
    final senders = _storage.read<Map>('sender_category_map');
    if (senders != null) {
      _senderCategoryMap.assignAll(Map<String, String>.from(senders));
    }

    // Load statistics
    _totalClassifications.value = _storage.read('total_classifications') ?? 0;
    _correctClassifications.value = _storage.read('correct_classifications') ?? 0;
    _updateAccuracy();
  }

  /// Initialize default classification rules
  void _initializeDefaultRules() {
    if (_rules.isEmpty) {
      final defaultRules = [
        // Income rules
        ClassificationRule(
          id: 'salary_rule',
          name: 'Salary Detection',
          type: ClassificationRuleType.income,
          conditions: [
            ClassificationCondition(
              field: 'description',
              operator: 'contains',
              value: 'salary',
              weight: 0.9,
            ),
            ClassificationCondition(
              field: 'amount',
              operator: 'range',
              value: '50000-500000',
              weight: 0.7,
            ),
          ],
          category: 'Salary',
          confidence: 0.9,
          isActive: true,
        ),

        ClassificationRule(
          id: 'freelance_rule',
          name: 'Freelance Income',
          type: ClassificationRuleType.income,
          conditions: [
            ClassificationCondition(
              field: 'description',
              operator: 'contains_any',
              value: 'freelance,upwork,fiverr,paypal',
              weight: 0.8,
            ),
          ],
          category: 'Freelance',
          confidence: 0.8,
          isActive: true,
        ),

        // Expense rules
        ClassificationRule(
          id: 'grocery_rule',
          name: 'Grocery Shopping',
          type: ClassificationRuleType.expense,
          conditions: [
            ClassificationCondition(
              field: 'merchant',
              operator: 'contains_any',
              value: 'tuskys,naivas,carrefour,quickmart,chandarana',
              weight: 0.9,
            ),
          ],
          category: 'Groceries',
          confidence: 0.9,
          isActive: true,
        ),

        ClassificationRule(
          id: 'transport_rule',
          name: 'Transportation',
          type: ClassificationRuleType.expense,
          conditions: [
            ClassificationCondition(
              field: 'description',
              operator: 'contains_any',
              value: 'uber,bolt,matatu,bus,taxi',
              weight: 0.8,
            ),
            ClassificationCondition(
              field: 'amount',
              operator: 'range',
              value: '50-2000',
              weight: 0.6,
            ),
          ],
          category: 'Transportation',
          confidence: 0.8,
          isActive: true,
        ),

        ClassificationRule(
          id: 'utility_rule',
          name: 'Utility Bills',
          type: ClassificationRuleType.expense,
          conditions: [
            ClassificationCondition(
              field: 'sender',
              operator: 'contains_any',
              value: 'kplc,nairobi water,safaricom',
              weight: 0.9,
            ),
          ],
          category: 'Utilities',
          confidence: 0.9,
          isActive: true,
        ),
      ];

      _rules.assignAll(defaultRules);
      _saveClassificationData();
    }
  }

  /// Classify a transaction automatically
  TransactionClassification classifyTransaction(Transaction transaction) {
    final features = _extractFeatures(transaction);
    
    // Try rule-based classification first
    final ruleResult = _classifyWithRules(features);
    if (ruleResult.confidence >= 0.8) {
      _updateStatistics(true);
      return ruleResult;
    }

    // Try pattern-based classification
    final patternResult = _classifyWithPatterns(features);
    if (patternResult.confidence >= 0.7) {
      _updateStatistics(true);
      return patternResult;
    }

    // Try frequency-based classification
    final frequencyResult = _classifyWithFrequency(features);
    _updateStatistics(frequencyResult.confidence >= 0.6);
    
    return frequencyResult;
  }

  /// Classify SMS transaction
  TransactionClassification classifySmsTransaction(SmsTransaction smsTransaction) {
    final features = _extractSmsFeatures(smsTransaction);
    
    // Check sender mapping first
    final senderCategory = _senderCategoryMap[smsTransaction.sender.toLowerCase()];
    if (senderCategory != null) {
      return TransactionClassification(
        category: senderCategory,
        confidence: 0.9,
        method: ClassificationMethod.senderMapping,
        suggestedType: TransactionType.values.firstWhere(
          (e) => e.name == (smsTransaction.transactionType ?? 'expense'),
          orElse: () => TransactionType.expense,
        ),
      );
    }

    // Use rule-based classification
    final ruleResult = _classifyWithRules(features);
    if (ruleResult.confidence >= 0.7) {
      return ruleResult;
    }

    // Fallback to pattern matching
    return _classifyWithPatterns(features);
  }

  /// Extract features from transaction
  Map<String, dynamic> _extractFeatures(Transaction transaction) {
    return {
      'amount': transaction.amount,
      'description': transaction.description?.toLowerCase() ?? '',
      'category': transaction.category.name.toLowerCase(),
      'type': transaction.type.name,
      'date': transaction.date,
      'merchant': '', // Would be extracted from description
      'sender': '', // Would be extracted from description
    };
  }

  /// Extract features from SMS transaction
  Map<String, dynamic> _extractSmsFeatures(SmsTransaction smsTransaction) {
    return {
      'amount': smsTransaction.amount,
      'description': smsTransaction.rawMessage.toLowerCase(),
      'category': smsTransaction.assignedCategory?.toLowerCase() ?? '',
      'type': smsTransaction.transactionType ?? '',
      'date': smsTransaction.receivedAt,
      'merchant': smsTransaction.merchantName?.toLowerCase() ?? '',
      'sender': smsTransaction.sender.toLowerCase(),
      'reference': smsTransaction.referenceNumber ?? '',
      'location': smsTransaction.merchantName?.toLowerCase() ?? '',
    };
  }

  /// Classify using rules
  TransactionClassification _classifyWithRules(Map<String, dynamic> features) {
    double bestConfidence = 0.0;
    String bestCategory = 'Other';
    TransactionType bestType = TransactionType.expense;
    ClassificationMethod method = ClassificationMethod.rules;

    for (final rule in _rules.where((r) => r.isActive)) {
      final confidence = _evaluateRule(rule, features);
      if (confidence > bestConfidence) {
        bestConfidence = confidence;
        bestCategory = rule.category;
        bestType = rule.type == ClassificationRuleType.income 
            ? TransactionType.income 
            : TransactionType.expense;
      }
    }

    return TransactionClassification(
      category: bestCategory,
      confidence: bestConfidence,
      method: method,
      suggestedType: bestType,
    );
  }

  /// Evaluate a single rule
  double _evaluateRule(ClassificationRule rule, Map<String, dynamic> features) {
    double totalWeight = 0.0;
    double matchedWeight = 0.0;

    for (final condition in rule.conditions) {
      totalWeight += condition.weight;
      
      if (_evaluateCondition(condition, features)) {
        matchedWeight += condition.weight;
      }
    }

    if (totalWeight == 0) return 0.0;
    
    final score = matchedWeight / totalWeight;
    return score * rule.confidence;
  }

  /// Evaluate a single condition
  bool _evaluateCondition(ClassificationCondition condition, Map<String, dynamic> features) {
    final fieldValue = features[condition.field];
    if (fieldValue == null) return false;

    switch (condition.operator) {
      case 'equals':
        return fieldValue.toString().toLowerCase() == condition.value.toLowerCase();
      
      case 'contains':
        return fieldValue.toString().toLowerCase().contains(condition.value.toLowerCase());
      
      case 'contains_any':
        final values = condition.value.split(',');
        return values.any((value) => 
            fieldValue.toString().toLowerCase().contains(value.trim().toLowerCase()));
      
      case 'range':
        if (fieldValue is! num) return false;
        final parts = condition.value.split('-');
        if (parts.length != 2) return false;
        final min = double.tryParse(parts[0]) ?? 0;
        final max = double.tryParse(parts[1]) ?? double.infinity;
        return fieldValue >= min && fieldValue <= max;
      
      case 'greater_than':
        if (fieldValue is! num) return false;
        final threshold = double.tryParse(condition.value) ?? 0;
        return fieldValue > threshold;
      
      case 'less_than':
        if (fieldValue is! num) return false;
        final threshold = double.tryParse(condition.value) ?? 0;
        return fieldValue < threshold;
      
      default:
        return false;
    }
  }

  /// Classify using patterns
  TransactionClassification _classifyWithPatterns(Map<String, dynamic> features) {
    // Simple pattern matching based on keywords
    final description = features['description'] as String;
    final amount = features['amount'] as double;
    
    // Check for common patterns
    if (description.contains('salary') || description.contains('payroll')) {
      return TransactionClassification(
        category: 'Salary',
        confidence: 0.8,
        method: ClassificationMethod.patterns,
        suggestedType: TransactionType.income,
      );
    }
    
    if (description.contains('grocery') || description.contains('supermarket')) {
      return TransactionClassification(
        category: 'Groceries',
        confidence: 0.7,
        method: ClassificationMethod.patterns,
        suggestedType: TransactionType.expense,
      );
    }
    
    if (amount < 500 && (description.contains('transport') || description.contains('fare'))) {
      return TransactionClassification(
        category: 'Transportation',
        confidence: 0.6,
        method: ClassificationMethod.patterns,
        suggestedType: TransactionType.expense,
      );
    }

    return TransactionClassification(
      category: 'Other',
      confidence: 0.3,
      method: ClassificationMethod.patterns,
      suggestedType: TransactionType.expense,
    );
  }

  /// Classify using frequency analysis
  TransactionClassification _classifyWithFrequency(Map<String, dynamic> features) {
    if (_categoryFrequency.isEmpty) {
      return TransactionClassification(
        category: 'Other',
        confidence: 0.2,
        method: ClassificationMethod.frequency,
        suggestedType: TransactionType.expense,
      );
    }

    // Find most frequent category
    final mostFrequent = _categoryFrequency.entries
        .reduce((a, b) => a.value > b.value ? a : b);
    
    final totalTransactions = _categoryFrequency.values.fold(0, (sum, count) => sum + count);
    final confidence = mostFrequent.value / totalTransactions * 0.5; // Max 0.5 confidence

    return TransactionClassification(
      category: mostFrequent.key,
      confidence: confidence,
      method: ClassificationMethod.frequency,
      suggestedType: TransactionType.expense,
    );
  }

  /// Train the classifier with user feedback
  void trainWithFeedback({
    required String transactionId,
    required String correctCategory,
    required TransactionType correctType,
    required Map<String, dynamic> features,
    bool wasCorrect = false,
  }) {
    // Update statistics
    _totalClassifications.value++;
    if (wasCorrect) {
      _correctClassifications.value++;
    }
    _updateAccuracy();

    // Update category frequency
    _categoryFrequency[correctCategory] = (_categoryFrequency[correctCategory] ?? 0) + 1;

    // Update sender mapping if available
    final sender = features['sender'] as String?;
    if (sender != null && sender.isNotEmpty) {
      _senderCategoryMap[sender.toLowerCase()] = correctCategory;
    }

    // Update amount patterns
    final amount = features['amount'] as double?;
    if (amount != null) {
      _amountPatterns[correctCategory] = 
          (_amountPatterns[correctCategory] ?? 0) * 0.8 + amount * 0.2;
    }

    // Create or update rule based on feedback
    if (!wasCorrect) {
      _createRuleFromFeedback(features, correctCategory, correctType);
    }

    _saveClassificationData();
  }

  /// Create a new rule from user feedback
  void _createRuleFromFeedback(
    Map<String, dynamic> features,
    String category,
    TransactionType type,
  ) {
    final conditions = <ClassificationCondition>[];
    
    // Add description condition if available
    final description = features['description'] as String?;
    if (description != null && description.isNotEmpty) {
      final keywords = _extractKeywords(description);
      if (keywords.isNotEmpty) {
        conditions.add(ClassificationCondition(
          field: 'description',
          operator: 'contains_any',
          value: keywords.join(','),
          weight: 0.7,
        ));
      }
    }

    // Add amount condition
    final amount = features['amount'] as double?;
    if (amount != null) {
      final range = _getAmountRange(amount);
      conditions.add(ClassificationCondition(
        field: 'amount',
        operator: 'range',
        value: range,
        weight: 0.5,
      ));
    }

    if (conditions.isNotEmpty) {
      final rule = ClassificationRule(
        id: 'learned_${DateTime.now().millisecondsSinceEpoch}',
        name: 'Learned: $category',
        type: type == TransactionType.income 
            ? ClassificationRuleType.income 
            : ClassificationRuleType.expense,
        conditions: conditions,
        category: category,
        confidence: 0.6,
        isActive: true,
        isLearned: true,
      );

      _rules.add(rule);
    }
  }

  /// Extract keywords from description
  List<String> _extractKeywords(String description) {
    final words = description.toLowerCase().split(RegExp(r'\W+'));
    return words.where((word) => word.length > 3).take(3).toList();
  }

  /// Get amount range for a given amount
  String _getAmountRange(double amount) {
    if (amount < 100) return '0-100';
    if (amount < 500) return '100-500';
    if (amount < 1000) return '500-1000';
    if (amount < 5000) return '1000-5000';
    if (amount < 10000) return '5000-10000';
    if (amount < 50000) return '10000-50000';
    return '50000-1000000';
  }

  /// Update accuracy statistics
  void _updateAccuracy() {
    if (_totalClassifications.value > 0) {
      _accuracy.value = _correctClassifications.value / _totalClassifications.value;
    }
  }

  /// Save classification data
  void _saveClassificationData() {
    _storage.write('classification_rules', _rules.map((r) => r.toJson()).toList());
    _storage.write('category_frequency', _categoryFrequency);
    _storage.write('amount_patterns', _amountPatterns);
    _storage.write('sender_category_map', _senderCategoryMap);
    _storage.write('total_classifications', _totalClassifications.value);
    _storage.write('correct_classifications', _correctClassifications.value);
  }

  /// Get classification statistics
  Map<String, dynamic> getClassificationStats() {
    return {
      'total_classifications': _totalClassifications.value,
      'correct_classifications': _correctClassifications.value,
      'accuracy': _accuracy.value,
      'rules_count': _rules.length,
      'learned_rules_count': _rules.where((r) => r.isLearned).length,
      'category_frequency': _categoryFrequency,
      'sender_mappings': _senderCategoryMap.length,
    };
  }

  /// Reset classification data
  void resetClassificationData() {
    _rules.clear();
    _categoryFrequency.clear();
    _amountPatterns.clear();
    _senderCategoryMap.clear();
    _totalClassifications.value = 0;
    _correctClassifications.value = 0;
    _accuracy.value = 0.0;
    
    _initializeDefaultRules();
    _saveClassificationData();
  }

  /// Add custom rule
  void addCustomRule(ClassificationRule rule) {
    _rules.add(rule);
    _saveClassificationData();
  }

  /// Remove rule
  void removeRule(String ruleId) {
    _rules.removeWhere((rule) => rule.id == ruleId);
    _saveClassificationData();
  }

  /// Update rule
  void updateRule(ClassificationRule updatedRule) {
    final index = _rules.indexWhere((rule) => rule.id == updatedRule.id);
    if (index != -1) {
      _rules[index] = updatedRule;
      _saveClassificationData();
    }
  }

  /// Update classification statistics
  void _updateStatistics(bool isCorrect) {
    _totalClassifications.value++;

    // Simple accuracy calculation - in a real app, this would be more sophisticated
    if (isCorrect) {
      _accuracyRate.value = (_accuracyRate.value * (_totalClassifications.value - 1) + 1.0) / _totalClassifications.value;
    } else {
      _accuracyRate.value = (_accuracyRate.value * (_totalClassifications.value - 1)) / _totalClassifications.value;
    }

    // Ensure accuracy rate stays within bounds
    _accuracyRate.value = _accuracyRate.value.clamp(0.0, 1.0);
  }

  /// Get classification statistics
  Map<String, dynamic> getStatistics() {
    return {
      'total_classifications': _totalClassifications.value,
      'accuracy_rate': _accuracyRate.value,
      'last_updated': DateTime.now().toIso8601String(),
    };
  }
}
