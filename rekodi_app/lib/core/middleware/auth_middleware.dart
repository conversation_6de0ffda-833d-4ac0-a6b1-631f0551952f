import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/auth_service.dart';
import '../routes/app_routes.dart';

class AuthMiddleware extends GetMiddleware {
  @override
  int? get priority => 1;

  @override
  RouteSettings? redirect(String? route) {
    final authService = Get.find<AuthService>();
    
    // If user is not authenticated and trying to access protected routes
    if (!authService.isAuthenticated && route != AppRoutes.login && route != AppRoutes.signup && route != AppRoutes.onboarding) {
      return const RouteSettings(name: AppRoutes.login);
    }
    
    // If user is authenticated and trying to access auth routes
    if (authService.isAuthenticated && (route == AppRoutes.login || route == AppRoutes.signup)) {
      return const RouteSettings(name: AppRoutes.dashboard);
    }
    
    return null;
  }
}

class AuthGuard {
  static bool get isAuthenticated {
    try {
      final authService = Get.find<AuthService>();
      return authService.isAuthenticated;
    } catch (e) {
      return false;
    }
  }
  
  static void checkAuthAndNavigate() {
    if (isAuthenticated) {
      Get.offAllNamed(AppRoutes.dashboard);
    } else {
      Get.offAllNamed(AppRoutes.login);
    }
  }
}
