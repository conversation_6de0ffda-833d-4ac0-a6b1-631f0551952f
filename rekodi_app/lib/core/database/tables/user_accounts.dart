import 'package:drift/drift.dart';

@DataClassName('UserAccount')
class UserAccounts extends Table {
  TextColumn get id => text()();
  TextColumn get name => text()();
  TextColumn get email => text()();
  TextColumn get accountType => text()(); // 'personal' or 'business'
  BoolColumn get isActive => boolean().withDefault(const Constant(false))();
  TextColumn get profileImageUrl => text().nullable()();
  TextColumn get currency => text().withDefault(const Constant('USD'))();
  TextColumn get timezone => text().nullable()();
  BoolColumn get isPremium => boolean().withDefault(const Constant(false))();
  DateTimeColumn get premiumExpiresAt => dateTime().nullable()();
  DateTimeColumn get createdAt => dateTime()();
  DateTimeColumn get updatedAt => dateTime().nullable()();
  DateTimeColumn get lastLoginAt => dateTime().nullable()();

  @override
  Set<Column> get primaryKey => {id};
}
