import 'package:drift/drift.dart';

@DataClassName('PersonalTransaction')
class PersonalTransactions extends Table {
  TextColumn get id => text()();
  TextColumn get accountId => text()(); // Foreign key to UserAccounts
  TextColumn get title => text()();
  TextColumn get description => text().nullable()();
  RealColumn get amount => real()();
  TextColumn get type => text()(); // 'income' or 'expense'
  TextColumn get category => text()();
  DateTimeColumn get date => dateTime()();
  TextColumn get paymentMethod => text().nullable()(); // 'cash', 'card', 'bank_transfer', etc.
  RealColumn get transactionCost => real().withDefault(const Constant(0.0))(); // Transaction fees/charges
  TextColumn get location => text().nullable()();
  TextColumn get tags => text().nullable()(); // JSON array of tags
  TextColumn get receiptImageUrl => text().nullable()();
  BoolColumn get isRecurring => boolean().withDefault(const Constant(false))();
  TextColumn get recurringPattern => text().nullable()(); // 'daily', 'weekly', 'monthly', 'yearly'
  DateTimeColumn get nextRecurringDate => dateTime().nullable()();
  BoolColumn get isSynced => boolean().withDefault(const Constant(false))();
  DateTimeColumn get createdAt => dateTime()();
  DateTimeColumn get updatedAt => dateTime().nullable()();

  @override
  Set<Column> get primaryKey => {id};
}
