import 'dart:io';
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

import 'tables/user_accounts.dart';
import 'tables/personal_transactions.dart';
import 'tables/account_settings.dart';
import 'tables/sync_status.dart';

part 'database.g.dart';

@DriftDatabase(tables: [
  UserAccounts,
  PersonalTransactions,
  AccountSettings,
  SyncStatus,
])
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  @override
  int get schemaVersion => 1;

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onCreate: (Migrator m) async {
        await m.createAll();
        
        // Create default personal account if none exists
        final existingAccounts = await select(userAccounts).get();
        if (existingAccounts.isEmpty) {
          await into(userAccounts).insert(UserAccountsCompanion.insert(
            id: 'default_personal',
            name: 'Personal Account',
            email: '<EMAIL>',
            accountType: 'personal',
            isActive: const Value(true),
            createdAt: DateTime.now(),
          ));
        }
      },
      onUpgrade: (Migrator m, int from, int to) async {
        // Handle future schema upgrades
      },
    );
  }

  // User Account Methods
  Future<List<UserAccount>> getAllAccounts() => select(userAccounts).get();
  
  Future<UserAccount?> getActiveAccount() => 
    (select(userAccounts)..where((tbl) => tbl.isActive.equals(true))).getSingleOrNull();
  
  Future<UserAccount?> getAccountById(String id) =>
    (select(userAccounts)..where((tbl) => tbl.id.equals(id))).getSingleOrNull();

  Future<void> switchAccount(String accountId) async {
    await transaction(() async {
      // Deactivate all accounts
      await (update(userAccounts)..where((tbl) => tbl.isActive.equals(true)))
          .write(const UserAccountsCompanion(isActive: Value(false)));
      
      // Activate selected account
      await (update(userAccounts)..where((tbl) => tbl.id.equals(accountId)))
          .write(const UserAccountsCompanion(isActive: Value(true)));
    });
  }

  Future<int> createAccount({
    required String id,
    required String name,
    required String email,
    required String accountType,
    bool makeActive = false,
  }) async {
    return await transaction(() async {
      if (makeActive) {
        // Deactivate all other accounts
        await (update(userAccounts)..where((tbl) => tbl.isActive.equals(true)))
            .write(const UserAccountsCompanion(isActive: Value(false)));
      }
      
      return await into(userAccounts).insert(UserAccountsCompanion.insert(
        id: id,
        name: name,
        email: email,
        accountType: accountType,
        isActive: Value(makeActive),
        createdAt: DateTime.now(),
      ));
    });
  }

  // Personal Transaction Methods
  Future<List<PersonalTransaction>> getTransactionsForAccount(String accountId) =>
    (select(personalTransactions)..where((tbl) => tbl.accountId.equals(accountId))).get();

  Future<List<PersonalTransaction>> getRecentTransactions(String accountId, {int limit = 10}) =>
    (select(personalTransactions)
      ..where((tbl) => tbl.accountId.equals(accountId))
      ..orderBy([(tbl) => OrderingTerm.desc(tbl.date)])
      ..limit(limit)).get();

  Future<List<PersonalTransaction>> getTransactionsByDateRange(
    String accountId,
    DateTime startDate,
    DateTime endDate,
  ) =>
    (select(personalTransactions)
      ..where((tbl) => 
        tbl.accountId.equals(accountId) &
        tbl.date.isBetweenValues(startDate, endDate))
      ..orderBy([(tbl) => OrderingTerm.desc(tbl.date)])).get();

  Future<double> getTotalIncomeForAccount(String accountId, {DateTime? startDate, DateTime? endDate}) async {
    final query = selectOnly(personalTransactions)
      ..addColumns([personalTransactions.amount.sum()])
      ..where(personalTransactions.accountId.equals(accountId) & 
              personalTransactions.type.equals('income'));
    
    if (startDate != null && endDate != null) {
      query.where(personalTransactions.date.isBetweenValues(startDate, endDate));
    }
    
    final result = await query.getSingleOrNull();
    return result?.read(personalTransactions.amount.sum()) ?? 0.0;
  }

  Future<double> getTotalExpensesForAccount(String accountId, {DateTime? startDate, DateTime? endDate}) async {
    final query = selectOnly(personalTransactions)
      ..addColumns([personalTransactions.amount.sum()])
      ..where(personalTransactions.accountId.equals(accountId) & 
              personalTransactions.type.equals('expense'));
    
    if (startDate != null && endDate != null) {
      query.where(personalTransactions.date.isBetweenValues(startDate, endDate));
    }
    
    final result = await query.getSingleOrNull();
    return result?.read(personalTransactions.amount.sum()) ?? 0.0;
  }

  Future<int> addTransaction(PersonalTransactionsCompanion transaction) =>
    into(personalTransactions).insert(transaction);

  Future<bool> updateTransaction(String id, PersonalTransactionsCompanion transaction) async {
    final result = await (update(personalTransactions)..where((tbl) => tbl.id.equals(id))).write(transaction);
    return result > 0;
  }

  Future<int> deleteTransaction(String id) =>
    (delete(personalTransactions)..where((tbl) => tbl.id.equals(id))).go();

  // Account Settings Methods
  Future<AccountSetting?> getAccountSetting(String accountId, String key) =>
    (select(accountSettings)
      ..where((tbl) => tbl.accountId.equals(accountId) & tbl.key.equals(key)))
        .getSingleOrNull();

  Future<void> setAccountSetting(String accountId, String key, String value) async {
    await into(accountSettings).insertOnConflictUpdate(
      AccountSettingsCompanion.insert(
        accountId: accountId,
        key: key,
        value: value,
        updatedAt: DateTime.now(),
      ),
    );
  }

  Future<Map<String, String>> getAllAccountSettings(String accountId) async {
    final settings = await (select(accountSettings)
      ..where((tbl) => tbl.accountId.equals(accountId))).get();
    
    return Map.fromEntries(
      settings.map((setting) => MapEntry(setting.key, setting.value)),
    );
  }

  // Sync Status Methods
  Future<SyncStatu?> getSyncStatus(String accountId) =>
    (select(syncStatus)..where((tbl) => tbl.accountId.equals(accountId))).getSingleOrNull();

  Future<void> updateSyncStatus(String accountId, {
    DateTime? lastSyncAt,
    bool? hasUnsyncedChanges,
    String? syncError,
  }) async {
    await into(syncStatus).insertOnConflictUpdate(
      SyncStatusCompanion.insert(
        accountId: accountId,
        lastSyncAt: Value(lastSyncAt),
        hasUnsyncedChanges: Value(hasUnsyncedChanges ?? false),
        syncError: Value(syncError),
        updatedAt: DateTime.now(),
      ),
    );
  }
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'rekodi_app.db'));
    return NativeDatabase.createInBackground(file);
  });
}
