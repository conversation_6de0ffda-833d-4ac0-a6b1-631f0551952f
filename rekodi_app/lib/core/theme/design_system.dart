import 'package:flutter/material.dart';

/// Rekodi (Smartledger) Design System
/// Modern, clean, and elegant design with sophisticated color palette
class DesignSystem {
  // Private constructor to prevent instantiation
  DesignSystem._();

  /// Primary Accent (Blue-Green Gradient)
  /// Subtle gradient from soft teal to gentle sky blue
  static const Color primaryTeal = Color(0xFF5CC9B6);
  static const Color primarySkyBlue = Color(0xFF4D9DE0);
  static const Color primaryColor = primaryTeal; // Main primary color reference
  static const Color borderLight = Color(0xFFF0F0F0);

  /// Secondary Accent (Warm Coral/Orange)
  /// Sophisticated coral for expense indicators and warnings
  static const Color secondaryCoral = Color(0xFFFF7F6F);
  
  /// Neutral Backgrounds
  static const Color neutralBackground = Color(0xFFF8F9FB); // Very light, almost off-white gray
  static const Color cardBackground = Color(0xFFFFFFFF); // Pure white for crispness
  
  /// Text Colors
  static const Color textPrimary = Color(0xFF2C3E50); // Dark charcoal gray
  static const Color textSecondary = Color(0xFF7F8C8D); // Lighter gray for secondary info
  static const Color textOnPrimary = Color(0xFFFFFFFF); // White text on colored backgrounds

  /// Common Colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);

  /// Background Colors
  static const Color backgroundPrimary = Color(0xFFF8F9FB);
  static const Color backgroundSecondary = Color(0xFFFFFFFF);
  
  /// Shadow System
  static const Color shadowColor = Color(0x14000000); // rgba(0,0,0,0.08) - subtle, diffused shadows

  /// Status Colors
  static const Color success = Color(0xFF27AE60);
  static const Color successColor = success; // Alias for compatibility
  static const Color warning = Color(0xFFF39C12);
  static const Color warningColor = Color(0xFFF39C12);
  static const Color error = Color(0xFFE74C3C);
  static const Color errorColor = error; // Alias for compatibility
  static const Color info = primarySkyBlue;
  static const Color infoColor = info; // Alias for compatibility
  static const Color backgroundColor = neutralBackground;
  
  /// Gradients
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primaryTeal, primarySkyBlue],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient incomeGradient = LinearGradient(
    colors: [Color(0xFF27AE60), Color(0xFF2ECC71)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient expenseGradient = LinearGradient(
    colors: [secondaryCoral, Color(0xFFFF6B6B)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  /// Border Radius
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  static const double radiusXLarge = 24.0;
  
  /// Spacing System
  static const double spaceXS = 4.0;
  static const double spaceS = 8.0;
  static const double spaceM = 16.0;
  static const double spaceL = 24.0;
  static const double spaceXL = 32.0;
  static const double spaceXXL = 48.0;

  /// Alternative Spacing Names (for consistency)
  static const double spacingXSmall = spaceXS;
  static const double spacingSmall = spaceS;
  static const double spacingMedium = spaceM;
  static const double spacingLarge = spaceL;
  static const double spacingXLarge = spaceXL;
  static const double spacingXXLarge = spaceXXL;
  
  /// Elevation System
  static const double elevationLow = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;
  static const double elevationXHigh = 16.0;
  
  /// Box Shadows
  static List<BoxShadow> get shadowLow => [
    BoxShadow(
      color: shadowColor,
      blurRadius: 4,
      offset: const Offset(0, 2),
    ),
  ];

  static List<BoxShadow> get shadowSmall => [
    BoxShadow(
      color: shadowColor,
      blurRadius: 4,
      offset: const Offset(0, 2),
    ),
  ];
  
  static List<BoxShadow> get shadowMedium => [
    BoxShadow(
      color: shadowColor,
      blurRadius: 8,
      offset: const Offset(0, 4),
    ),
  ];
  
  static List<BoxShadow> get shadowHigh => [
    BoxShadow(
      color: shadowColor,
      blurRadius: 16,
      offset: const Offset(0, 8),
    ),
  ];

  static List<BoxShadow> get shadowLarge => [
    BoxShadow(
      color: primaryTeal.withOpacity(0.3),
      blurRadius: 20,
      offset: const Offset(0, 8),
    ),
  ];
  
  /// Frosted Glass Effect for Navigation
  static BoxDecoration get frostedGlassDecoration => BoxDecoration(
    color: cardBackground.withOpacity(0.8),
    borderRadius: const BorderRadius.only(
      topLeft: Radius.circular(radiusLarge),
      topRight: Radius.circular(radiusLarge),
    ),
    boxShadow: shadowMedium,
    border: Border.all(
      color: textSecondary.withOpacity(0.1),
      width: 0.5,
    ),
  );
  
  /// Card Decoration
  static BoxDecoration get cardDecoration => BoxDecoration(
    color: cardBackground,
    borderRadius: BorderRadius.circular(radiusLarge),
    boxShadow: shadowLow,
  );
  
  /// Hero Card Decoration (for balance snapshot)
  static BoxDecoration get heroCardDecoration => BoxDecoration(
    gradient: primaryGradient,
    borderRadius: BorderRadius.circular(radiusLarge),
    boxShadow: shadowMedium,
  );
  
  /// Input Field Decoration
  static InputDecoration getInputDecoration({
    required String label,
    String? hint,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) => InputDecoration(
    labelText: label,
    hintText: hint,
    prefixIcon: prefixIcon,
    suffixIcon: suffixIcon,
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(radiusMedium),
      borderSide: BorderSide(color: textSecondary.withOpacity(0.3)),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(radiusMedium),
      borderSide: BorderSide(color: textSecondary.withOpacity(0.3)),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(radiusMedium),
      borderSide: const BorderSide(color: primaryTeal, width: 2),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(radiusMedium),
      borderSide: const BorderSide(color: error, width: 2),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(radiusMedium),
      borderSide: const BorderSide(color: error, width: 2),
    ),
    filled: true,
    fillColor: cardBackground,
    contentPadding: const EdgeInsets.symmetric(
      horizontal: spaceM,
      vertical: spaceM,
    ),
  );
  
  /// Button Styles
  static ButtonStyle get primaryButtonStyle => ElevatedButton.styleFrom(
    backgroundColor: primaryTeal,
    foregroundColor: textOnPrimary,
    elevation: elevationLow,
    shadowColor: shadowColor,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(radiusMedium),
    ),
    padding: const EdgeInsets.symmetric(
      horizontal: spaceL,
      vertical: spaceM,
    ),
  );
  
  static ButtonStyle get secondaryButtonStyle => OutlinedButton.styleFrom(
    foregroundColor: primaryTeal,
    side: const BorderSide(color: primaryTeal),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(radiusMedium),
    ),
    padding: const EdgeInsets.symmetric(
      horizontal: spaceL,
      vertical: spaceM,
    ),
  );
  
  /// Gradient Button Decoration
  static BoxDecoration get gradientButtonDecoration => BoxDecoration(
    gradient: primaryGradient,
    borderRadius: BorderRadius.circular(radiusMedium),
    boxShadow: shadowLow,
  );
  
  /// Category Icon Background
  static BoxDecoration getCategoryIconDecoration(Color color) => BoxDecoration(
    color: color.withOpacity(0.1),
    borderRadius: BorderRadius.circular(radiusSmall),
  );
  
  /// Progress Bar Decoration
  static BoxDecoration get progressBarBackground => BoxDecoration(
    color: textSecondary.withOpacity(0.1),
    borderRadius: BorderRadius.circular(radiusSmall),
  );
  
  /// Chip Decoration
  static BoxDecoration get chipDecoration => BoxDecoration(
    color: neutralBackground,
    borderRadius: BorderRadius.circular(radiusLarge),
    border: Border.all(
      color: textSecondary.withOpacity(0.2),
      width: 1,
    ),
  );
  
  /// Animation Durations
  static const Duration animationFast = Duration(milliseconds: 200);
  static const Duration animationMedium = Duration(milliseconds: 300);
  static const Duration animationSlow = Duration(milliseconds: 500);
  
  /// Curves
  static const Curve defaultCurve = Curves.easeInOut;
  static const Curve bounceCurve = Curves.elasticOut;
  static const Curve smoothCurve = Curves.easeOutCubic;

  // static var warningColor = ;
}
