import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'design_system.dart';
import 'typography.dart';

/// Sophisticated Theme Implementation for Rekodi (Smartledger)
/// Modern, clean, and elegant with focus on smooth transitions and thoughtful design
class SophisticatedTheme {
  // Private constructor
  SophisticatedTheme._();

  /// Light Theme - Primary theme for the app
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      
      // Color Scheme based on our sophisticated palette
      colorScheme:  ColorScheme.light(
        primary: DesignSystem.primaryTeal,
        primaryContainer: DesignSystem.primarySkyBlue,
        secondary: DesignSystem.secondaryCoral,
        secondaryContainer: Color(0xFFFFE5E1), // Light coral background
        tertiary: DesignSystem.success,
        tertiaryContainer: Color(0xFFE8F5E8), // Light green background
        surface: DesignSystem.cardBackground,
        surfaceVariant: DesignSystem.neutralBackground,
        background: DesignSystem.neutralBackground,
        error: DesignSystem.error,
        errorContainer: Color(0xFFFFEBEE), // Light red background
        onPrimary: DesignSystem.textOnPrimary,
        onPrimaryContainer: DesignSystem.textPrimary,
        onSecondary: DesignSystem.textOnPrimary,
        onSecondaryContainer: DesignSystem.textPrimary,
        onTertiary: DesignSystem.textOnPrimary,
        onTertiaryContainer: DesignSystem.textPrimary,
        onSurface: DesignSystem.textPrimary,
        onSurfaceVariant: DesignSystem.textSecondary,
        onBackground: DesignSystem.textPrimary,
        onError: DesignSystem.textOnPrimary,
        onErrorContainer: DesignSystem.textPrimary,
        outline: DesignSystem.textSecondary.withOpacity(0.3),
        outlineVariant: DesignSystem.textSecondary.withOpacity(0.1),
        shadow: DesignSystem.shadowColor,
        scrim: Colors.black54,
        inverseSurface: DesignSystem.textPrimary,
        onInverseSurface: DesignSystem.textOnPrimary,
        inversePrimary: DesignSystem.primarySkyBlue,
      ),
      
      // Typography
      textTheme: AppTypography.textTheme,
      
      // App Bar Theme - Clean and minimal
      appBarTheme: AppBarTheme(
        backgroundColor: DesignSystem.cardBackground,
        foregroundColor: DesignSystem.textPrimary,
        elevation: 0,
        scrolledUnderElevation: DesignSystem.elevationLow,
        shadowColor: DesignSystem.shadowColor,
        surfaceTintColor: Colors.transparent,
        centerTitle: false,
        titleTextStyle: AppTypography.headlineSmall,
        toolbarTextStyle: AppTypography.bodyMedium,
        iconTheme: const IconThemeData(
          color: DesignSystem.textPrimary,
          size: 24,
        ),
        actionsIconTheme: const IconThemeData(
          color: DesignSystem.textPrimary,
          size: 24,
        ),
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
        ),
      ),
      
      // Card Theme - Elegant cards with subtle shadows
      cardTheme: CardTheme(
        color: DesignSystem.cardBackground,
        elevation: DesignSystem.elevationLow,
        shadowColor: DesignSystem.shadowColor,
        surfaceTintColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
        ),
        margin: const EdgeInsets.symmetric(
          horizontal: DesignSystem.spaceS,
          vertical: DesignSystem.spaceS,
        ),
      ),
      
      // Elevated Button Theme - Gradient-style buttons
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: DesignSystem.primaryButtonStyle.copyWith(
          textStyle: MaterialStateProperty.all(AppTypography.buttonMedium),
          minimumSize: MaterialStateProperty.all(const Size(120, 48)),
        ),
      ),
      
      // Outlined Button Theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: DesignSystem.secondaryButtonStyle.copyWith(
          textStyle: MaterialStateProperty.all(AppTypography.buttonMedium),
          minimumSize: MaterialStateProperty.all(const Size(120, 48)),
        ),
      ),
      
      // Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: DesignSystem.primaryTeal,
          textStyle: AppTypography.buttonMedium,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: DesignSystem.spaceM,
            vertical: DesignSystem.spaceS,
          ),
        ),
      ),
      
      // Input Decoration Theme - Floating labels with elegant borders
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: DesignSystem.cardBackground,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
          borderSide: BorderSide(
            color: DesignSystem.textSecondary.withOpacity(0.3),
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
          borderSide: BorderSide(
            color: DesignSystem.textSecondary.withOpacity(0.3),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
          borderSide: const BorderSide(
            color: DesignSystem.primaryTeal,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
          borderSide: const BorderSide(
            color: DesignSystem.error,
            width: 2,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
          borderSide: const BorderSide(
            color: DesignSystem.error,
            width: 2,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: DesignSystem.spaceM,
          vertical: DesignSystem.spaceM,
        ),
        hintStyle: AppTypography.inputHint,
        labelStyle: AppTypography.inputLabel,
        floatingLabelStyle: AppTypography.inputLabel.copyWith(
          color: DesignSystem.primaryTeal,
        ),
      ),
      
      // Floating Action Button Theme
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: DesignSystem.primaryTeal,
        foregroundColor: DesignSystem.textOnPrimary,
        elevation: DesignSystem.elevationMedium,
        focusElevation: DesignSystem.elevationHigh,
        hoverElevation: DesignSystem.elevationMedium,
        highlightElevation: DesignSystem.elevationHigh,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
        ),
      ),
      
      // Bottom Navigation Bar Theme - Frosted glass effect
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: DesignSystem.cardBackground.withOpacity(0.9),
        selectedItemColor: DesignSystem.primaryTeal,
        unselectedItemColor: DesignSystem.textSecondary,
        selectedLabelStyle: AppTypography.navigationLabelActive,
        unselectedLabelStyle: AppTypography.navigationLabel,
        type: BottomNavigationBarType.fixed,
        elevation: DesignSystem.elevationMedium,
      ),
      
      // Chip Theme
      chipTheme: ChipThemeData(
        backgroundColor: DesignSystem.neutralBackground,
        selectedColor: DesignSystem.primaryTeal.withOpacity(0.1),
        disabledColor: DesignSystem.textSecondary.withOpacity(0.1),
        deleteIconColor: DesignSystem.textSecondary,
        labelStyle: AppTypography.labelMedium,
        secondaryLabelStyle: AppTypography.labelSmall,
        brightness: Brightness.light,
        padding: const EdgeInsets.symmetric(
          horizontal: DesignSystem.spaceM,
          vertical: DesignSystem.spaceS,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
        ),
      ),
      
      // Dialog Theme
      dialogTheme: DialogTheme(
        backgroundColor: DesignSystem.cardBackground,
        elevation: DesignSystem.elevationHigh,
        shadowColor: DesignSystem.shadowColor,
        surfaceTintColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
        ),
        titleTextStyle: AppTypography.headlineSmall,
        contentTextStyle: AppTypography.bodyMedium,
      ),
      
      // Divider Theme
      dividerTheme: DividerThemeData(
        color: DesignSystem.textSecondary.withOpacity(0.1),
        thickness: 1,
        space: 1,
      ),
      
      // Icon Theme
      iconTheme: const IconThemeData(
        color: DesignSystem.textSecondary,
        size: 24,
      ),
      
      // Primary Icon Theme
      primaryIconTheme: const IconThemeData(
        color: DesignSystem.textOnPrimary,
        size: 24,
      ),
      
      // Switch Theme
      switchTheme: SwitchThemeData(
        thumbColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return DesignSystem.primaryTeal;
          }
          return DesignSystem.textSecondary.withOpacity(0.5);
        }),
        trackColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return DesignSystem.primaryTeal.withOpacity(0.3);
          }
          return DesignSystem.textSecondary.withOpacity(0.1);
        }),
      ),
      
      // Progress Indicator Theme
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: DesignSystem.primaryTeal,
        linearTrackColor: Color(0xFFE0E0E0),
        circularTrackColor: Color(0xFFE0E0E0),
      ),
      
      // Scaffold Background
      scaffoldBackgroundColor: DesignSystem.neutralBackground,
      
      // Visual Density
      visualDensity: VisualDensity.adaptivePlatformDensity,
      
      // Material Tap Target Size
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      
      // Page Transitions
      pageTransitionsTheme: const PageTransitionsTheme(
        builders: {
          TargetPlatform.android: CupertinoPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        },
      ),
    );
  }

  /// Dark Theme - For future implementation
  static ThemeData get darkTheme {
    // For now, return light theme
    // TODO: Implement sophisticated dark theme
    return lightTheme;
  }
}
