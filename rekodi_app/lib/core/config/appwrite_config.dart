class AppwriteConfig {
  // Appwrite Configuration
  static const String APPWRITE_PROJECT_ID = "683c61460026c3e03330";
  static const String projectId = APPWRITE_PROJECT_ID;
  static const String endpoint = "https://cloud.appwrite.io/v1";

  // API Key Configuration
  static const String apiKeyName = "appwrite_api";
  static const String _apiKeySecret = "standard_abc2cc610aa19bd38038daa25ce303e26ade989b481d9b6a05f95438ac88ba8ff4d06caa10d67b14157bd04030885e2a5cc0d84c8e3d816216c62f92a668d7fcdcbd2c2af1a50b131d09c4a69f9644d165ac90b62079d49fc24ecce167fbbb7638385a8521d245480a69217ff0a9097ad361b6656499c882c65cd5e7eea4facb";

  // Database Configuration
  static const String databaseId = "rekodi_main_db";
  
  // Collection IDs
  static const String usersCollectionId = "users";
  static const String accountsCollectionId = "user_accounts";
  static const String transactionsCollectionId = "transactions";
  static const String inventoryCollectionId = "inventory_items";
  static const String categoriesCollectionId = "categories";
  static const String settingsCollectionId = "user_settings";

  // SMS and Auto-Classification Collections
  static const String smsTransactionsCollectionId = "sms_transactions";
  static const String smsPatternsCollectionId = "sms_patterns";
  static const String merchantMappingsCollectionId = "merchant_mappings";
  static const String transactionRulesCollectionId = "transaction_rules";

  // Budget and Analytics Collections
  static const String budgetsCollectionId = "budgets";
  static const String recurringTransactionsCollectionId = "recurring_transactions";
  static const String analyticsCollectionId = "analytics_data";
  
  // Storage Configuration
  static const String storageId = "rekodi_storage";
  
  // Function IDs (for future use)
  static const String syncFunctionId = "sync_data";
  static const String backupFunctionId = "backup_data";
  
  // Authentication Providers
  static const List<String> enabledProviders = [
    'email',
    'google',
    'apple',
  ];
  
  // App Configuration
  static const String appName = "Rekodi";
  static const String appVersion = "1.0.0";
  
  // Sync Configuration
  static const Duration syncInterval = Duration(minutes: 5);
  static const Duration offlineTimeout = Duration(seconds: 30);
  
  // Pagination
  static const int defaultPageSize = 25;
  static const int maxPageSize = 100;
  
  // File Upload Configuration
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = [
    'image/jpeg',
    'image/png',
    'image/webp',
  ];
  
  // Security Configuration
  static const Duration sessionTimeout = Duration(days: 30);
  static const bool enableBiometricAuth = true;
  
  // Feature Flags
  static const bool enableOfflineMode = true;
  static const bool enableDataSync = true;
  static const bool enableCloudBackup = true;
  static const bool enableAnalytics = false; // Set to true when ready
  
  // Error Messages
  static const String networkErrorMessage = "Please check your internet connection and try again.";
  static const String serverErrorMessage = "Server error occurred. Please try again later.";
  static const String authErrorMessage = "Authentication failed. Please login again.";
  static const String syncErrorMessage = "Data sync failed. Your data is saved locally.";

  // API Key Management
  /// Get the API key for server-side operations
  /// This should only be used in secure contexts
  static String get apiKey => _apiKeySecret;

  /// Check if API key is configured
  static bool get hasApiKey => _apiKeySecret.isNotEmpty;

  /// Get API key headers for server requests
  static Map<String, String> get apiHeaders => {
    'X-Appwrite-Project': projectId,
    'X-Appwrite-Key': apiKey,
    'Content-Type': 'application/json',
  };
}
