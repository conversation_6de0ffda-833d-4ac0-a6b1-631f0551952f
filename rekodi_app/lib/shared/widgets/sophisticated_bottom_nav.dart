import 'package:flutter/material.dart';
import 'dart:ui';
import '../../core/theme/design_system.dart';
import '../../core/theme/typography.dart';

/// Sophisticated Bottom Navigation with Frosted Glass Effect
/// Beautiful navigation bar with translucent background, gradient animations, and elegant FAB
class SophisticatedBottomNav extends StatefulWidget {
  final int currentIndex;
  final Function(int) onTap;
  final VoidCallback onFabPressed;
  final List<SophisticatedNavItem> items;
  final bool showLabels;
  final double height;

  const SophisticatedBottomNav({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.onFabPressed,
    required this.items,
    this.showLabels = true,
    this.height = 80,
  });

  @override
  State<SophisticatedBottomNav> createState() => _SophisticatedBottomNavState();
}

class _SophisticatedBottomNavState extends State<SophisticatedBottomNav>
    with TickerProviderStateMixin {
  late AnimationController _fabAnimationController;
  late AnimationController _rippleAnimationController;
  late Animation<double> _fabScaleAnimation;
  late Animation<double> _fabRotationAnimation;
  late Animation<double> _rippleAnimation;

  int? _lastTappedIndex;

  @override
  void initState() {
    super.initState();
    
    _fabAnimationController = AnimationController(
      duration: DesignSystem.animationMedium,
      vsync: this,
    );
    
    _rippleAnimationController = AnimationController(
      duration: DesignSystem.animationFast,
      vsync: this,
    );

    _fabScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _fabAnimationController,
      curve: DesignSystem.defaultCurve,
    ));

    _fabRotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.125, // 45 degrees
    ).animate(CurvedAnimation(
      parent: _fabAnimationController,
      curve: DesignSystem.defaultCurve,
    ));

    _rippleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rippleAnimationController,
      curve: DesignSystem.defaultCurve,
    ));
  }

  @override
  void dispose() {
    _fabAnimationController.dispose();
    _rippleAnimationController.dispose();
    super.dispose();
  }

  void _onFabTap() {
    _fabAnimationController.forward().then((_) {
      _fabAnimationController.reverse();
    });
    widget.onFabPressed();
  }

  void _onNavItemTap(int index) {
    setState(() {
      _lastTappedIndex = index;
    });
    _rippleAnimationController.forward().then((_) {
      _rippleAnimationController.reset();
    });
    widget.onTap(index);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height + MediaQuery.of(context).padding.bottom,
      child: Stack(
        children: [
          // Frosted glass background
          Positioned.fill(
            child: ClipPath(
              clipper: _BottomNavClipper(),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        DesignSystem.cardBackground.withOpacity(0.8),
                        DesignSystem.cardBackground.withOpacity(0.9),
                      ],
                    ),
                    border: Border(
                      top: BorderSide(
                        color: DesignSystem.textSecondary.withOpacity(0.1),
                        width: 1,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),

          // Navigation items
          Positioned(
            left: 0,
            right: 0,
            top: 0,
            height: widget.height,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: _buildNavItems(),
            ),
          ),

          // Floating Action Button
          Positioned(
            top: -20,
            left: MediaQuery.of(context).size.width / 2 - 28,
            child: AnimatedBuilder(
              animation: _fabAnimationController,
              builder: (context, child) {
                return Transform.scale(
                  scale: _fabScaleAnimation.value,
                  child: Transform.rotate(
                    angle: _fabRotationAnimation.value * 2 * 3.14159,
                    child: _buildFloatingActionButton(),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildNavItems() {
    List<Widget> items = [];
    
    for (int i = 0; i < widget.items.length; i++) {
      if (i == widget.items.length ~/ 2) {
        // Add spacer for FAB
        items.add(const SizedBox(width: 56));
      }
      
      items.add(_buildNavItem(widget.items[i], i));
    }
    
    return items;
  }

  Widget _buildNavItem(SophisticatedNavItem item, int index) {
    final isSelected = widget.currentIndex == index;
    final showRipple = _lastTappedIndex == index;

    return GestureDetector(
      onTap: () => _onNavItemTap(index),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: DesignSystem.spaceS),
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Ripple effect
            if (showRipple)
              AnimatedBuilder(
                animation: _rippleAnimation,
                builder: (context, child) {
                  return Container(
                    width: 40 * _rippleAnimation.value,
                    height: 40 * _rippleAnimation.value,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: DesignSystem.primaryTeal.withOpacity(
                        0.3 * (1 - _rippleAnimation.value),
                      ),
                    ),
                  );
                },
              ),

            // Nav item content
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icon container with gradient background for selected state
                AnimatedContainer(
                  duration: DesignSystem.animationMedium,
                  curve: DesignSystem.defaultCurve,
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    gradient: isSelected ? DesignSystem.primaryGradient : null,
                    color: isSelected ? null : Colors.transparent,
                    borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
                    boxShadow: isSelected ? DesignSystem.shadowLow : null,
                  ),
                  child: Icon(
                    isSelected ? item.activeIcon : item.icon,
                    color: isSelected 
                        ? DesignSystem.textOnPrimary
                        : DesignSystem.textSecondary,
                    size: 24,
                  ),
                ),

                // Label
                if (widget.showLabels) ...[
                  const SizedBox(height: DesignSystem.spaceXS),
                  AnimatedDefaultTextStyle(
                    duration: DesignSystem.animationMedium,
                    curve: DesignSystem.defaultCurve,
                    style: AppTypography.labelSmall.copyWith(
                      color: isSelected 
                          ? DesignSystem.primaryTeal
                          : DesignSystem.textSecondary,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    ),
                    child: Text(item.label),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        gradient: DesignSystem.primaryGradient,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: DesignSystem.primaryTeal.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
          ...DesignSystem.shadowMedium,
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _onFabTap,
          borderRadius: BorderRadius.circular(28),
          child: const Icon(
            Icons.add_rounded,
            color: DesignSystem.textOnPrimary,
            size: 28,
          ),
        ),
      ),
    );
  }
}

/// Custom clipper for curved bottom navigation
class _BottomNavClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    
    // Start from top-left
    path.moveTo(0, 20);
    
    // Top-left curve
    path.quadraticBezierTo(0, 0, 20, 0);
    
    // Top edge to FAB curve start
    path.lineTo(size.width / 2 - 40, 0);
    
    // FAB curve
    path.quadraticBezierTo(size.width / 2 - 20, 0, size.width / 2 - 20, 20);
    path.quadraticBezierTo(size.width / 2, -10, size.width / 2 + 20, 20);
    path.quadraticBezierTo(size.width / 2 + 20, 0, size.width / 2 + 40, 0);
    
    // Top edge to top-right
    path.lineTo(size.width - 20, 0);
    
    // Top-right curve
    path.quadraticBezierTo(size.width, 0, size.width, 20);
    
    // Right edge
    path.lineTo(size.width, size.height);
    
    // Bottom edge
    path.lineTo(0, size.height);
    
    // Close path
    path.close();
    
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) => false;
}

/// Navigation item data class
class SophisticatedNavItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;

  const SophisticatedNavItem({
    required this.icon,
    required this.label,
    IconData? activeIcon,
  }) : activeIcon = activeIcon ?? icon;
}
