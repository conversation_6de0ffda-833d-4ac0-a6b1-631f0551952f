import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../core/theme/design_system.dart';
import '../../core/theme/typography.dart';

/// Compact Date Picker with horizontal day slider and calendar icon
/// Minimal design that takes up less screen space
class CompactDatePicker extends StatefulWidget {
  final DateTime? selectedDate;
  final DateTimeRange? selectedRange;
  final Function(DateTime)? onDateSelected;
  final Function(DateTimeRange)? onRangeSelected;
  final bool allowRangeSelection;
  final bool allowFutureDates;
  final String? label;

  const CompactDatePicker({
    super.key,
    this.selectedDate,
    this.selectedRange,
    this.onDateSelected,
    this.onRangeSelected,
    this.allowRangeSelection = false,
    this.allowFutureDates = false,
    this.label,
  });

  @override
  State<CompactDatePicker> createState() => _CompactDatePickerState();
}

class _CompactDatePickerState extends State<CompactDatePicker> {
  late DateTime _currentMonth;
  DateTime? _selectedDate;
  DateTimeRange? _selectedRange;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _currentMonth = DateTime.now();
    _selectedDate = widget.selectedDate ?? DateTime.now();
    _selectedRange = widget.selectedRange;
    _scrollController = ScrollController();
    
    // Auto-scroll to selected day
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToSelectedDay();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToSelectedDay() {
    if (_selectedDate != null && 
        _selectedDate!.month == _currentMonth.month && 
        _selectedDate!.year == _currentMonth.year) {
      final dayIndex = _selectedDate!.day - 1;
      final scrollOffset = dayIndex * 50.0 - 100; // Center the selected day
      _scrollController.animateTo(
        scrollOffset.clamp(0.0, _scrollController.position.maxScrollExtent),
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  int _getDaysInMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0).day;
  }

  void _selectDay(int day) {
    final newDate = DateTime(_currentMonth.year, _currentMonth.month, day);
    setState(() {
      _selectedDate = newDate;
    });
    widget.onDateSelected?.call(newDate);
  }

  void _showCalendarDialog() async {
    if (widget.allowRangeSelection) {
      final DateTimeRange? picked = await showDateRangePicker(
        context: context,
        firstDate: DateTime(2020),
        lastDate: DateTime.now().add(const Duration(days: 365)),
        initialDateRange: _selectedRange,
        builder: (context, child) {
          return Theme(
            data: Theme.of(context).copyWith(
              colorScheme: Theme.of(context).colorScheme.copyWith(
                primary: DesignSystem.primaryTeal,
              ),
            ),
            child: child!,
          );
        },
      );

      if (picked != null) {
        setState(() {
          _selectedRange = picked;
        });
        widget.onRangeSelected?.call(picked);
      }
    } else {
      final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: _selectedDate ?? DateTime.now(),
        firstDate: DateTime(2020),
        lastDate: DateTime.now().add(const Duration(days: 365)),
        builder: (context, child) {
          return Theme(
            data: Theme.of(context).copyWith(
              colorScheme: Theme.of(context).colorScheme.copyWith(
                primary: DesignSystem.primaryTeal,
              ),
            ),
            child: child!,
          );
        },
      );

      if (picked != null) {
        setState(() {
          _selectedDate = picked;
          _currentMonth = DateTime(picked.year, picked.month);
        });
        widget.onDateSelected?.call(picked);
        _scrollToSelectedDay();
      }
    }
  }

  void _changeMonth(int direction) {
    setState(() {
      _currentMonth = DateTime(_currentMonth.year, _currentMonth.month + direction);
    });
  }

  @override
  Widget build(BuildContext context) {
    final daysInMonth = _getDaysInMonth(_currentMonth);
    final today = DateTime.now();
    
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with label and calendar icon
          if (widget.label != null) ...[
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              child: Row(
                children: [
                  Text(
                    widget.label!,
                    style: AppTypography.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: DesignSystem.textPrimary,
                    ),
                  ),
                  const Spacer(),
                  if (_selectedRange != null)
                    Text(
                      '${DateFormat('MMM dd').format(_selectedRange!.start)} - ${DateFormat('MMM dd').format(_selectedRange!.end)}',
                      style: AppTypography.bodySmall.copyWith(
                        color: DesignSystem.textSecondary,
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(height: 8),
          ],
          
          // Month navigation and calendar icon
          Container(
            height: 50,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                // Calendar icon button
                GestureDetector(
                  onTap: _showCalendarDialog,
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: DesignSystem.primaryTeal.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: DesignSystem.primaryTeal.withOpacity(0.3),
                      ),
                    ),
                    child: Icon(
                      Icons.calendar_today_rounded,
                      color: DesignSystem.primaryTeal,
                      size: 20,
                    ),
                  ),
                ),
                
                const SizedBox(width: 12),
                
                // Month navigation
                IconButton(
                  onPressed: () => _changeMonth(-1),
                  icon: const Icon(Icons.chevron_left),
                  iconSize: 20,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                ),
                
                Expanded(
                  child: Center(
                    child: Text(
                      DateFormat('MMMM yyyy').format(_currentMonth),
                      style: AppTypography.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: DesignSystem.textPrimary,
                      ),
                    ),
                  ),
                ),
                
                IconButton(
                  onPressed: () => _changeMonth(1),
                  icon: const Icon(Icons.chevron_right),
                  iconSize: 20,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                ),
              ],
            ),
          ),
          
          // Days horizontal slider
          SizedBox(
            height: 50,
            child: ListView.builder(
              controller: _scrollController,
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: daysInMonth,
              itemBuilder: (context, index) {
                final day = index + 1;
                final date = DateTime(_currentMonth.year, _currentMonth.month, day);
                final isSelected = _selectedDate != null &&
                    _selectedDate!.day == day &&
                    _selectedDate!.month == _currentMonth.month &&
                    _selectedDate!.year == _currentMonth.year;
                final isToday = date.day == today.day &&
                    date.month == today.month &&
                    date.year == today.year;
                final isInRange = _selectedRange != null &&
                    date.isAfter(_selectedRange!.start.subtract(const Duration(days: 1))) &&
                    date.isBefore(_selectedRange!.end.add(const Duration(days: 1)));

                return GestureDetector(
                  onTap: () => _selectDay(day),
                  child: Container(
                    width: 40,
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? DesignSystem.primaryTeal
                          : isInRange
                              ? DesignSystem.primaryTeal.withOpacity(0.2)
                              : isToday
                                  ? DesignSystem.primaryTeal.withOpacity(0.1)
                                  : Colors.transparent,
                      borderRadius: BorderRadius.circular(8),
                      border: isToday && !isSelected
                          ? Border.all(color: DesignSystem.primaryTeal, width: 1)
                          : null,
                    ),
                    child: Center(
                      child: Text(
                        day.toString(),
                        style: AppTypography.bodyMedium.copyWith(
                          color: isSelected
                              ? DesignSystem.white
                              : isToday
                                  ? DesignSystem.primaryTeal
                                  : DesignSystem.textPrimary,
                          fontWeight: isSelected || isToday
                              ? FontWeight.w600
                              : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
