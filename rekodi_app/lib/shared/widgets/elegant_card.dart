import 'package:flutter/material.dart';
import '../../core/theme/design_system.dart';

/// Elegant Card Widget
/// Beautiful card with sophisticated shadows and smooth animations
class ElegantCard extends StatefulWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final LinearGradient? gradient;
  final List<BoxShadow>? boxShadow;
  final VoidCallback? onTap;
  final bool isClickable;
  final double? width;
  final double? height;
  final Border? border;

  const ElegantCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.borderRadius,
    this.backgroundColor,
    this.gradient,
    this.boxShadow,
    this.onTap,
    this.isClickable = false,
    this.width,
    this.height,
    this.border,
  });

  @override
  State<ElegantCard> createState() => _ElegantCardState();
}

class _ElegantCardState extends State<ElegantCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: DesignSystem.animationFast,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: DesignSystem.defaultCurve,
    ));
    _elevationAnimation = Tween<double>(
      begin: 1.0,
      end: 0.5,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: DesignSystem.defaultCurve,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.isClickable || widget.onTap != null) {
      setState(() => _isPressed = true);
      _animationController.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (widget.isClickable || widget.onTap != null) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  void _onTapCancel() {
    if (widget.isClickable || widget.onTap != null) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final borderRadius = widget.borderRadius ?? 
        BorderRadius.circular(DesignSystem.radiusLarge);
    final backgroundColor = widget.backgroundColor ?? DesignSystem.cardBackground;
    final boxShadow = widget.boxShadow ?? DesignSystem.shadowLow;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            width: widget.width,
            height: widget.height,
            margin: widget.margin ?? const EdgeInsets.symmetric(
              horizontal: DesignSystem.spaceS,
              vertical: DesignSystem.spaceS,
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: widget.onTap,
                onTapDown: _onTapDown,
                onTapUp: _onTapUp,
                onTapCancel: _onTapCancel,
                borderRadius: borderRadius,
                splashColor: DesignSystem.primaryTeal.withOpacity(0.1),
                highlightColor: DesignSystem.primaryTeal.withOpacity(0.05),
                child: AnimatedContainer(
                  duration: DesignSystem.animationMedium,
                  curve: DesignSystem.defaultCurve,
                  padding: widget.padding ?? const EdgeInsets.all(DesignSystem.spaceM),
                  decoration: BoxDecoration(
                    color: widget.gradient == null ? backgroundColor : null,
                    gradient: widget.gradient,
                    borderRadius: borderRadius,
                    border: widget.border,
                    boxShadow: boxShadow.map((shadow) => BoxShadow(
                      color: shadow.color,
                      blurRadius: shadow.blurRadius * _elevationAnimation.value,
                      offset: shadow.offset * _elevationAnimation.value,
                      spreadRadius: shadow.spreadRadius,
                    )).toList(),
                  ),
                  child: widget.child,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Hero Card Widget
/// Special card for balance snapshots with gradient background
class HeroCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? borderRadius;
  final LinearGradient? gradient;
  final VoidCallback? onTap;
  final double? width;
  final double? height;

  const HeroCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.borderRadius,
    this.gradient,
    this.onTap,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return ElegantCard(
      onTap: onTap,
      padding: padding ?? const EdgeInsets.all(DesignSystem.spaceL),
      margin: margin,
      borderRadius: borderRadius,
      gradient: gradient ?? DesignSystem.primaryGradient,
      boxShadow: DesignSystem.shadowMedium,
      width: width,
      height: height,
      child: child,
    );
  }
}

/// Mini Card Widget
/// Small card for budget progress and quick stats
class MiniCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final VoidCallback? onTap;
  final double? width;
  final double? height;

  const MiniCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.onTap,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return ElegantCard(
      onTap: onTap,
      padding: padding ?? const EdgeInsets.all(DesignSystem.spaceM),
      margin: margin ?? const EdgeInsets.symmetric(
        horizontal: DesignSystem.spaceXS,
        vertical: DesignSystem.spaceXS,
      ),
      borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
      backgroundColor: backgroundColor,
      boxShadow: DesignSystem.shadowLow,
      width: width,
      height: height,
      child: child,
    );
  }
}

/// Transaction Card Widget
/// Card specifically designed for transaction list items
class TransactionCard extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool showActions;

  const TransactionCard({
    super.key,
    required this.child,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.showActions = false,
  });

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: UniqueKey(),
      direction: showActions ? DismissDirection.horizontal : DismissDirection.none,
      background: Container(
        decoration: BoxDecoration(
          color: DesignSystem.success.withOpacity(0.1),
          borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
        ),
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.only(left: DesignSystem.spaceL),
        child: Icon(
          Icons.edit_rounded,
          color: DesignSystem.success,
          size: 24,
        ),
      ),
      secondaryBackground: Container(
        decoration: BoxDecoration(
          color: DesignSystem.error.withOpacity(0.1),
          borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
        ),
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: DesignSystem.spaceL),
        child: Icon(
          Icons.delete_rounded,
          color: DesignSystem.error,
          size: 24,
        ),
      ),
      confirmDismiss: (direction) async {
        if (direction == DismissDirection.startToEnd && onEdit != null) {
          onEdit!();
          return false;
        } else if (direction == DismissDirection.endToStart && onDelete != null) {
          // Show confirmation dialog
          return await showDialog<bool>(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Delete Transaction'),
              content: const Text('Are you sure you want to delete this transaction?'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('Delete'),
                ),
              ],
            ),
          ) ?? false;
        }
        return false;
      },
      onDismissed: (direction) {
        if (direction == DismissDirection.endToStart && onDelete != null) {
          onDelete!();
        }
      },
      child: ElegantCard(
        onTap: onTap,
        padding: const EdgeInsets.all(DesignSystem.spaceM),
        margin: const EdgeInsets.symmetric(
          horizontal: DesignSystem.spaceM,
          vertical: DesignSystem.spaceXS,
        ),
        child: child,
      ),
    );
  }
}
