import 'package:flutter/material.dart';
import '../../core/theme/design_system.dart';
import '../../core/theme/typography.dart';

/// Sophisticated FAB Menu with Expansion Animation
/// Beautiful floating menu that expands from FAB with smooth animations
class SophisticatedFabMenu extends StatefulWidget {
  final List<FabMenuItem> items;
  final VoidCallback? onClose;

  const SophisticatedFabMenu({
    super.key,
    required this.items,
    this.onClose,
  });

  @override
  State<SophisticatedFabMenu> createState() => _SophisticatedFabMenuState();
}

class _SophisticatedFabMenuState extends State<SophisticatedFabMenu>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _staggerController;
  late Animation<double> _backgroundAnimation;
  late Animation<double> _scaleAnimation;
  late List<Animation<double>> _itemAnimations;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: DesignSystem.animationMedium,
      vsync: this,
    );
    
    _staggerController = AnimationController(
      duration: Duration(milliseconds: 300 + (widget.items.length * 100)),
      vsync: this,
    );

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: DesignSystem.defaultCurve,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    // Create staggered animations for each item
    _itemAnimations = List.generate(widget.items.length, (index) {
      final start = index * 0.1;
      final end = start + 0.6;
      
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: _staggerController,
        curve: Interval(start, end.clamp(0.0, 1.0), curve: Curves.elasticOut),
      ));
    });

    // Start animations
    _animationController.forward();
    _staggerController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _staggerController.dispose();
    super.dispose();
  }

  void _close() {
    _staggerController.reverse().then((_) {
      _animationController.reverse().then((_) {
        widget.onClose?.call();
        if (mounted) {
          Navigator.of(context).pop();
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Stack(
        children: [
          // Background overlay
          AnimatedBuilder(
            animation: _backgroundAnimation,
            builder: (context, child) {
              return GestureDetector(
                onTap: _close,
                child: Container(
                  color: Colors.black.withOpacity(0.3 * _backgroundAnimation.value),
                ),
              );
            },
          ),

          // Menu items
          AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) {
              return Center(
                child: Transform.scale(
                  scale: _scaleAnimation.value,
                  child: _buildMenuContent(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMenuContent() {
    return Container(
      margin: const EdgeInsets.all(DesignSystem.spaceL),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
          // Title
          Container(
            padding: const EdgeInsets.all(DesignSystem.spaceL),
            decoration: BoxDecoration(
              gradient: DesignSystem.primaryGradient,
              borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
              boxShadow: DesignSystem.shadowMedium,
            ),
            child: Column(
              children: [
                Icon(
                  Icons.add_circle_rounded,
                  color: DesignSystem.textOnPrimary,
                  size: 32,
                ),
                const SizedBox(height: DesignSystem.spaceS),
                Text(
                  'Quick Actions',
                  style: AppTypography.headlineSmall.copyWith(
                    color: DesignSystem.textOnPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'Choose an action to continue',
                  style: AppTypography.bodyMedium.copyWith(
                    color: DesignSystem.textOnPrimary.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: DesignSystem.spaceL),

          // Menu items grid
          Container(
            padding: const EdgeInsets.all(DesignSystem.spaceL),
            decoration: BoxDecoration(
              color: DesignSystem.cardBackground,
              borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
              boxShadow: DesignSystem.shadowMedium,
            ),
            child: Column(
              children: [
                // First row
                if (widget.items.length >= 2)
                  Row(
                    children: [
                      Expanded(child: _buildMenuItem(widget.items[0], 0)),
                      const SizedBox(width: DesignSystem.spaceM),
                      Expanded(child: _buildMenuItem(widget.items[1], 1)),
                    ],
                  ),

                // Second row
                if (widget.items.length >= 4) ...[
                  const SizedBox(height: DesignSystem.spaceM),
                  Row(
                    children: [
                      Expanded(child: _buildMenuItem(widget.items[2], 2)),
                      const SizedBox(width: DesignSystem.spaceM),
                      Expanded(child: _buildMenuItem(widget.items[3], 3)),
                    ],
                  ),
                ],

                // Additional items (single column)
                if (widget.items.length > 4)
                  ...List.generate(
                    widget.items.length - 4,
                        (i) {
                      final index = i + 4;
                      return Container(
                        margin: const EdgeInsets.only(top: DesignSystem.spaceM),
                        child: _buildMenuItem(widget.items[index], index),
                      );
                    },
                  ),
              ],
            ),
          ),

          const SizedBox(height: DesignSystem.spaceL),

          // Close button
          AnimatedBuilder(
            animation: _itemAnimations.last,
            builder: (context, child) {
              return Transform.scale(
                scale: _itemAnimations.last.value,
                child: GestureDetector(
                  onTap: _close,
                  child: Container(
                    width: 56,
                    height: 56,
                    decoration: BoxDecoration(
                      color: DesignSystem.textSecondary.withOpacity(0.1),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: DesignSystem.textSecondary.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      Icons.close_rounded,
                      color: DesignSystem.textSecondary,
                      size: 24,
                    ),
                  ),
                ),
              );
            },
          ),
        ],
        ),
      ),
    );
  }

  Widget _buildMenuItem(FabMenuItem item, int index) {
    if (index >= _itemAnimations.length) return const SizedBox.shrink();

    return AnimatedBuilder(
      animation: _itemAnimations[index],
      builder: (context, child) {
        return Transform.scale(
          scale: _itemAnimations[index].value,
          child: Transform.translate(
            offset: Offset(0, 20 * (1 - _itemAnimations[index].value)),
            child: GestureDetector(
              onTap: () {
                _close();
                item.onTap();
              },
              child: Container(
                padding: const EdgeInsets.all(DesignSystem.spaceL),
                decoration: BoxDecoration(
                  gradient: item.gradient ?? LinearGradient(
                    colors: [
                      item.color.withOpacity(0.1),
                      item.color.withOpacity(0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
                  border: Border.all(
                    color: item.color.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: item.color.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
                      ),
                      child: Icon(
                        item.icon,
                        color: item.color,
                        size: 24,
                      ),
                    ),
                    const SizedBox(height: DesignSystem.spaceS),
                    Text(
                      item.label,
                      style: AppTypography.labelMedium.copyWith(
                        color: DesignSystem.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    if (item.subtitle != null) ...[
                      const SizedBox(height: DesignSystem.spaceXS),
                      Text(
                        item.subtitle!,
                        style: AppTypography.bodySmall.copyWith(
                          color: DesignSystem.textSecondary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// FAB Menu Item data class
class FabMenuItem {
  final IconData icon;
  final String label;
  final String? subtitle;
  final Color color;
  final LinearGradient? gradient;
  final VoidCallback onTap;

  const FabMenuItem({
    required this.icon,
    required this.label,
    required this.color,
    required this.onTap,
    this.subtitle,
    this.gradient,
  });
}

/// Helper function to show FAB menu
void showSophisticatedFabMenu(
  BuildContext context, {
  required List<FabMenuItem> items,
  VoidCallback? onClose,
}) {
  showGeneralDialog(
    context: context,
    barrierDismissible: true,
    barrierLabel: 'Close menu',
    barrierColor: Colors.transparent,
    transitionDuration: DesignSystem.animationMedium,
    pageBuilder: (context, animation, secondaryAnimation) {
      return SophisticatedFabMenu(
        items: items,
        onClose: onClose,
      );
    },
  );
}
