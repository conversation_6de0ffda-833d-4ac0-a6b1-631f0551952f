import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class CustomCalendarTabSlider extends StatefulWidget {
  final Function(DateTime) onDateSelected;
  final Function(DateTimeRange) onDateRangeSelected;
  final DateTime? initialDate;

  const CustomCalendarTabSlider({
    Key? key,
    required this.onDateSelected,
    required this.onDateRangeSelected,
    this.initialDate,
  }) : super(key: key);

  @override
  State<CustomCalendarTabSlider> createState() => _CustomCalendarTabSliderState();
}

class _CustomCalendarTabSliderState extends State<CustomCalendarTabSlider>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late DateTime _currentMonth;
  late PageController _pageController;
  int _currentPageIndex = 0;
  DateTimeRange? _selectedRange;

  @override
  void initState() {
    super.initState();
    _currentMonth = widget.initialDate ?? DateTime.now();
    _tabController = TabController(length: 2, vsync: this);
    _pageController = PageController(initialPage: 1000); // Start in middle for infinite scroll
    _currentPageIndex = 1000;
  }

  @override
  void dispose() {
    _tabController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  int _getDaysInMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0).day;
  }

  DateTime _getMonthFromPageIndex(int pageIndex) {
    final monthsFromCurrent = pageIndex - 1000;
    return DateTime(_currentMonth.year, _currentMonth.month + monthsFromCurrent);
  }

  List<Widget> _buildDayTabs(DateTime month) {
    final daysInMonth = _getDaysInMonth(month);
    final today = DateTime.now();
    
    return List.generate(daysInMonth, (index) {
      final day = index + 1;
      final date = DateTime(month.year, month.month, day);
      final isToday = date.day == today.day && 
                     date.month == today.month && 
                     date.year == today.year;
      final isSelected = date.day == _currentMonth.day &&
                        date.month == _currentMonth.month &&
                        date.year == _currentMonth.year;

      return GestureDetector(
        onTap: () {
          setState(() {
            _currentMonth = date;
          });
          widget.onDateSelected(date);
        },
        child: Container(
          width: 40,
          height: 40,
          margin: const EdgeInsets.symmetric(horizontal: 4),
          decoration: BoxDecoration(
            gradient: isSelected
                ? const LinearGradient(
                    colors: [Color(0xFF5CC9B6), Color(0xFF4D9DE0)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : null,
            color: isToday && !isSelected 
                ? const Color(0xFF5CC9B6).withOpacity(0.2)
                : null,
            borderRadius: BorderRadius.circular(20),
            border: isToday && !isSelected
                ? Border.all(color: const Color(0xFF5CC9B6), width: 1)
                : null,
          ),
          child: Center(
            child: Text(
              '$day',
              style: TextStyle(
                color: isSelected
                    ? Colors.white
                    : isToday
                        ? const Color(0xFF5CC9B6)
                        : Get.theme.textTheme.bodyMedium?.color,
                fontWeight: isSelected || isToday ? FontWeight.w600 : FontWeight.w400,
                fontSize: 14,
              ),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildMonthView(DateTime month) {
    return Column(
      children: [
        // Month header
        Container(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                DateFormat('MMMM yyyy').format(month),
                style: Get.theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Get.theme.textTheme.titleLarge?.color,
                ),
              ),
              Row(
                children: [
                  IconButton(
                    onPressed: () {
                      _pageController.previousPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    },
                    icon: const Icon(Icons.chevron_left),
                    iconSize: 24,
                  ),
                  IconButton(
                    onPressed: () {
                      _pageController.nextPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    },
                    icon: const Icon(Icons.chevron_right),
                    iconSize: 24,
                  ),
                ],
              ),
            ],
          ),
        ),
        // Days slider
        Container(
          height: 60,
          child: ListView(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            children: _buildDayTabs(month),
          ),
        ),
      ],
    );
  }

  Widget _buildCustomRangePicker() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Text(
            'Select Date Range',
            style: Get.theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 20),
          if (_selectedRange != null) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF5CC9B6), Color(0xFF4D9DE0)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'From',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                      Text(
                        DateFormat('MMM dd, yyyy').format(_selectedRange!.start),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  const Icon(
                    Icons.arrow_forward,
                    color: Colors.white,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      const Text(
                        'To',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                      Text(
                        DateFormat('MMM dd, yyyy').format(_selectedRange!.end),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],
          ElevatedButton.icon(
            onPressed: () async {
              final DateTimeRange? picked = await showDateRangePicker(
                context: context,
                firstDate: DateTime(2020),
                lastDate: DateTime.now().add(const Duration(days: 365)),
                initialDateRange: _selectedRange,
                builder: (context, child) {
                  return Theme(
                    data: Get.theme.copyWith(
                      colorScheme: Get.theme.colorScheme.copyWith(
                        primary: const Color(0xFF5CC9B6),
                        onPrimary: Colors.white,
                      ),
                    ),
                    child: child!,
                  );
                },
              );
              
              if (picked != null) {
                setState(() {
                  _selectedRange = picked;
                });
                widget.onDateRangeSelected(picked);
              }
            },
            icon: const Icon(Icons.date_range),
            label: Text(_selectedRange == null ? 'Pick Date Range' : 'Change Range'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF5CC9B6),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Get.theme.cardColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Tab bar
          Container(
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Get.theme.scaffoldBackgroundColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF5CC9B6), Color(0xFF4D9DE0)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              labelColor: Colors.white,
              unselectedLabelColor: Get.theme.textTheme.bodyMedium?.color,
              tabs: const [
                Tab(text: 'Monthly View'),
                Tab(text: 'Custom Range'),
              ],
            ),
          ),
          // Tab content
          SizedBox(
            height: 200,
            child: TabBarView(
              controller: _tabController,
              children: [
                // Monthly view with day slider
                PageView.builder(
                  controller: _pageController,
                  onPageChanged: (index) {
                    setState(() {
                      _currentPageIndex = index;
                    });
                  },
                  itemBuilder: (context, index) {
                    final month = _getMonthFromPageIndex(index);
                    return _buildMonthView(month);
                  },
                ),
                // Custom range picker
                _buildCustomRangePicker(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
