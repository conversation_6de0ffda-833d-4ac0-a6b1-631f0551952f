import 'package:flutter/material.dart';
import '../../core/theme/design_system.dart';
import '../../core/theme/typography.dart';

/// Sophisticated Social Login Button
/// Elegant button for social authentication with smooth animations
class SophisticatedSocialButton extends StatefulWidget {
  final String provider; // 'google', 'apple', 'facebook', etc.
  final String label;
  final VoidCallback onPressed;
  final bool isLoading;
  final bool isEnabled;

  const SophisticatedSocialButton({
    super.key,
    required this.provider,
    required this.label,
    required this.onPressed,
    this.isLoading = false,
    this.isEnabled = true,
  });

  @override
  State<SophisticatedSocialButton> createState() => _SophisticatedSocialButtonState();
}

class _SophisticatedSocialButtonState extends State<SophisticatedSocialButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: DesignSystem.animationFast,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: DesignSystem.defaultCurve,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.isEnabled && !widget.isLoading) {
      setState(() => _isPressed = true);
      _animationController.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (widget.isEnabled && !widget.isLoading) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  void _onTapCancel() {
    if (widget.isEnabled && !widget.isLoading) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDisabled = !widget.isEnabled || widget.isLoading;
    final config = _getSocialConfig(widget.provider);

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _onTapDown,
            onTapUp: _onTapUp,
            onTapCancel: _onTapCancel,
            onTap: isDisabled ? null : widget.onPressed,
            child: AnimatedContainer(
              duration: DesignSystem.animationMedium,
              curve: DesignSystem.defaultCurve,
              height: 56,
              padding: const EdgeInsets.symmetric(
                horizontal: DesignSystem.spaceL,
                vertical: DesignSystem.spaceM,
              ),
              decoration: BoxDecoration(
                color: isDisabled 
                    ? DesignSystem.textSecondary.withOpacity(0.1)
                    : config.backgroundColor,
                borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
                border: Border.all(
                  color: isDisabled
                      ? DesignSystem.textSecondary.withOpacity(0.2)
                      : config.borderColor,
                  width: 1,
                ),
                boxShadow: isDisabled 
                    ? null 
                    : _isPressed 
                        ? DesignSystem.shadowLow
                        : DesignSystem.shadowMedium,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (widget.isLoading) ...[
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          config.iconColor,
                        ),
                      ),
                    ),
                    const SizedBox(width: DesignSystem.spaceM),
                  ] else ...[
                    _buildSocialIcon(config),
                    const SizedBox(width: DesignSystem.spaceM),
                  ],
                  Text(
                    widget.label,
                    style: AppTypography.buttonMedium.copyWith(
                      color: isDisabled 
                          ? DesignSystem.textSecondary
                          : config.textColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSocialIcon(SocialConfig config) {
    if (config.customIcon != null) {
      return config.customIcon!;
    }

    return Icon(
      config.iconData,
      size: 20,
      color: widget.isEnabled ? config.iconColor : DesignSystem.textSecondary,
    );
  }

  SocialConfig _getSocialConfig(String provider) {
    switch (provider.toLowerCase()) {
      case 'google':
        return SocialConfig(
          iconData: Icons.g_mobiledata_rounded,
          backgroundColor: DesignSystem.cardBackground,
          borderColor: DesignSystem.textSecondary.withOpacity(0.2),
          textColor: DesignSystem.textPrimary,
          iconColor: const Color(0xFF4285F4), // Google Blue
          customIcon: _buildGoogleIcon(),
        );
      case 'apple':
        return SocialConfig(
          iconData: Icons.apple_rounded,
          backgroundColor: DesignSystem.textPrimary,
          borderColor: DesignSystem.textPrimary,
          textColor: DesignSystem.textOnPrimary,
          iconColor: DesignSystem.textOnPrimary,
        );
      case 'facebook':
        return SocialConfig(
          iconData: Icons.facebook_rounded,
          backgroundColor: const Color(0xFF1877F2), // Facebook Blue
          borderColor: const Color(0xFF1877F2),
          textColor: DesignSystem.textOnPrimary,
          iconColor: DesignSystem.textOnPrimary,
        );
      case 'twitter':
        return SocialConfig(
          iconData: Icons.alternate_email_rounded,
          backgroundColor: const Color(0xFF1DA1F2), // Twitter Blue
          borderColor: const Color(0xFF1DA1F2),
          textColor: DesignSystem.textOnPrimary,
          iconColor: DesignSystem.textOnPrimary,
        );
      default:
        return SocialConfig(
          iconData: Icons.login_rounded,
          backgroundColor: DesignSystem.cardBackground,
          borderColor: DesignSystem.textSecondary.withOpacity(0.2),
          textColor: DesignSystem.textPrimary,
          iconColor: DesignSystem.textSecondary,
        );
    }
  }

  Widget _buildGoogleIcon() {
    return Container(
      width: 20,
      height: 20,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(2),
        color: Colors.white,
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Google "G" icon simulation
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              gradient: const LinearGradient(
                colors: [
                  Color(0xFF4285F4), // Google Blue
                  Color(0xFF34A853), // Google Green
                  Color(0xFFFBBC05), // Google Yellow
                  Color(0xFFEA4335), // Google Red
                ],
                stops: [0.0, 0.33, 0.66, 1.0],
              ),
            ),
            child: const Center(
              child: Text(
                'G',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class SocialConfig {
  final IconData iconData;
  final Color backgroundColor;
  final Color borderColor;
  final Color textColor;
  final Color iconColor;
  final Widget? customIcon;

  SocialConfig({
    required this.iconData,
    required this.backgroundColor,
    required this.borderColor,
    required this.textColor,
    required this.iconColor,
    this.customIcon,
  });
}

/// Divider with "OR" text for social login sections
class SocialDivider extends StatelessWidget {
  final String text;

  const SocialDivider({
    super.key,
    this.text = 'OR',
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 1,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.transparent,
                  DesignSystem.textSecondary.withOpacity(0.3),
                ],
              ),
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: DesignSystem.spaceM),
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: DesignSystem.spaceM,
              vertical: DesignSystem.spaceS,
            ),
            decoration: BoxDecoration(
              color: DesignSystem.neutralBackground,
              borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
              border: Border.all(
                color: DesignSystem.textSecondary.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Text(
              text,
              style: AppTypography.labelSmall.copyWith(
                color: DesignSystem.textSecondary,
                fontWeight: FontWeight.w600,
                letterSpacing: 1.2,
              ),
            ),
          ),
        ),
        Expanded(
          child: Container(
            height: 1,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  DesignSystem.textSecondary.withOpacity(0.3),
                  Colors.transparent,
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
