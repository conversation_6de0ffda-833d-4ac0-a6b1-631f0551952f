import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../widgets/universal_transaction_dialog.dart';
import '../../core/models/transaction.dart';

class EnhancedFabMenu extends StatefulWidget {
  final VoidCallback? onScanReceipt;
  final VoidCallback? onViewReports;
  final VoidCallback? onManageBudgets;
  final VoidCallback? onViewAnalytics;

  const EnhancedFabMenu({
    Key? key,
    this.onScanReceipt,
    this.onViewReports,
    this.onManageBudgets,
    this.onViewAnalytics,
  }) : super(key: key);

  @override
  State<EnhancedFabMenu> createState() => _EnhancedFabMenuState();
}

class _EnhancedFabMenuState extends State<EnhancedFabMenu>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _rotationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _rotationAnimation;

  bool _isExpanded = false;
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.75,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    _rotationController.dispose();
    _removeOverlay();
    super.dispose();
  }

  void _toggleMenu() {
    HapticFeedback.lightImpact();
    
    if (_isExpanded) {
      _closeMenu();
    } else {
      _openMenu();
    }
  }

  void _openMenu() {
    setState(() {
      _isExpanded = true;
    });
    
    _rotationController.forward();
    _showOverlay();
  }

  void _closeMenu() {
    setState(() {
      _isExpanded = false;
    });
    
    _rotationController.reverse();
    _removeOverlay();
  }

  void _showOverlay() {
    _overlayEntry = OverlayEntry(
      builder: (context) => _buildOverlay(),
    );
    
    Overlay.of(context).insert(_overlayEntry!);
    _animationController.forward();
  }

  void _removeOverlay() {
    _animationController.reverse().then((_) {
      _overlayEntry?.remove();
      _overlayEntry = null;
    });
  }

  Widget _buildOverlay() {
    return GestureDetector(
      onTap: _closeMenu,
      child: Container(
        color: Colors.black.withOpacity(0.3),
        child: Stack(
          children: [
            // Quick action buttons
            Positioned(
              bottom: 140,
              right: 16,
              child: AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _scaleAnimation.value,
                    child: Opacity(
                      opacity: _fadeAnimation.value,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          _buildQuickActionButton(
                            'Add Income',
                            Icons.trending_up,
                            const Color(0xFF5CC9B6),
                            () => _addTransaction(TransactionType.income),
                            delay: 0,
                          ),
                          const SizedBox(height: 12),
                          _buildQuickActionButton(
                            'Add Expense',
                            Icons.trending_down,
                            const Color(0xFFFF7F6F),
                            () => _addTransaction(TransactionType.expense),
                            delay: 50,
                          ),
                          const SizedBox(height: 12),
                          _buildQuickActionButton(
                            'Multiple Transactions',
                            Icons.content_copy,
                            const Color(0xFFFF9800),
                            () => _addMultipleTransactions(),
                            delay: 75,
                          ),
                          const SizedBox(height: 12),
                          _buildQuickActionButton(
                            'Add to Savings',
                            Icons.savings,
                            const Color(0xFF2E7D32),
                            () {
                              _closeMenu();
                              Get.toNamed('/savings');
                            },
                            delay: 100,
                          ),
                          const SizedBox(height: 12),
                          _buildQuickActionButton(
                            'Scan Receipt',
                            Icons.camera_alt,
                            const Color(0xFF4D9DE0),
                            () {
                              _closeMenu();
                              widget.onScanReceipt?.call();
                            },
                            delay: 125,
                          ),
                          const SizedBox(height: 12),
                          _buildQuickActionButton(
                            'View Reports',
                            Icons.analytics,
                            const Color(0xFF9C27B0),
                            () {
                              _closeMenu();
                              widget.onViewReports?.call();
                            },
                            delay: 175,
                          ),
                          const SizedBox(height: 12),
                          _buildQuickActionButton(
                            'Manage Budgets',
                            Icons.account_balance_wallet,
                            const Color(0xFFFF9800),
                            () {
                              _closeMenu();
                              widget.onManageBudgets?.call();
                            },
                            delay: 200,
                          ),
                          const SizedBox(height: 12),
                          _buildQuickActionButton(
                            'Analytics',
                            Icons.insights,
                            const Color(0xFF4CAF50),
                            () {
                              _closeMenu();
                              widget.onViewAnalytics?.call();
                            },
                            delay: 250,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onTap, {
    int delay = 0,
  }) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 300 + delay),
      tween: Tween(begin: 0.0, end: 1.0),
      curve: Curves.elasticOut,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(50 * (1 - value), 0),
          child: Opacity(
            opacity: value,
            child: child,
          ),
        );
      },
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          margin: const EdgeInsets.only(bottom: 4),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Label
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Get.theme.cardColor,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  label,
                  style: Get.theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              // Icon button
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      color,
                      color.withOpacity(0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(28),
                  boxShadow: [
                    BoxShadow(
                      color: color.withOpacity(0.3),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Icon(
                  icon,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _addTransaction(TransactionType type) {
    _closeMenu();
    UniversalTransactionDialog.show(initialType: type);
  }

  void _addMultipleTransactions() {
    _closeMenu();
    UniversalTransactionDialog.showMultiple();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _rotationController,
      builder: (context, child) {
        return Transform.rotate(
          angle: _rotationAnimation.value * 2 * 3.14159,
          child: FloatingActionButton(
            onPressed: _toggleMenu,
            backgroundColor: _isExpanded
                ? const Color(0xFFFF7F6F)
                : const Color(0xFF5CC9B6),
            elevation: _isExpanded ? 12 : 8,
            heroTag: "enhanced_fab_menu",
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: Icon(
                _isExpanded ? Icons.close : Icons.add,
                key: ValueKey(_isExpanded),
                color: Colors.white,
                size: 28,
              ),
            ),
          ),
        );
      },
    );
  }
}
