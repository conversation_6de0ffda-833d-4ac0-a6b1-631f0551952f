import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../core/theme/design_system.dart';
import '../../core/theme/typography.dart';
import 'compact_date_picker.dart';

class PersistentDateFilter extends StatefulWidget {
  final DateTime? selectedDate;
  final DateTimeRange? selectedDateRange;
  final Function(DateTime?) onDateSelected;
  final Function(DateTimeRange?) onDateRangeSelected;
  final bool showQuickFilters;
  final bool allowFutureDates;
  final String? emptyStateMessage;

  const PersistentDateFilter({
    super.key,
    this.selectedDate,
    this.selectedDateRange,
    required this.onDateSelected,
    required this.onDateRangeSelected,
    this.showQuickFilters = true,
    this.allowFutureDates = false,
    this.emptyStateMessage,
  });

  @override
  State<PersistentDateFilter> createState() => _PersistentDateFilterState();
}

class _PersistentDateFilterState extends State<PersistentDateFilter> {
  DateTime _currentMonth = DateTime.now();
  DateTime? _selectedDate;
  DateTimeRange? _selectedDateRange;
  bool _isRangeMode = false;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.selectedDate;
    _selectedDateRange = widget.selectedDateRange;
    if (_selectedDate != null) {
      _currentMonth = DateTime(_selectedDate!.year, _selectedDate!.month);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: DesignSystem.white,
        borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
        boxShadow: DesignSystem.shadowLow,
        border: Border.all(
          color: DesignSystem.textSecondary.withOpacity(0.5),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          _buildHeader(),
          if (widget.showQuickFilters) _buildQuickFilters(),
          _buildCalendar(),
          if (widget.emptyStateMessage != null && _selectedDate != null)
            _buildEmptyStateMessage(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacingMedium),
      decoration: BoxDecoration(
        color: DesignSystem.primaryTeal.withOpacity(0.05),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(DesignSystem.radiusLarge),
          topRight: Radius.circular(DesignSystem.radiusLarge),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Date Filter',
                  style: AppTypography.titleMedium.copyWith(
                    color: DesignSystem.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (_selectedDate != null || _selectedDateRange != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    _getSelectedDateText(),
                    style: AppTypography.bodySmall.copyWith(
                      color: DesignSystem.textSecondary,
                    ),
                  ),
                ],
              ],
            ),
          ),
          Row(
            children: [
              IconButton(
                onPressed: () {
                  setState(() {
                    _isRangeMode = !_isRangeMode;
                    if (!_isRangeMode) {
                      _selectedDateRange = null;
                      widget.onDateRangeSelected(null);
                    } else {
                      _selectedDate = null;
                      widget.onDateSelected(null);
                    }
                  });
                },
                icon: Icon(
                  _isRangeMode ? Icons.event : Icons.date_range,
                  color: DesignSystem.primaryTeal,
                ),
                tooltip: _isRangeMode ? 'Single Date' : 'Date Range',
              ),
              IconButton(
                onPressed: _clearSelection,
                icon: Icon(
                  Icons.clear,
                  color: DesignSystem.textSecondary,
                ),
                tooltip: 'Clear Selection',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickFilters() {
    final quickFilters = [
      {'label': 'Today', 'days': 0},
      {'label': 'Yesterday', 'days': 1},
      {'label': 'Last 7 days', 'days': 7},
      {'label': 'Last 30 days', 'days': 30},
      {'label': 'This Month', 'days': -1},
    ];

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: DesignSystem.spacingMedium,
        vertical: DesignSystem.spacingSmall,
      ),
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: quickFilters.map((filter) {
          final isSelected = _isQuickFilterSelected(filter);
          
          return GestureDetector(
            onTap: () => _selectQuickFilter(filter),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: isSelected 
                    ? DesignSystem.primaryTeal
                    : DesignSystem.backgroundSecondary,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: isSelected 
                      ? DesignSystem.primaryTeal
                      : DesignSystem.textSecondary.withOpacity(0.3),
                ),
              ),
              child: Text(
                filter['label'] as String,
                style: AppTypography.bodySmall.copyWith(
                  color: isSelected 
                      ? DesignSystem.white
                      : DesignSystem.textPrimary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildCalendar() {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacingMedium),
      child: Column(
        children: [
          _buildMonthNavigation(),
          const SizedBox(height: 16),
          _buildCalendarGrid(),
        ],
      ),
    );
  }

  Widget _buildMonthNavigation() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
          onPressed: () {
            setState(() {
              _currentMonth = DateTime(_currentMonth.year, _currentMonth.month - 1);
            });
          },
          icon: Icon(
            Icons.chevron_left,
            color: DesignSystem.textPrimary,
          ),
        ),
        Text(
          DateFormat('MMMM yyyy').format(_currentMonth),
          style: AppTypography.titleMedium.copyWith(
            color: DesignSystem.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        IconButton(
          onPressed: () {
            final nextMonth = DateTime(_currentMonth.year, _currentMonth.month + 1);
            if (widget.allowFutureDates || nextMonth.isBefore(DateTime.now().add(const Duration(days: 1)))) {
              setState(() {
                _currentMonth = nextMonth;
              });
            }
          },
          icon: Icon(
            Icons.chevron_right,
            color: DesignSystem.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildCalendarGrid() {
    final daysInMonth = DateTime(_currentMonth.year, _currentMonth.month + 1, 0).day;
    final firstDayOfMonth = DateTime(_currentMonth.year, _currentMonth.month, 1);
    final firstWeekday = firstDayOfMonth.weekday;
    
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: 1,
      ),
      itemCount: daysInMonth + firstWeekday - 1,
      itemBuilder: (context, index) {
        if (index < firstWeekday - 1) {
          return const SizedBox(); // Empty cells for days before the first day of month
        }
        
        final day = index - firstWeekday + 2;
        final date = DateTime(_currentMonth.year, _currentMonth.month, day);
        final isSelected = _selectedDate != null && 
            _selectedDate!.year == date.year &&
            _selectedDate!.month == date.month &&
            _selectedDate!.day == date.day;
        final isInRange = _selectedDateRange != null &&
            date.isAfter(_selectedDateRange!.start.subtract(const Duration(days: 1))) &&
            date.isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
        final isToday = _isToday(date);
        final isFuture = date.isAfter(DateTime.now()) && !widget.allowFutureDates;
        
        return GestureDetector(
          onTap: isFuture ? null : () => _selectDate(date),
          child: Container(
            margin: const EdgeInsets.all(2),
            decoration: BoxDecoration(
              color: isSelected 
                  ? DesignSystem.primaryTeal
                  : isInRange
                      ? DesignSystem.primaryTeal.withOpacity(0.2)
                      : isToday
                          ? DesignSystem.primaryTeal.withOpacity(0.1)
                          : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
              border: isToday && !isSelected
                  ? Border.all(color: DesignSystem.primaryTeal, width: 1)
                  : null,
            ),
            child: Center(
              child: Text(
                day.toString(),
                style: AppTypography.bodyMedium.copyWith(
                  color: isFuture
                      ? DesignSystem.textSecondary.withOpacity(0.5)
                      : isSelected
                          ? DesignSystem.white
                          : DesignSystem.textPrimary,
                  fontWeight: isSelected || isToday ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyStateMessage() {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacingMedium),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: DesignSystem.textSecondary,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              widget.emptyStateMessage!,
              style: AppTypography.bodySmall.copyWith(
                color: DesignSystem.textSecondary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getSelectedDateText() {
    if (_selectedDateRange != null) {
      return '${DateFormat('MMM d').format(_selectedDateRange!.start)} - ${DateFormat('MMM d, y').format(_selectedDateRange!.end)}';
    } else if (_selectedDate != null) {
      return DateFormat('EEEE, MMM d, y').format(_selectedDate!);
    }
    return '';
  }

  bool _isToday(DateTime date) {
    final today = DateTime.now();
    return date.year == today.year &&
        date.month == today.month &&
        date.day == today.day;
  }

  bool _isQuickFilterSelected(Map<String, dynamic> filter) {
    final days = filter['days'] as int;
    final now = DateTime.now();
    
    if (days == 0) {
      return _selectedDate != null && _isToday(_selectedDate!);
    } else if (days == 1) {
      final yesterday = now.subtract(const Duration(days: 1));
      return _selectedDate != null &&
          _selectedDate!.year == yesterday.year &&
          _selectedDate!.month == yesterday.month &&
          _selectedDate!.day == yesterday.day;
    } else if (days == -1) {
      // This month
      return _selectedDateRange != null &&
          _selectedDateRange!.start.year == now.year &&
          _selectedDateRange!.start.month == now.month &&
          _selectedDateRange!.start.day == 1;
    } else {
      // Last N days
      final startDate = now.subtract(Duration(days: days - 1));
      return _selectedDateRange != null &&
          _selectedDateRange!.start.year == startDate.year &&
          _selectedDateRange!.start.month == startDate.month &&
          _selectedDateRange!.start.day == startDate.day;
    }
  }

  void _selectQuickFilter(Map<String, dynamic> filter) {
    final days = filter['days'] as int;
    final now = DateTime.now();
    
    setState(() {
      if (days == 0) {
        _selectedDate = now;
        _selectedDateRange = null;
        _isRangeMode = false;
        widget.onDateSelected(_selectedDate);
        widget.onDateRangeSelected(null);
      } else if (days == 1) {
        _selectedDate = now.subtract(const Duration(days: 1));
        _selectedDateRange = null;
        _isRangeMode = false;
        widget.onDateSelected(_selectedDate);
        widget.onDateRangeSelected(null);
      } else if (days == -1) {
        // This month
        _selectedDateRange = DateTimeRange(
          start: DateTime(now.year, now.month, 1),
          end: DateTime(now.year, now.month + 1, 0),
        );
        _selectedDate = null;
        _isRangeMode = true;
        widget.onDateRangeSelected(_selectedDateRange);
        widget.onDateSelected(null);
      } else {
        // Last N days
        _selectedDateRange = DateTimeRange(
          start: now.subtract(Duration(days: days - 1)),
          end: now,
        );
        _selectedDate = null;
        _isRangeMode = true;
        widget.onDateRangeSelected(_selectedDateRange);
        widget.onDateSelected(null);
      }
    });
  }

  void _selectDate(DateTime date) {
    setState(() {
      if (_isRangeMode) {
        if (_selectedDateRange == null) {
          _selectedDateRange = DateTimeRange(start: date, end: date);
        } else {
          if (date.isBefore(_selectedDateRange!.start)) {
            _selectedDateRange = DateTimeRange(start: date, end: _selectedDateRange!.end);
          } else {
            _selectedDateRange = DateTimeRange(start: _selectedDateRange!.start, end: date);
          }
        }
        widget.onDateRangeSelected(_selectedDateRange);
      } else {
        _selectedDate = date;
        widget.onDateSelected(_selectedDate);
      }
    });
  }

  void _clearSelection() {
    setState(() {
      _selectedDate = null;
      _selectedDateRange = null;
    });
    widget.onDateSelected(null);
    widget.onDateRangeSelected(null);
  }
}
