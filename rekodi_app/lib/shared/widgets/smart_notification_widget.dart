import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../core/database/database.dart';
import '../../core/models/transaction.dart';
import '../../core/services/personal_transaction_service.dart';
import '../../core/services/sms_parser_service.dart';
import '../widgets/universal_transaction_dialog.dart';

class SmartNotificationWidget extends StatefulWidget {
  const SmartNotificationWidget({Key? key}) : super(key: key);

  @override
  State<SmartNotificationWidget> createState() => _SmartNotificationWidgetState();
}

class _SmartNotificationWidgetState extends State<SmartNotificationWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  final PersonalTransactionService _transactionService = Get.find();
  final SmsParserService _smsParserService = Get.find();

  List<FrequentTransaction> _frequentTransactions = [];
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadFrequentTransactions();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: -1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _loadFrequentTransactions() {
    final transactions = _transactionService.transactions;
    final frequencyMap = <String, FrequentTransaction>{};

    // Count frequency of transaction titles/categories
    for (final transaction in transactions) {
      final key = transaction.title.toLowerCase();
      if (frequencyMap.containsKey(key)) {
        frequencyMap[key] = frequencyMap[key]!.copyWith(
          count: frequencyMap[key]!.count + 1,
          totalAmount: frequencyMap[key]!.totalAmount + transaction.amount,
        );
      } else {
        // Convert string types to enums for FrequentTransaction
        final typeEnum = TransactionType.values.firstWhere(
          (type) => type.name == transaction.type,
          orElse: () => TransactionType.expense,
        );
        final categoryEnum = TransactionCategory.values.firstWhere(
          (category) => category.name == transaction.category,
          orElse: () => TransactionCategory.other,
        );

        frequencyMap[key] = FrequentTransaction(
          title: transaction.title,
          category: categoryEnum,
          type: typeEnum,
          count: 1,
          totalAmount: transaction.amount,
          averageAmount: transaction.amount,
        );
      }
    }

    // Calculate average amounts and sort by frequency
    final frequentList = frequencyMap.values.map((ft) {
      return ft.copyWith(averageAmount: ft.totalAmount / ft.count);
    }).toList();

    frequentList.sort((a, b) => b.count.compareTo(a.count));

    setState(() {
      _frequentTransactions = frequentList.take(6).toList();
    });
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
    HapticFeedback.lightImpact();
  }

  void _quickAddTransaction(FrequentTransaction frequentTransaction) {
    _showQuickAmountDialog(frequentTransaction);
  }

  void _showQuickAmountDialog(FrequentTransaction frequentTransaction) {
    final amountController = TextEditingController(
      text: frequentTransaction.averageAmount.toStringAsFixed(0),
    );
    final notesController = TextEditingController();

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: frequentTransaction.type == TransactionType.income
                            ? [const Color(0xFF5CC9B6), const Color(0xFF4D9DE0)]
                            : [const Color(0xFFFF7F6F), const Color(0xFFFF9A8B)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getCategoryIcon(frequentTransaction.category),
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      frequentTransaction.title,
                      style: Get.theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              TextFormField(
                controller: amountController,
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
                style: Get.theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: frequentTransaction.type == TransactionType.income
                      ? const Color(0xFF5CC9B6)
                      : const Color(0xFFFF7F6F),
                ),
                decoration: InputDecoration(
                  labelText: 'Amount',
                  prefixText: 'KSh ',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Get.theme.scaffoldBackgroundColor,
                ),
                autofocus: true,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: notesController,
                decoration: InputDecoration(
                  labelText: 'Notes (Optional)',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Get.theme.scaffoldBackgroundColor,
                ),
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Get.back(),
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => _saveQuickTransaction(
                        frequentTransaction,
                        amountController.text,
                        notesController.text,
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: frequentTransaction.type == TransactionType.income
                            ? const Color(0xFF5CC9B6)
                            : const Color(0xFFFF7F6F),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('Save'),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              TextButton.icon(
                onPressed: () {
                  Get.back();
                  _showSmsParseDialog(frequentTransaction);
                },
                icon: const Icon(Icons.message),
                label: const Text('Parse from SMS'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSmsParseDialog(FrequentTransaction frequentTransaction) {
    UniversalTransactionDialog.show(
      initialType: frequentTransaction.type,
      smsMessage: '',
    );
  }

  void _saveQuickTransaction(
    FrequentTransaction frequentTransaction,
    String amountText,
    String notes,
  ) async {
    final amount = double.tryParse(amountText);
    if (amount == null || amount <= 0) {
      Get.snackbar(
        'Error',
        'Please enter a valid amount',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: const Color(0xFFFF7F6F),
        colorText: Colors.white,
      );
      return;
    }

    await _transactionService.addTransaction(
      title: frequentTransaction.title,
      description: notes.isEmpty ? null : notes,
      amount: amount,
      type: frequentTransaction.type,
      category: frequentTransaction.category,
      date: DateTime.now(),
    );
    Get.back();

    Get.snackbar(
      'Success',
      'Transaction added successfully',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: const Color(0xFF5CC9B6),
      colorText: Colors.white,
    );

    _loadFrequentTransactions(); // Refresh the list
  }

  IconData _getCategoryIcon(TransactionCategory category) {
    switch (category) {
      case TransactionCategory.food:
        return Icons.restaurant;
      case TransactionCategory.transportation:
        return Icons.directions_car;
      case TransactionCategory.shopping:
        return Icons.shopping_bag;
      case TransactionCategory.entertainment:
        return Icons.movie;
      case TransactionCategory.healthcare:
        return Icons.local_hospital;
      case TransactionCategory.education:
        return Icons.school;
      case TransactionCategory.utilities:
        return Icons.receipt_long;
      case TransactionCategory.housing:
        return Icons.home;
      case TransactionCategory.travel:
        return Icons.flight;
      case TransactionCategory.insurance:
        return Icons.security;
      case TransactionCategory.debt:
        return Icons.credit_card;
      case TransactionCategory.savings:
        return Icons.savings;
      case TransactionCategory.charity:
        return Icons.volunteer_activism;
      case TransactionCategory.business_expense:
        return Icons.business_center;
      case TransactionCategory.other_expense:
        return Icons.category;
      default:
        return Icons.category;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_frequentTransactions.isEmpty) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value * 100),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF5CC9B6), Color(0xFF4D9DE0)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF5CC9B6).withOpacity(0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Header
                  GestureDetector(
                    onTap: _toggleExpanded,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.flash_on,
                            color: Colors.white,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          const Expanded(
                            child: Text(
                              'Quick Add',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          Icon(
                            _isExpanded ? Icons.expand_less : Icons.expand_more,
                            color: Colors.white,
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Content
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    height: _isExpanded ? null : 0,
                    child: _isExpanded
                        ? Container(
                            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                            child: GridView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 3,
                                childAspectRatio: 1.0,
                                crossAxisSpacing: 8,
                                mainAxisSpacing: 8,
                              ),
                              itemCount: _frequentTransactions.length,
                              itemBuilder: (context, index) {
                                final transaction = _frequentTransactions[index];
                                return GestureDetector(
                                  onTap: () => _quickAddTransaction(transaction),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.white.withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: Colors.white.withOpacity(0.3),
                                      ),
                                    ),
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          _getCategoryIcon(transaction.category),
                                          color: Colors.white,
                                          size: 24,
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          transaction.title,
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 10,
                                            fontWeight: FontWeight.w500,
                                          ),
                                          textAlign: TextAlign.center,
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        Text(
                                          '${transaction.count}x',
                                          style: TextStyle(
                                            color: Colors.white.withOpacity(0.8),
                                            fontSize: 8,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                          )
                        : null,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class FrequentTransaction {
  final String title;
  final TransactionCategory category;
  final TransactionType type;
  final int count;
  final double totalAmount;
  final double averageAmount;

  const FrequentTransaction({
    required this.title,
    required this.category,
    required this.type,
    required this.count,
    required this.totalAmount,
    required this.averageAmount,
  });

  FrequentTransaction copyWith({
    String? title,
    TransactionCategory? category,
    TransactionType? type,
    int? count,
    double? totalAmount,
    double? averageAmount,
  }) {
    return FrequentTransaction(
      title: title ?? this.title,
      category: category ?? this.category,
      type: type ?? this.type,
      count: count ?? this.count,
      totalAmount: totalAmount ?? this.totalAmount,
      averageAmount: averageAmount ?? this.averageAmount,
    );
  }
}
