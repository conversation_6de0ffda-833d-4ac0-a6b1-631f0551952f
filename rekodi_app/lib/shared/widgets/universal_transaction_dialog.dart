import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../core/database/database.dart';
import '../../core/models/transaction.dart';
import '../../core/services/personal_transaction_service.dart';
import '../../core/services/sms_parser_service.dart';
import '../../core/services/transaction_charges_service.dart';
import '../../core/services/currency_service.dart';
import '../../core/models/transaction_charge.dart';
import '../../core/theme/design_system.dart';

class UniversalTransactionDialog extends StatefulWidget {
  final PersonalTransaction? editTransaction;
  final TransactionType? initialType;
  final String? smsMessage;

  const UniversalTransactionDialog({
    Key? key,
    this.editTransaction,
    this.initialType,
    this.smsMessage,
  }) : super(key: key);

  static Future<void> show({
    PersonalTransaction? editTransaction,
    TransactionType? initialType,
    String? smsMessage,
  }) {
    return Get.dialog(
      UniversalTransactionDialog(
        editTransaction: editTransaction,
        initialType: initialType,
        smsMessage: smsMessage,
      ),
      barrierDismissible: true,
    );
  }

  static Future<void> showEdit(PersonalTransaction transaction) {
    return show(editTransaction: transaction);
  }

  static Future<void> showAdd({
    TransactionType? initialType,
    String? smsMessage,
  }) {
    return show(
      initialType: initialType,
      smsMessage: smsMessage,
    );
  }

  static Future<void> showMultiple({
    TransactionType? initialType,
    PersonalTransaction? templateTransaction,
  }) {
    return Get.dialog(
      _MultipleTransactionDialog(
        initialType: initialType,
        templateTransaction: templateTransaction,
      ),
      barrierDismissible: false,
    );
  }

  @override
  State<UniversalTransactionDialog> createState() => _UniversalTransactionDialogState();
}

class _UniversalTransactionDialogState extends State<UniversalTransactionDialog>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;

  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _titleController = TextEditingController();
  final _notesController = TextEditingController();
  final _smsController = TextEditingController();

  TransactionType _selectedType = TransactionType.expense;
  TransactionCategory _selectedCategory = TransactionCategory.food;
  DateTime _selectedDate = DateTime.now();
  TimeOfDay _selectedTime = TimeOfDay.now();
  bool _includeTime = false;
  bool _isLoading = false;
  bool _showSmsParser = false;
  bool _includeCharges = false;
  double _estimatedCharges = 0.0;
  final List<String> _paymentMethods = [
    'Cash',
    'M-Pesa',
    'Bank Transfer',
    'Card',
    'Digital Wallet',
    'Crypto',
  ];
  String _selectedPaymentMethod = 'Cash';

  final PersonalTransactionService _transactionService = Get.find();
  final SmsParserService _smsParserService = Get.find();
  final TransactionChargesService _chargesService = Get.find();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeData();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  void _initializeData() {
    if (widget.editTransaction != null) {
      final transaction = widget.editTransaction!;
      _amountController.text = transaction.amount.toString();
      _titleController.text = transaction.title;
      _notesController.text = transaction.description ?? '';

      // Convert string types to enums
      _selectedType = TransactionType.values.firstWhere(
        (type) => type.name == transaction.type,
        orElse: () => TransactionType.expense,
      );
      _selectedCategory = TransactionCategory.values.firstWhere(
        (category) => category.name == transaction.category,
        orElse: () => TransactionCategory.other,
      );
      _selectedDate = transaction.date;
      _selectedTime = TimeOfDay.fromDateTime(transaction.date);
      _includeTime = true;
    } else {
      _selectedType = widget.initialType ?? TransactionType.expense;
    }

    if (widget.smsMessage != null) {
      _smsController.text = widget.smsMessage!;
      _showSmsParser = true;
      _parseSmsMessage();
    }
  }

  void _parseSmsMessage() async {
    if (_smsController.text.isNotEmpty) {
      try {
        // Try to detect sender from message content
        String detectedSender = _detectSenderFromMessage(_smsController.text);

        final parsedData = _smsParserService.parseSmsMessage(
          _smsController.text,
          detectedSender,
          DateTime.now(),
        );

        if (parsedData != null) {
          setState(() {
            _amountController.text = parsedData.amount?.toString() ?? '';
            _titleController.text = parsedData.description ?? parsedData.merchantName ?? '';

            // Convert string types to enums
            _selectedType = TransactionType.values.firstWhere(
              (type) => type.name == parsedData.transactionType,
              orElse: () => TransactionType.expense,
            );
            _selectedCategory = TransactionCategory.values.firstWhere(
              (category) => category.name == parsedData.category,
              orElse: () => TransactionCategory.other,
            );

            // Set payment method if available
            final paymentMethod = parsedData.parsedData['paymentMethod'] as String?;
            if (paymentMethod != null) {
              // You could set a payment method field here if you have one
            }
          });

          Get.snackbar(
            'SMS Parsed Successfully',
            'Transaction details extracted from ${parsedData.sender}',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: const Color(0xFF5CC9B6),
            colorText: Colors.white,
          );
        } else {
          Get.snackbar(
            'Parse Error',
            'Could not parse SMS message. Please check the format.',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: const Color(0xFFFF7F6F),
            colorText: Colors.white,
          );
        }
      } catch (e) {
        Get.snackbar(
          'Parse Error',
          'Could not parse SMS message automatically: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: const Color(0xFFFF7F6F),
          colorText: Colors.white,
        );
      }
    }
  }

  String _detectSenderFromMessage(String message) {
    final messageUpper = message.toUpperCase();

    // Check for known senders in the message
    final knownSenders = _smsParserService.supportedPaymentMethods;
    for (final sender in ['MPESA', 'KCB-BANK', 'KCB', 'NCBA', 'LOOP', 'EQUITY', 'COOP-BANK', 'ABSA', 'STANBIC']) {
      if (messageUpper.contains(sender)) {
        return sender;
      }
    }

    return 'UNKNOWN';
  }

  Widget _buildSenderChip(String sender, String type) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Get.theme.primaryColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        '$sender ($type)',
        style: Get.theme.textTheme.bodySmall?.copyWith(
          color: Get.theme.primaryColor,
          fontSize: 10,
        ),
      ),
    );
  }

  void _calculateEstimatedCharges() {
    if (_amountController.text.isNotEmpty) {
      final amount = double.tryParse(_amountController.text) ?? 0.0;
      setState(() {
        _estimatedCharges = _chargesService.calculateTransactionCharges(amount, _selectedPaymentMethod);
      });
    }
  }

  Widget _buildPaymentMethodAndCharges() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Payment Method Selector
        Text(
          'Payment Method',
          style: Get.theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
            border: Border.all(color: Get.theme.dividerColor),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedPaymentMethod,
              isExpanded: true,
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    _selectedPaymentMethod = newValue;
                    _calculateEstimatedCharges();
                  });
                }
              },
              items: _paymentMethods.map<DropdownMenuItem<String>>((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
            ),
          ),
        ),
        const SizedBox(height: 12),

        // Include Charges Toggle
        Row(
          children: [
            Checkbox(
              value: _includeCharges,
              onChanged: (bool? value) {
                setState(() {
                  _includeCharges = value ?? false;
                  if (_includeCharges) {
                    _calculateEstimatedCharges();
                  }
                });
              },
              activeColor: const Color(0xFF5CC9B6),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Include Transaction Charges',
                style: Get.theme.textTheme.bodyMedium,
              ),
            ),
          ],
        ),

        // Estimated Charges Display
        if (_includeCharges && _estimatedCharges > 0) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFFFF7F6F).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: const Color(0xFFFF7F6F).withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.receipt_long_rounded,
                  color: const Color(0xFFFF7F6F),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Estimated Charges: KES ${_estimatedCharges.toStringAsFixed(2)}',
                    style: Get.theme.textTheme.bodyMedium?.copyWith(
                      color: const Color(0xFFFF7F6F),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: _selectedType == TransactionType.income
                  ? const Color(0xFF5CC9B6)
                  : const Color(0xFFFF7F6F),
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _selectTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: _selectedType == TransactionType.income
                  ? const Color(0xFF5CC9B6)
                  : const Color(0xFFFF7F6F),
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _selectedTime = picked;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _amountController.dispose();
    _titleController.dispose();
    _notesController.dispose();
    _smsController.dispose();
    super.dispose();
  }

  Widget _buildDateTimePicker() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date & Time',
          style: Get.theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),

        // Date picker
        InkWell(
          onTap: _selectDate,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Get.theme.scaffoldBackgroundColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Get.theme.dividerColor,
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  color: Get.theme.textTheme.bodyMedium?.color,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    DateFormat('EEEE, MMMM dd, yyyy').format(_selectedDate),
                    style: Get.theme.textTheme.bodyMedium,
                  ),
                ),
                Icon(
                  Icons.arrow_drop_down,
                  color: Get.theme.textTheme.bodyMedium?.color,
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 12),

        // Time toggle and picker
        Row(
          children: [
            Checkbox(
              value: _includeTime,
              onChanged: (value) {
                setState(() {
                  _includeTime = value ?? false;
                });
              },
              activeColor: _selectedType == TransactionType.income
                  ? const Color(0xFF5CC9B6)
                  : const Color(0xFFFF7F6F),
            ),
            Text(
              'Include specific time',
              style: Get.theme.textTheme.bodyMedium,
            ),
          ],
        ),

        if (_includeTime) ...[
          const SizedBox(height: 8),
          InkWell(
            onTap: _selectTime,
            borderRadius: BorderRadius.circular(12),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Get.theme.scaffoldBackgroundColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Get.theme.dividerColor,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.access_time,
                    color: Get.theme.textTheme.bodyMedium?.color,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _selectedTime.format(context),
                      style: Get.theme.textTheme.bodyMedium,
                    ),
                  ),
                  Icon(
                    Icons.arrow_drop_down,
                    color: Get.theme.textTheme.bodyMedium?.color,
                  ),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTypeSelector() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      decoration: BoxDecoration(
        color: Get.theme.scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedType = TransactionType.income),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  gradient: _selectedType == TransactionType.income
                      ? const LinearGradient(
                          colors: [Color(0xFF5CC9B6), Color(0xFF4D9DE0)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        )
                      : null,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.trending_up,
                      color: _selectedType == TransactionType.income
                          ? Colors.white
                          : Get.theme.textTheme.bodyMedium?.color,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Income',
                      style: TextStyle(
                        color: _selectedType == TransactionType.income
                            ? Colors.white
                            : Get.theme.textTheme.bodyMedium?.color,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedType = TransactionType.expense),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  gradient: _selectedType == TransactionType.expense
                      ? const LinearGradient(
                          colors: [Color(0xFFFF7F6F), Color(0xFFFF9A8B)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        )
                      : null,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.trending_down,
                      color: _selectedType == TransactionType.expense
                          ? Colors.white
                          : Get.theme.textTheme.bodyMedium?.color,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Expense',
                      style: TextStyle(
                        color: _selectedType == TransactionType.expense
                            ? Colors.white
                            : Get.theme.textTheme.bodyMedium?.color,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountField() {
    return TextFormField(
      controller: _amountController,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
      ],
      style: Get.theme.textTheme.headlineMedium?.copyWith(
        fontWeight: FontWeight.w600,
        color: _selectedType == TransactionType.income
            ? const Color(0xFF5CC9B6)
            : const Color(0xFFFF7F6F),
      ),
      decoration: InputDecoration(
        labelText: 'Amount',
        prefixText: '${CurrencyService.to.currentCurrency.value.symbol} ',
        prefixStyle: Get.theme.textTheme.headlineMedium?.copyWith(
          fontWeight: FontWeight.w600,
          color: Get.theme.textTheme.bodyMedium?.color,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        filled: true,
        fillColor: Get.theme.scaffoldBackgroundColor,
        contentPadding: const EdgeInsets.all(16),
      ),
      onChanged: (value) {
        if (_includeCharges) {
          _calculateEstimatedCharges();
        }
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter an amount';
        }
        if (double.tryParse(value) == null) {
          return 'Please enter a valid amount';
        }
        return null;
      },
    );
  }

  Widget _buildSmsParser() {
    if (!_showSmsParser) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.message,
              color: Get.theme.textTheme.bodyMedium?.color,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'SMS Message Parser',
              style: Get.theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            IconButton(
              onPressed: () => setState(() => _showSmsParser = false),
              icon: const Icon(Icons.close, size: 20),
            ),
          ],
        ),
        const SizedBox(height: 8),

        // Supported senders info
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Get.theme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Get.theme.primaryColor.withOpacity(0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Supported Senders:',
                style: Get.theme.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Get.theme.primaryColor,
                ),
              ),
              const SizedBox(height: 4),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: [
                  _buildSenderChip('MPESA', 'Mobile Money'),
                  _buildSenderChip('KCB-BANK', 'Bank'),
                  _buildSenderChip('NCBA', 'Bank'),
                  _buildSenderChip('LOOP', 'Bank'),
                  _buildSenderChip('EQUITY', 'Bank'),
                  _buildSenderChip('COOP-BANK', 'Bank'),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        TextFormField(
          controller: _smsController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'Paste your SMS message here...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            filled: true,
            fillColor: Get.theme.scaffoldBackgroundColor,
            contentPadding: const EdgeInsets.all(16),
          ),
          onChanged: (value) => _parseSmsMessage(),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  void _saveTransaction() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final amount = double.parse(_amountController.text);

      // Combine date and time if time is included
      final DateTime finalDateTime = _includeTime
          ? DateTime(
              _selectedDate.year,
              _selectedDate.month,
              _selectedDate.day,
              _selectedTime.hour,
              _selectedTime.minute,
            )
          : _selectedDate;

      if (widget.editTransaction != null) {
        // Update existing transaction
        await _transactionService.updateTransaction(
          transactionId: widget.editTransaction!.id,
          title: _titleController.text,
          description: _notesController.text.isEmpty ? null : _notesController.text,
          amount: amount,
          type: _selectedType,
          category: _selectedCategory,
          date: finalDateTime,
          paymentMethod: _selectedPaymentMethod,
          transactionCost: _includeCharges ? _estimatedCharges : null,
        );
        Get.back();
        Get.snackbar(
          'Success',
          'Transaction updated successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: const Color(0xFF5CC9B6),
          colorText: Colors.white,
        );
      } else {
        // Create new transaction
        final success = await _transactionService.addTransaction(
          title: _titleController.text,
          description: _notesController.text.isEmpty ? null : _notesController.text,
          amount: amount,
          type: _selectedType,
          category: _selectedCategory,
          date: finalDateTime,
          paymentMethod: _selectedPaymentMethod,
        );

        if (success && _includeCharges && _estimatedCharges > 0) {
          // Add transaction charges
          await _chargesService.addChargesToTransaction(
            'txn_${DateTime.now().millisecondsSinceEpoch}', // Transaction ID would be returned from service
            amount,
            _selectedPaymentMethod,
          );
        }

        Get.back();
        Get.snackbar(
          'Success',
          _includeCharges && _estimatedCharges > 0
              ? 'Transaction and charges added successfully'
              : 'Transaction added successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: const Color(0xFF5CC9B6),
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to save transaction: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: const Color(0xFFFF7F6F),
        colorText: Colors.white,
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: SlideTransition(
            position: _slideAnimation,
            child: Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                constraints: const BoxConstraints(maxWidth: 400),
                decoration: BoxDecoration(
                  color: Get.theme.cardColor,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header
                        Row(
                          children: [
                            Text(
                              widget.editTransaction != null 
                                  ? 'Edit Transaction' 
                                  : 'Add Transaction',
                              style: Get.theme.textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const Spacer(),
                            if (!_showSmsParser)
                              IconButton(
                                onPressed: () => setState(() => _showSmsParser = true),
                                icon: const Icon(Icons.message),
                                tooltip: 'Parse SMS',
                              ),
                            IconButton(
                              onPressed: () => Get.back(),
                              icon: const Icon(Icons.close),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        
                        // SMS Parser
                        _buildSmsParser(),
                        
                        // Type selector
                        _buildTypeSelector(),
                        
                        // Amount field
                        _buildAmountField(),
                        const SizedBox(height: 16),
                        
                        // Title field
                        TextFormField(
                          controller: _titleController,
                          decoration: InputDecoration(
                            labelText: 'Title',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide.none,
                            ),
                            filled: true,
                            fillColor: Get.theme.scaffoldBackgroundColor,
                            contentPadding: const EdgeInsets.all(16),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a title';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),

                        // Payment Method and Charges
                        _buildPaymentMethodAndCharges(),
                        const SizedBox(height: 16),

                        // Notes field
                        TextFormField(
                          controller: _notesController,
                          maxLines: 2,
                          decoration: InputDecoration(
                            labelText: 'Notes (Optional)',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide.none,
                            ),
                            filled: true,
                            fillColor: Get.theme.scaffoldBackgroundColor,
                            contentPadding: const EdgeInsets.all(16),
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Date and Time picker
                        _buildDateTimePicker(),
                        const SizedBox(height: 24),

                        // Action buttons
                        Row(
                          children: [
                            Expanded(
                              child: OutlinedButton(
                                onPressed: () => Get.back(),
                                style: OutlinedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: const Text('Cancel'),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: ElevatedButton(
                                onPressed: _isLoading ? null : _saveTransaction,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: _selectedType == TransactionType.income
                                      ? const Color(0xFF5CC9B6)
                                      : const Color(0xFFFF7F6F),
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: _isLoading
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                        ),
                                      )
                                    : Text(widget.editTransaction != null ? 'Update' : 'Save'),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

// Multiple Transaction Dialog for adding similar transactions
class _MultipleTransactionDialog extends StatefulWidget {
  final TransactionType? initialType;
  final PersonalTransaction? templateTransaction;

  const _MultipleTransactionDialog({
    this.initialType,
    this.templateTransaction,
  });

  @override
  State<_MultipleTransactionDialog> createState() => _MultipleTransactionDialogState();
}

class _MultipleTransactionDialogState extends State<_MultipleTransactionDialog> {
  final PersonalTransactionService _transactionService = Get.find();
  final List<_TransactionEntry> _transactions = [];

  // Template data
  String _templateTitle = '';
  TransactionType _templateType = TransactionType.expense;
  TransactionCategory _templateCategory = TransactionCategory.other;
  String _templateNotes = '';

  @override
  void initState() {
    super.initState();
    _initializeTemplate();
    _addNewTransaction();
  }

  void _initializeTemplate() {
    if (widget.templateTransaction != null) {
      final template = widget.templateTransaction!;
      _templateTitle = template.title;

      // Convert string types to enums
      _templateType = TransactionType.values.firstWhere(
        (type) => type.name == template.type,
        orElse: () => TransactionType.expense,
      );
      _templateCategory = TransactionCategory.values.firstWhere(
        (category) => category.name == template.category,
        orElse: () => TransactionCategory.other,
      );
      _templateNotes = template.description ?? '';
    } else {
      _templateType = widget.initialType ?? TransactionType.expense;
    }
  }

  void _addNewTransaction() {
    final amountController = TextEditingController();

    // Pre-fill amount if we have a template transaction
    if (widget.templateTransaction != null) {
      amountController.text = widget.templateTransaction!.amount.toString();
    }

    setState(() {
      _transactions.add(_TransactionEntry(
        amountController: amountController,
        dateTime: DateTime.now(),
        title: _templateTitle,
        type: _templateType,
        category: _templateCategory,
        notes: _templateNotes,
      ));
    });
  }

  void _removeTransaction(int index) {
    if (_transactions.length > 1) {
      setState(() {
        _transactions[index].amountController.dispose();
        _transactions.removeAt(index);
      });
    }
  }

  @override
  void dispose() {
    for (final transaction in _transactions) {
      transaction.amountController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        constraints: const BoxConstraints(maxHeight: 600, maxWidth: 500),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.content_copy,
                  color: DesignSystem.primaryColor,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    widget.templateTransaction != null
                        ? 'Duplicate Transaction Multiple Times'
                        : 'Add Multiple Transactions',
                    style: Get.theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Get.back(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 20),
            // Template Info
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: DesignSystem.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: DesignSystem.primaryColor.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.templateTransaction != null
                        ? 'Duplicating: ${_templateTitle.isEmpty ? 'New Transaction' : _templateTitle}'
                        : 'Template: ${_templateTitle.isEmpty ? 'New Transaction' : _templateTitle}',
                    style: Get.theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: DesignSystem.primaryColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${_templateType.name.toUpperCase()} • ${_templateCategory.name.toUpperCase()}',
                    style: Get.theme.textTheme.bodySmall?.copyWith(
                      color: DesignSystem.primaryColor.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            // Transactions List
            Expanded(
              child: ListView.builder(
                itemCount: _transactions.length,
                itemBuilder: (context, index) {
                  return _buildTransactionEntry(index);
                },
              ),
            ),
            const SizedBox(height: 16),
            // Add More Button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _addNewTransaction,
                icon: const Icon(Icons.add),
                label: const Text('Add Another'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: DesignSystem.primaryColor,
                  side: BorderSide(color: DesignSystem.primaryColor),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            // Actions
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Get.back(),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _saveAllTransactions,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: DesignSystem.primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text('Save All (${_transactions.length})'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionEntry(int index) {
    final transaction = _transactions[index];

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                'Transaction ${index + 1}',
                style: Get.theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              if (_transactions.length > 1)
                IconButton(
                  onPressed: () => _removeTransaction(index),
                  icon: const Icon(Icons.delete, color: Colors.red, size: 20),
                  constraints: const BoxConstraints(),
                  padding: EdgeInsets.zero,
                ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              // Amount
              Expanded(
                flex: 2,
                child: TextFormField(
                  controller: transaction.amountController,
                  decoration: InputDecoration(
                    labelText: 'Amount',
                    prefixText: '${CurrencyService.to.currentCurrency.value.symbol} ',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  keyboardType: TextInputType.number,
                  style: Get.theme.textTheme.bodyMedium,
                ),
              ),
              const SizedBox(width: 12),
              // Date/Time
              Expanded(
                flex: 3,
                child: GestureDetector(
                  onTap: () => _selectDateTime(index),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[400]!),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.schedule, size: 16, color: Colors.grey[600]),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            DateFormat('MMM dd, HH:mm').format(transaction.dateTime),
                            style: Get.theme.textTheme.bodyMedium,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _selectDateTime(int index) async {
    final transaction = _transactions[index];

    final date = await showDatePicker(
      context: context,
      initialDate: transaction.dateTime,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(transaction.dateTime),
      );

      if (time != null) {
        setState(() {
          _transactions[index] = transaction.copyWith(
            dateTime: DateTime(
              date.year,
              date.month,
              date.day,
              time.hour,
              time.minute,
            ),
          );
        });
      }
    }
  }

  Future<void> _saveAllTransactions() async {
    final validTransactions = <_TransactionEntry>[];

    for (final transaction in _transactions) {
      if (transaction.amountController.text.isNotEmpty) {
        final amount = double.tryParse(transaction.amountController.text);
        if (amount != null && amount > 0) {
          validTransactions.add(transaction);
        }
      }
    }

    if (validTransactions.isEmpty) {
      Get.snackbar(
        'Error',
        'Please enter valid amounts for at least one transaction',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: DesignSystem.errorColor,
        colorText: Colors.white,
      );
      return;
    }

    try {
      for (final transaction in validTransactions) {
        final amount = double.parse(transaction.amountController.text);

        // Convert enum types to their string names for the service call
        final typeString = TransactionType.values.firstWhere(
          (type) => type.name == transaction.type,
          orElse: () => TransactionType.expense,
        );
        final categoryString = TransactionCategory.values.firstWhere(
          (category) => category.name == transaction.category,
          orElse: () => TransactionCategory.other,
        );

        await _transactionService.addTransaction(
          title: transaction.title.isEmpty ? 'Transaction' : transaction.title,
          description: transaction.notes.isEmpty ? null : transaction.notes,
          amount: amount,
          type: typeString,
          category: categoryString,
          date: transaction.dateTime,
        );
      }

      Get.back();
      Get.snackbar(
        'Success',
        '${validTransactions.length} transactions added successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: DesignSystem.successColor,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to save transactions',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: DesignSystem.errorColor,
        colorText: Colors.white,
      );
    }
  }
}

class _TransactionEntry {
  final TextEditingController amountController;
  final DateTime dateTime;
  final String title;
  final TransactionType type;
  final TransactionCategory category;
  final String notes;

  _TransactionEntry({
    required this.amountController,
    required this.dateTime,
    required this.title,
    required this.type,
    required this.category,
    required this.notes,
  });

  _TransactionEntry copyWith({
    TextEditingController? amountController,
    DateTime? dateTime,
    String? title,
    TransactionType? type,
    TransactionCategory? category,
    String? notes,
  }) {
    return _TransactionEntry(
      amountController: amountController ?? this.amountController,
      dateTime: dateTime ?? this.dateTime,
      title: title ?? this.title,
      type: type ?? this.type,
      category: category ?? this.category,
      notes: notes ?? this.notes,
    );
  }
}
