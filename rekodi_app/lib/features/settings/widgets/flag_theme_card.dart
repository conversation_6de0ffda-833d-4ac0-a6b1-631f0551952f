import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/models/country_flag.dart';
import '../../../core/services/theme_service.dart';

class FlagThemeCard extends StatelessWidget {
  final CountryFlag flag;
  final bool isSelected;
  final VoidCallback onTap;

  const FlagThemeCard({
    super.key,
    required this.flag,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isSelected 
                ? flag.primary.withOpacity(0.1)
                : ThemeService.to.surfaceColor,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isSelected 
                  ? flag.primary
                  : ThemeService.to.borderColor,
              width: isSelected ? 2 : 1,
            ),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: flag.primary.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ]
                : [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Flag Emoji
              Text(
                flag.emoji,
                style: const TextStyle(fontSize: 32),
              ),
              
              const SizedBox(height: 12),
              
              // Country Name
              Text(
                flag.name,
                style: Get.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: isSelected 
                      ? flag.primary
                      : ThemeService.to.textPrimaryColor,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: 12),
              
              // Color Preview
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildColorDot(flag.primary),
                  const SizedBox(width: 4),
                  _buildColorDot(flag.secondary),
                  const SizedBox(width: 4),
                  _buildColorDot(flag.accent),
                ],
              ),
              
              // Selection Indicator
              if (isSelected) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: flag.primary,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.check_rounded,
                        size: 16,
                        color: ThemeService.to.getContrastingTextColor(flag.primary),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Selected',
                        style: Get.textTheme.labelSmall?.copyWith(
                          color: ThemeService.to.getContrastingTextColor(flag.primary),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildColorDot(Color color) {
    return Container(
      width: 12,
      height: 12,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
    );
  }
}
