import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/services/currency_service.dart';
import '../../../core/models/currency.dart';

class CurrencyThemeSettingsCard extends StatefulWidget {
  const CurrencyThemeSettingsCard({Key? key}) : super(key: key);

  @override
  State<CurrencyThemeSettingsCard> createState() => _CurrencyThemeSettingsCardState();
}

class _CurrencyThemeSettingsCardState extends State<CurrencyThemeSettingsCard> {
  final CurrencyService _currencyService = Get.find();
  bool _useCountryBasedCurrency = false;
  String _selectedCountry = 'Kenya';

  final Map<String, Currency> _countryToCurrency = {
    'Kenya': Currency(code: 'KES', name: 'Kenyan Shilling', symbol: 'KSh', flag: '🇰🇪'),
    'Uganda': Currency(code: 'UGX', name: 'Ugandan <PERSON>', symbol: 'USh', flag: '🇺🇬'),
    'Tanzania': Currency(code: 'TZS', name: 'Tanzanian <PERSON>', symbol: 'TSh', flag: '🇹🇿'),
    'Nigeria': Currency(code: 'NGN', name: 'Nigerian Naira', symbol: '₦', flag: '🇳🇬'),
    'Ghana': Currency(code: 'GHS', name: 'Ghanaian Cedi', symbol: '₵', flag: '🇬🇭'),
    'South Africa': Currency(code: 'ZAR', name: 'South African Rand', symbol: 'R', flag: '🇿🇦'),
    'United States': Currency(code: 'USD', name: 'US Dollar', symbol: '\$', flag: '🇺🇸'),
    'United Kingdom': Currency(code: 'GBP', name: 'British Pound', symbol: '£', flag: '🇬🇧'),
    'European Union': Currency(code: 'EUR', name: 'Euro', symbol: '€', flag: '🇪🇺'),
    'Japan': Currency(code: 'JPY', name: 'Japanese Yen', symbol: '¥', flag: '🇯🇵'),
    'China': Currency(code: 'CNY', name: 'Chinese Yuan', symbol: '¥', flag: '🇨🇳'),
    'India': Currency(code: 'INR', name: 'Indian Rupee', symbol: '₹', flag: '🇮🇳'),
  };

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  void _loadSettings() {
    // Load saved settings
    _useCountryBasedCurrency = Get.find<CurrencyService>().useCountryBasedCurrency.value;
    _selectedCountry = Get.find<CurrencyService>().selectedCountry.value;
  }

  Widget _buildThemeSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Get.theme.scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Theme Settings',
            style: Get.theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildThemeOption(
                  'Light',
                  Icons.light_mode,
                  ThemeMode.light,
                  Get.isDarkMode == false,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildThemeOption(
                  'Dark',
                  Icons.dark_mode,
                  ThemeMode.dark,
                  Get.isDarkMode == true,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildThemeOption(
                  'System',
                  Icons.settings_system_daydream,
                  ThemeMode.system,
                  false, // System theme detection
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildThemeOption(String label, IconData icon, ThemeMode mode, bool isSelected) {
    return GestureDetector(
      onTap: () {
        Get.changeThemeMode(mode);
        setState(() {});
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          gradient: isSelected
              ? const LinearGradient(
                  colors: [Color(0xFF5CC9B6), Color(0xFF4D9DE0)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : null,
          color: isSelected ? null : Get.theme.cardColor,
          borderRadius: BorderRadius.circular(8),
          border: isSelected
              ? null
              : Border.all(
                  color: Get.theme.dividerColor,
                  width: 1,
                ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : Get.theme.iconTheme.color,
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : Get.theme.textTheme.bodyMedium?.color,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrencySelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Get.theme.scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Currency Settings',
            style: Get.theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          
          // Country-based currency toggle
          Row(
            children: [
              Expanded(
                child: Text(
                  'Use country-based currency',
                  style: Get.theme.textTheme.bodyMedium,
                ),
              ),
              Switch(
                value: _useCountryBasedCurrency,
                onChanged: (value) {
                  setState(() {
                    _useCountryBasedCurrency = value;
                  });
                  _currencyService.setUseCountryBasedCurrency(value);
                  if (value) {
                    _currencyService.setSelectedCountry(_selectedCountry);
                  }
                },
                activeColor: const Color(0xFF5CC9B6),
              ),
            ],
          ),
          
          if (_useCountryBasedCurrency) ...[
            const SizedBox(height: 16),
            Text(
              'Select Country',
              style: Get.theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                color: Get.theme.cardColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Get.theme.dividerColor),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: _selectedCountry,
                  isExpanded: true,
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  items: _countryToCurrency.keys.map((country) {
                    final currency = _countryToCurrency[country]!;
                    return DropdownMenuItem<String>(
                      value: country,
                      child: Row(
                        children: [
                          Text(
                            currency.flag,
                            style: const TextStyle(fontSize: 20),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              country,
                              style: Get.theme.textTheme.bodyMedium,
                            ),
                          ),
                          Text(
                            '${currency.symbol} ${currency.code}',
                            style: Get.theme.textTheme.bodySmall?.copyWith(
                              color: Get.theme.textTheme.bodySmall?.color?.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (country) {
                    if (country != null) {
                      setState(() {
                        _selectedCountry = country;
                      });
                      _currencyService.setSelectedCountry(country);
                      final currency = _countryToCurrency[country]!;
                      _currencyService.setCurrentCurrency(currency);
                    }
                  },
                ),
              ),
            ),
          ] else ...[
            const SizedBox(height: 16),
            Text(
              'Manual Currency Selection',
              style: Get.theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                color: Get.theme.cardColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Get.theme.dividerColor),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<Currency>(
                  value: _currencyService.currentCurrency.value,
                  isExpanded: true,
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  items: _currencyService.availableCurrencies.map((currency) {
                    return DropdownMenuItem<Currency>(
                      value: currency,
                      child: Row(
                        children: [
                          Text(
                            currency.flag,
                            style: const TextStyle(fontSize: 20),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              currency.name,
                              style: Get.theme.textTheme.bodyMedium,
                            ),
                          ),
                          Text(
                            '${currency.symbol} ${currency.code}',
                            style: Get.theme.textTheme.bodySmall?.copyWith(
                              color: Get.theme.textTheme.bodySmall?.color?.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (currency) {
                    if (currency != null) {
                      _currencyService.setCurrentCurrency(currency);
                    }
                  },
                ),
              ),
            ),
          ],
          
          const SizedBox(height: 16),
          
          // Current currency display
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF5CC9B6), Color(0xFF4D9DE0)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Text(
                  _currencyService.currentCurrency.value.flag,
                  style: const TextStyle(fontSize: 24),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Current Currency',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                      Text(
                        '${_currencyService.currentCurrency.value.name} (${_currencyService.currentCurrency.value.code})',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  _currencyService.currentCurrency.value.symbol,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Get.theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF5CC9B6), Color(0xFF4D9DE0)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.palette,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Theme & Currency',
                  style: Get.theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: _buildThemeSelector(),
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: _buildCurrencySelector(),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}
