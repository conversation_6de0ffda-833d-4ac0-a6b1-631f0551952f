import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/services/theme_service.dart';
import '../../../core/services/account_service.dart';
import '../../../core/services/auth_service.dart';
import '../../../core/services/subscription_service.dart';
import '../../../core/models/account_type.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/database/database.dart';
import 'multi_account_manager.dart';

class AccountDrawer extends StatelessWidget {
  const AccountDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final themeService = ThemeService.to;
      final accountService = AccountService.to;
      final subscriptionService = SubscriptionService.to;
      final currentAccount = accountService.currentAccount;

      return Drawer(
        backgroundColor: themeService.surfaceColor,
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              // Drawer Header with Account Info
              _buildDrawerHeader(currentAccount, themeService, subscriptionService),
              
              const SizedBox(height: 16),
              
              // Account Switching Section
              _buildQuickAccountSwitcher(accountService, themeService),
              
              const Divider(),
              
              // Menu Items
              Expanded(
                child: ListView(
                  padding: EdgeInsets.zero,
                  children: [
                    _buildMenuItem(
                      icon: Icons.dashboard_rounded,
                      title: 'Dashboard',
                      onTap: () {
                        Get.back();
                        Get.offAllNamed(AppRoutes.dashboard);
                      },
                      themeService: themeService,
                    ),
                    _buildMenuItem(
                      icon: Icons.receipt_long_rounded,
                      title: 'Transactions',
                      onTap: () {
                        Get.back();
                        // Navigate to transactions
                      },
                      themeService: themeService,
                    ),
                    _buildMenuItem(
                      icon: Icons.analytics_rounded,
                      title: 'Analytics',
                      onTap: () {
                        Get.back();
                        // Navigate to analytics
                      },
                      themeService: themeService,
                    ),
                    _buildMenuItem(
                      icon: Icons.palette_rounded,
                      title: 'Theme Settings',
                      onTap: () {
                        Get.back();
                        Get.toNamed(AppRoutes.themeSettings);
                      },
                      themeService: themeService,
                    ),
                    _buildMenuItem(
                      icon: Icons.star_rounded,
                      title: 'Upgrade to Premium',
                      subtitle: subscriptionService.isPremium ? 'Premium Active' : 'Unlock all features',
                      onTap: () {
                        Get.back();
                        if (!subscriptionService.isPremium) {
                          subscriptionService.showUpgradeDialog();
                        }
                      },
                      themeService: themeService,
                      isHighlighted: !subscriptionService.isPremium,
                    ),
                    _buildMenuItem(
                      icon: Icons.sync_rounded,
                      title: 'Sync Data',
                      subtitle: 'Last synced: Never',
                      onTap: () {
                        Get.back();
                        _showSyncDialog(themeService);
                      },
                      themeService: themeService,
                    ),
                    _buildMenuItem(
                      icon: Icons.download_rounded,
                      title: 'Export Data',
                      onTap: () {
                        Get.back();
                        if (subscriptionService.isFeatureAvailable(PremiumFeature.dataExport)) {
                          _showExportDialog(themeService);
                        } else {
                          subscriptionService.showFeatureLockedDialog(PremiumFeature.dataExport);
                        }
                      },
                      themeService: themeService,
                    ),
                    _buildMenuItem(
                      icon: Icons.help_outline_rounded,
                      title: 'Help & Support',
                      onTap: () {
                        Get.back();
                        // Navigate to help
                      },
                      themeService: themeService,
                    ),
                    _buildMenuItem(
                      icon: Icons.info_outline_rounded,
                      title: 'About',
                      onTap: () {
                        Get.back();
                        _showAboutDialog();
                      },
                      themeService: themeService,
                    ),
                  ],
                ),
              ),
              
              const Divider(),
              
              // Sign Out
              _buildMenuItem(
                icon: Icons.logout_rounded,
                title: 'Sign Out',
                onTap: () => _showSignOutDialog(themeService),
                themeService: themeService,
                isDestructive: true,
              ),
              
              const SizedBox(height: 16),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildDrawerHeader(UserAccount? account, ThemeService themeService, SubscriptionService subscriptionService) {
    if (account == null) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            themeService.primaryColor,
            themeService.primaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: Colors.white.withOpacity(0.2),
                backgroundImage: account.profileImageUrl != null
                    ? NetworkImage(account.profileImageUrl!)
                    : null,
                child: account.profileImageUrl == null
                    ? Icon(
                        account.accountType == 'business' 
                            ? Icons.business_rounded 
                            : Icons.person_rounded,
                        color: Colors.white,
                        size: 30,
                      )
                    : null,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      account.name,
                      style: Get.textTheme.titleLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      account.email,
                      style: Get.textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            AccountType.fromString(account.accountType).displayName,
                            style: Get.textTheme.labelSmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: subscriptionService.isPremium 
                                ? Colors.amber.withOpacity(0.3)
                                : Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            subscriptionService.premiumStatusText,
                            style: Get.textTheme.labelSmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAccountSwitchingSection(AccountService accountService, ThemeService themeService) {
    final allAccounts = accountService.allAccounts;
    final businessAccounts = allAccounts.where((acc) => acc.accountType == 'business').toList();
    final personalAccounts = allAccounts.where((acc) => acc.accountType == 'personal').toList();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Switch Account',
                style: Get.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: themeService.textPrimaryColor,
                ),
              ),
              PopupMenuButton<String>(
                icon: Icon(
                  Icons.swap_horiz_rounded,
                  color: themeService.primaryColor,
                ),
                tooltip: 'Switch Account',
                onSelected: (accountId) {
                  accountService.switchAccount(accountId);
                },
                itemBuilder: (context) => [
                  if (personalAccounts.isNotEmpty) ...[
                    PopupMenuItem<String>(
                      enabled: false,
                      child: Text(
                        'Personal Accounts',
                        style: Get.textTheme.labelMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: themeService.textSecondaryColor,
                        ),
                      ),
                    ),
                    ...personalAccounts.map((account) => PopupMenuItem<String>(
                      value: account.id,
                      child: Row(
                        children: [
                          Icon(
                            Icons.person_rounded,
                            size: 20,
                            color: account.isActive ? themeService.primaryColor : themeService.textSecondaryColor,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              account.name,
                              style: TextStyle(
                                color: account.isActive ? themeService.primaryColor : themeService.textPrimaryColor,
                                fontWeight: account.isActive ? FontWeight.w600 : FontWeight.normal,
                              ),
                            ),
                          ),
                          if (account.isActive)
                            Icon(
                              Icons.check_circle,
                              size: 16,
                              color: themeService.primaryColor,
                            ),
                        ],
                      ),
                    )),
                  ],
                  if (businessAccounts.isNotEmpty) ...[
                    const PopupMenuDivider(),
                    PopupMenuItem<String>(
                      enabled: false,
                      child: Text(
                        'Business Accounts',
                        style: Get.textTheme.labelMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: themeService.textSecondaryColor,
                        ),
                      ),
                    ),
                    ...businessAccounts.map((account) => PopupMenuItem<String>(
                      value: account.id,
                      child: Row(
                        children: [
                          Icon(
                            Icons.business_rounded,
                            size: 20,
                            color: account.isActive ? themeService.secondaryColor : themeService.textSecondaryColor,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              account.name,
                              style: TextStyle(
                                color: account.isActive ? themeService.secondaryColor : themeService.textPrimaryColor,
                                fontWeight: account.isActive ? FontWeight.w600 : FontWeight.normal,
                              ),
                            ),
                          ),
                          if (account.isActive)
                            Icon(
                              Icons.check_circle,
                              size: 16,
                              color: themeService.secondaryColor,
                            ),
                        ],
                      ),
                    )),
                  ],
                  const PopupMenuDivider(),
                  PopupMenuItem<String>(
                    value: 'add_business',
                    child: Row(
                      children: [
                        Icon(
                          Icons.add_business_rounded,
                          size: 20,
                          color: themeService.accentColor,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Add Business Account',
                          style: TextStyle(
                            color: themeService.accentColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          if (businessAccounts.isEmpty)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: themeService.accentColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: themeService.accentColor.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'No Business Accounts',
                    style: Get.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: themeService.accentColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Create a business account to manage inventory, invoices, and business finances.',
                    style: Get.textTheme.bodySmall?.copyWith(
                      color: themeService.textSecondaryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () => _showCreateBusinessAccountDialog(themeService),
                      icon: const Icon(Icons.add_business_rounded, size: 16),
                      label: const Text('Create Business Account'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: themeService.accentColor,
                        foregroundColor: themeService.getContrastingTextColor(themeService.accentColor),
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
    required ThemeService themeService,
    bool isHighlighted = false,
    bool isDestructive = false,
  }) {
    final color = isDestructive 
        ? themeService.errorColor
        : isHighlighted 
            ? themeService.accentColor
            : themeService.textPrimaryColor;

    return ListTile(
      leading: Icon(
        icon,
        color: color,
        size: 24,
      ),
      title: Text(
        title,
        style: Get.textTheme.bodyLarge?.copyWith(
          color: color,
          fontWeight: isHighlighted ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle,
              style: Get.textTheme.bodySmall?.copyWith(
                color: themeService.textSecondaryColor,
              ),
            )
          : null,
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }

  void _showCreateBusinessAccountDialog(ThemeService themeService) {
    if (!SubscriptionService.to.canCreateBusinessAccount()) {
      SubscriptionService.to.showFeatureLockedDialog(PremiumFeature.multipleBusinessAccounts);
      return;
    }

    Get.dialog(
      AlertDialog(
        title: const Text('Create Business Account'),
        content: const Text('Would you like to create a new business account to manage your business finances?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              // Navigate to business account creation
              Get.toNamed('/create-business-account');
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  void _showSyncDialog(ThemeService themeService) {
    Get.dialog(
      AlertDialog(
        title: const Text('Sync Data'),
        content: const Text('Would you like to sync your data with the cloud?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back();
              Get.snackbar(
                'Syncing',
                'Data sync in progress...',
                backgroundColor: themeService.infoColor,
                colorText: Colors.white,
                duration: const Duration(seconds: 2),
              );
              // TODO: Implement actual sync logic
            },
            child: const Text('Sync'),
          ),
        ],
      ),
    );
  }

  void _showExportDialog(ThemeService themeService) {
    Get.dialog(
      AlertDialog(
        title: const Text('Export Data'),
        content: const Text('Choose export format for your financial data:'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Get.snackbar(
                'Exporting',
                'Exporting data as CSV...',
                backgroundColor: themeService.successColor,
                colorText: Colors.white,
                duration: const Duration(seconds: 2),
              );
              // TODO: Implement CSV export
            },
            child: const Text('CSV'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              Get.snackbar(
                'Exporting',
                'Exporting data as PDF...',
                backgroundColor: themeService.successColor,
                colorText: Colors.white,
                duration: const Duration(seconds: 2),
              );
              // TODO: Implement PDF export
            },
            child: const Text('PDF'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('About Rekodi'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Rekodi - Personal & Business Finance Manager'),
            SizedBox(height: 8),
            Text('Version: 1.0.0'),
            SizedBox(height: 8),
            Text('A comprehensive financial management app for tracking income, expenses, and business operations.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showSignOutDialog(ThemeService themeService) {
    Get.dialog(
      AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out? Your local data will remain on this device.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back();
              Get.back(); // Close drawer
              // Implement sign out logic
              await AuthService.to.signOut();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: themeService.errorColor,
              foregroundColor: themeService.getContrastingTextColor(themeService.errorColor),
            ),
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAccountSwitcher(AccountService accountService, ThemeService themeService) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Accounts',
                style: Get.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: themeService.textPrimaryColor,
                ),
              ),
              Row(
                children: [
                  if (accountService.hasMultipleAccounts)
                    Text(
                      '${accountService.allAccounts.length} accounts',
                      style: Get.textTheme.bodySmall?.copyWith(
                        color: themeService.textSecondaryColor,
                      ),
                    ),
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: _showAccountManager,
                    icon: Icon(
                      Icons.manage_accounts_rounded,
                      color: themeService.primaryColor,
                    ),
                    tooltip: 'Manage Accounts',
                    style: IconButton.styleFrom(
                      backgroundColor: themeService.primaryColor.withOpacity(0.1),
                      padding: const EdgeInsets.all(8),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Quick account list (show max 3 accounts)
          ...accountService.allAccounts.take(3).map((account) {
            final isActive = account.isActive;
            final accountType = AccountType.fromString(account.accountType);

            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: isActive ? null : () => accountService.switchAccount(account.id),
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: isActive
                          ? themeService.primaryColor.withOpacity(0.1)
                          : themeService.surfaceColor,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isActive
                            ? themeService.primaryColor.withOpacity(0.3)
                            : themeService.borderColor,
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: isActive
                                  ? [themeService.primaryColor, themeService.secondaryColor]
                                  : [Colors.grey[400]!, Colors.grey[500]!],
                            ),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Icon(
                            accountType == AccountType.business
                                ? Icons.business_rounded
                                : Icons.person_rounded,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                account.name,
                                style: Get.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: isActive
                                      ? themeService.primaryColor
                                      : themeService.textPrimaryColor,
                                ),
                              ),
                              Text(
                                accountType.displayName,
                                style: Get.textTheme.bodySmall?.copyWith(
                                  color: themeService.textSecondaryColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (isActive)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: themeService.primaryColor,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              'Active',
                              style: Get.textTheme.labelSmall?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }).toList(),

          // Show more accounts button if there are more than 3
          if (accountService.allAccounts.length > 3)
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(top: 8),
              child: TextButton.icon(
                onPressed: _showAccountManager,
                icon: Icon(
                  Icons.expand_more_rounded,
                  color: themeService.primaryColor,
                ),
                label: Text(
                  'View All Accounts (${accountService.allAccounts.length})',
                  style: TextStyle(
                    color: themeService.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: TextButton.styleFrom(
                  backgroundColor: themeService.primaryColor.withOpacity(0.1),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _showAccountManager() {
    Get.back(); // Close drawer first

    showModalBottomSheet(
      context: Get.context!,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        margin: const EdgeInsets.all(16),
        child: const MultiAccountManager(),
      ),
    );
  }
}
