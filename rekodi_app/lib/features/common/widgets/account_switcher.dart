import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/models/account_type.dart';
import '../../../core/services/account_service.dart';
import '../../../core/database/database.dart';
import 'multi_account_manager.dart';

class AccountSwitcher extends StatelessWidget {
  final bool showAccountName;
  final bool showDropdownIcon;
  final Color? textColor;
  final double? fontSize;

  const AccountSwitcher({
    super.key,
    this.showAccountName = true,
    this.showDropdownIcon = true,
    this.textColor,
    this.fontSize,
  });

  @override
  Widget build(BuildContext context) {
    final AccountService accountService = Get.find();

    return Obx(() {
      final currentAccount = accountService.currentAccount;
      
      if (currentAccount == null) {
        return _buildNoAccountWidget();
      }

      return Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _showAccountManager,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Account Avatar
                _buildAccountAvatar(currentAccount),
                
                if (showAccountName) ...[
                  const SizedBox(width: 8),
                  // Account Info
                  Flexible(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          currentAccount.name,
                          style: TextStyle(
                            fontSize: fontSize ?? 14,
                            fontWeight: FontWeight.w600,
                            color: textColor ?? Colors.white,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          AccountType.fromString(currentAccount.accountType).displayName,
                          style: TextStyle(
                            fontSize: (fontSize ?? 14) - 2,
                            color: (textColor ?? Colors.white).withOpacity(0.8),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                
                if (showDropdownIcon) ...[
                  const SizedBox(width: 4),
                  Icon(
                    Icons.keyboard_arrow_down_rounded,
                    color: (textColor ?? Colors.white).withOpacity(0.8),
                    size: 20,
                  ),
                ],
              ],
            ),
          ),
        ),
      );
    });
  }

  Widget _buildAccountAvatar(UserAccount account) {
    final accountType = AccountType.fromString(account.accountType);
    
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withOpacity(0.3),
            Colors.white.withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: account.profileImageUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                account.profileImageUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) =>
                    _buildAccountIcon(accountType),
              ),
            )
          : _buildAccountIcon(accountType),
    );
  }

  Widget _buildAccountIcon(AccountType accountType) {
    return Icon(
      accountType == AccountType.business
          ? Icons.business_rounded
          : Icons.person_rounded,
      color: Colors.white,
      size: 18,
    );
  }

  Widget _buildNoAccountWidget() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: _showAccountManager,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.red.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.red.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.warning_rounded,
                color: Colors.red[300],
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'No Account',
                style: TextStyle(
                  fontSize: fontSize ?? 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.red[300],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAccountManager() {
    HapticFeedback.lightImpact();
    
    showModalBottomSheet(
      context: Get.context!,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        margin: const EdgeInsets.all(16),
        child: MultiAccountManager(
          onAccountSelected: (account) {
            // Account switching is handled in MultiAccountManager
          },
          onCreateAccount: () {
            // Account creation is handled in MultiAccountManager
          },
        ),
      ),
    );
  }
}

class CompactAccountSwitcher extends StatelessWidget {
  final Color? iconColor;
  final double? size;

  const CompactAccountSwitcher({
    super.key,
    this.iconColor,
    this.size,
  });

  @override
  Widget build(BuildContext context) {
    final AccountService accountService = Get.find();

    return Obx(() {
      final currentAccount = accountService.currentAccount;
      
      return Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _showAccountManager,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.all(8),
            child: Stack(
              children: [
                _buildAccountAvatar(currentAccount),
                if (accountService.allAccounts.length > 1)
                  Positioned(
                    right: -2,
                    bottom: -2,
                    child: Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Colors.white,
                          width: 2,
                        ),
                      ),
                      child: const Icon(
                        Icons.swap_horiz_rounded,
                        color: Colors.white,
                        size: 8,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      );
    });
  }

  Widget _buildAccountAvatar(UserAccount? account) {
    if (account == null) {
      return Container(
        width: size ?? 40,
        height: size ?? 40,
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          Icons.person_rounded,
          color: iconColor ?? Colors.grey[600],
          size: (size ?? 40) * 0.6,
        ),
      );
    }

    final accountType = AccountType.fromString(account.accountType);
    
    return Container(
      width: size ?? 40,
      height: size ?? 40,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.secondary],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: account.profileImageUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                account.profileImageUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) =>
                    _buildAccountIcon(accountType),
              ),
            )
          : _buildAccountIcon(accountType),
    );
  }

  Widget _buildAccountIcon(AccountType accountType) {
    return Icon(
      accountType == AccountType.business
          ? Icons.business_rounded
          : Icons.person_rounded,
      color: Colors.white,
      size: (size ?? 40) * 0.5,
    );
  }

  void _showAccountManager() {
    HapticFeedback.lightImpact();
    
    showModalBottomSheet(
      context: Get.context!,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(Get.context!).size.height * 0.8,
        ),
        margin: const EdgeInsets.all(16),
        child: MultiAccountManager(
          onAccountSelected: (account) {
            // Account switching is handled in MultiAccountManager
          },
          onCreateAccount: () {
            // Account creation is handled in MultiAccountManager
          },
        ),
      ),
    );
  }
}
