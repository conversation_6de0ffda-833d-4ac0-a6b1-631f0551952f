import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/services/theme_service.dart';
import '../../../core/services/account_service.dart';
import '../../../core/models/account_type.dart';
import '../../../core/database/database.dart';
import 'account_switcher.dart';

class CommonAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final bool showAccountInfo;
  final VoidCallback? onDrawerTap;

  const CommonAppBar({
    super.key,
    required this.title,
    this.actions,
    this.showAccountInfo = true,
    this.onDrawerTap,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final themeService = ThemeService.to;
      final accountService = AccountService.to;
      final currentAccount = accountService.currentAccount;

      return AppBar(
        backgroundColor: themeService.surfaceColor,
        foregroundColor: themeService.textPrimaryColor,
        elevation: 0,
        centerTitle: false,
        leading: showAccountInfo && currentAccount != null
            ? const CompactAccountSwitcher()
            : null,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Get.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: themeService.textPrimaryColor,
              ),
            ),
            if (showAccountInfo && currentAccount != null)
              Text(
                currentAccount.name,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: themeService.textSecondaryColor,
                ),
              ),
          ],
        ),
        actions: [
          if (showAccountInfo && currentAccount != null) ...[
            // Flag indicator
            Container(
              margin: const EdgeInsets.only(right: 8),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: themeService.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: themeService.primaryColor.withOpacity(0.3),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    themeService.flagEmoji,
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    AccountType.fromString(currentAccount.accountType).displayName,
                    style: Get.textTheme.labelSmall?.copyWith(
                      color: themeService.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
          if (actions != null) ...actions!,
          // Drawer button
          IconButton(
            onPressed: onDrawerTap ?? () => Scaffold.of(context).openEndDrawer(),
            icon: Icon(
              Icons.menu_rounded,
              color: themeService.textPrimaryColor,
            ),
            tooltip: 'Account Menu',
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1),
          child: Container(
            height: 1,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  themeService.primaryColor.withOpacity(0.3),
                  themeService.secondaryColor.withOpacity(0.3),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildAccountAvatar(UserAccount account, ThemeService themeService) {
    return GestureDetector(
      onTap: onDrawerTap ?? () => Get.context != null 
          ? Scaffold.of(Get.context!).openEndDrawer() 
          : null,
      child: Container(
        margin: const EdgeInsets.all(8),
        child: CircleAvatar(
          radius: 20,
          backgroundColor: _getAccountTypeColor(account.accountType, themeService),
          backgroundImage: account.profileImageUrl != null
              ? NetworkImage(account.profileImageUrl!)
              : null,
          child: account.profileImageUrl == null
              ? Icon(
                  _getAccountTypeIcon(account.accountType),
                  color: themeService.getContrastingTextColor(
                    _getAccountTypeColor(account.accountType, themeService),
                  ),
                  size: 20,
                )
              : null,
        ),
      ),
    );
  }

  Color _getAccountTypeColor(String accountType, ThemeService themeService) {
    switch (accountType.toLowerCase()) {
      case 'business':
        return themeService.secondaryColor;
      case 'personal':
      default:
        return themeService.primaryColor;
    }
  }

  IconData _getAccountTypeIcon(String accountType) {
    switch (accountType.toLowerCase()) {
      case 'business':
        return Icons.business_rounded;
      case 'personal':
      default:
        return Icons.person_rounded;
    }
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
