import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/payment_methods.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/services/theme_service.dart';

class PaymentMethodSelector extends StatefulWidget {
  final PaymentMethod? initialPaymentMethod;
  final Function(PaymentMethod?) onPaymentMethodChanged;
  final bool isRequired;
  final String? label;

  const PaymentMethodSelector({
    super.key,
    this.initialPaymentMethod,
    required this.onPaymentMethodChanged,
    this.isRequired = false,
    this.label,
  });

  @override
  State<PaymentMethodSelector> createState() => _PaymentMethodSelectorState();
}

class _PaymentMethodSelectorState extends State<PaymentMethodSelector> {
  PaymentCategory? _selectedCategory;
  String? _selectedSpecification;
  
  @override
  void initState() {
    super.initState();
    if (widget.initialPaymentMethod != null) {
      _selectedCategory = widget.initialPaymentMethod!.category;
      _selectedSpecification = widget.initialPaymentMethod!.specification;
    }
  }

  void _onCategoryChanged(PaymentCategory? category) {
    setState(() {
      _selectedCategory = category;
      _selectedSpecification = null; // Reset specification when category changes
    });
    
    // Notify parent that payment method changed
    widget.onPaymentMethodChanged(null);
  }

  void _onSpecificationChanged(String? specification) {
    setState(() {
      _selectedSpecification = specification;
    });
    
    // Create complete payment method and notify parent
    if (_selectedCategory != null && specification != null) {
      final paymentMethod = PaymentMethod(
        category: _selectedCategory!,
        specification: specification,
      );
      widget.onPaymentMethodChanged(paymentMethod);
    } else {
      widget.onPaymentMethodChanged(null);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final themeService = ThemeService.to;
      final isDark = themeService.isDarkMode;

      return LayoutBuilder(
        builder: (context, constraints) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
          if (widget.label != null) ...[
            Text(
              widget.label!,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: isDark ? Colors.white : AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 8),
          ],
          
          // Category Selection
          Container(
            width: constraints.maxWidth,
            constraints: BoxConstraints(
              maxWidth: constraints.maxWidth,
              minWidth: 0,
            ),
            decoration: BoxDecoration(
              color: isDark ? AppColors.surfaceDark : Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isDark ? AppColors.borderDark : AppColors.borderLight,
              ),
            ),
            child: DropdownButtonFormField<PaymentCategory>(
              value: _selectedCategory,
              isExpanded: true,
              decoration: InputDecoration(
                labelText: 'Payment Category${widget.isRequired ? ' *' : ''}',
                prefixIcon: Icon(
                  _selectedCategory?.icon ?? Icons.payment_rounded,
                  color: AppColors.primary,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              items: PaymentMethods.getPrimaryCategories().map((category) {
                return DropdownMenuItem<PaymentCategory>(
                  value: category,
                  child: Container(
                    width: double.infinity,
                    constraints: const BoxConstraints(
                      maxHeight: 60, // Limit height to prevent overflow
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(category.icon, size: 18, color: AppColors.primary),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                category.name,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                              Text(
                                category.description,
                                style: TextStyle(
                                  fontSize: 11,
                                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ));
              }).toList(),
              onChanged: _onCategoryChanged,
              validator: widget.isRequired ? (value) {
                if (value == null) {
                  return 'Please select a payment category';
                }
                return null;
              } : null,
            ),
          ),
          
          // Specification Selection (only show if category is selected)
          if (_selectedCategory != null) ...[
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: isDark ? AppColors.surfaceDark : Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isDark ? AppColors.borderDark : AppColors.borderLight,
                ),
              ),
              child: DropdownButtonFormField<String>(
                value: _selectedSpecification,
                decoration: InputDecoration(
                  labelText: 'Specific Method${widget.isRequired ? ' *' : ''}',
                  prefixIcon: Icon(
                    Icons.arrow_forward_ios_rounded,
                    color: AppColors.primary,
                    size: 16,
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
                items: _selectedCategory!.specifications.map((specification) {
                  return DropdownMenuItem<String>(
                    value: specification,
                    child: Text(specification),
                  );
                }).toList(),
                onChanged: _onSpecificationChanged,
                validator: widget.isRequired ? (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select a specific payment method';
                  }
                  return null;
                } : null,
              ),
            ),
          ],
          
          // Display selected payment method
          if (_selectedCategory != null && _selectedSpecification != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.primary.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    _selectedCategory!.icon,
                    color: AppColors.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Selected: ${_selectedCategory!.name} - $_selectedSpecification',
                      style: TextStyle(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            ],
          ],
        );
        },
      );
    });
  }
}

/// Simple payment method display widget
class PaymentMethodDisplay extends StatelessWidget {
  final PaymentMethod? paymentMethod;
  final bool showIcon;
  final TextStyle? textStyle;

  const PaymentMethodDisplay({
    super.key,
    this.paymentMethod,
    this.showIcon = true,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    if (paymentMethod == null) {
      return Text(
        'No payment method selected',
        style: textStyle ?? TextStyle(
          color: Colors.grey[600],
          fontStyle: FontStyle.italic,
        ),
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (showIcon) ...[
          Icon(
            paymentMethod!.icon,
            size: 16,
            color: AppColors.primary,
          ),
          const SizedBox(width: 6),
        ],
        Text(
          paymentMethod!.displayName,
          style: textStyle,
        ),
      ],
    );
  }
}
