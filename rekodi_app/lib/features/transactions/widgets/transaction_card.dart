import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/theme/typography.dart';
import '../../../core/database/database.dart';
import '../../../core/services/currency_service.dart';

class TransactionCard extends StatelessWidget {
  final PersonalTransaction transaction;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onDuplicate;

  const TransactionCard({
    super.key,
    required this.transaction,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onDuplicate,
  });

  @override
  Widget build(BuildContext context) {
    final isIncome = transaction.type == 'income';
    final amount = transaction.amount;
    final categoryIcon = _getCategoryIcon(transaction.category);
    final categoryColor = _getCategoryColor(transaction.category);

    return Dismissible(
      key: Key(transaction.id.toString()),
      background: _buildSwipeBackground(true),
      secondaryBackground: _buildSwipeBackground(false),
      onDismissed: (direction) {
        if (direction == DismissDirection.endToStart) {
          onDelete?.call();
        } else {
          onEdit?.call();
        }
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: DesignSystem.spacingSmall),
        decoration: BoxDecoration(
          color: DesignSystem.white,
          borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
          boxShadow: DesignSystem.shadowMedium,
          border: Border.all(
            color: DesignSystem.textSecondary.withOpacity(0.1),
            width: 1,
          ),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            onLongPress: () => _showActionMenu(context),
            borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
            child: Padding(
              padding: const EdgeInsets.all(DesignSystem.spacingMedium),
              child: Row(
                children: [
                  // Category icon
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: categoryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(DesignSystem.radiusSmall),
                    ),
                    child: Icon(
                      categoryIcon,
                      color: categoryColor,
                      size: 24,
                    ),
                  ),
                  
                  SizedBox(width: DesignSystem.spacingMedium),
                  
                  // Transaction details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          transaction.title,
                          style: AppTypography.bodyLarge.copyWith(
                            fontWeight: FontWeight.w600,
                            color: DesignSystem.textPrimary,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: DesignSystem.spacingXSmall),
                        Row(
                          children: [
                            Text(
                              transaction.category,
                              style: AppTypography.bodySmall.copyWith(
                                color: DesignSystem.textSecondary,
                              ),
                            ),
                            if (transaction.description?.isNotEmpty == true) ...[
                              Text(
                                ' • ',
                                style: AppTypography.bodySmall.copyWith(
                                  color: DesignSystem.textSecondary,
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  transaction.description!,
                                  style: AppTypography.bodySmall.copyWith(
                                    color: DesignSystem.textSecondary,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  SizedBox(width: DesignSystem.spacingMedium),
                  
                  // Amount and time
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Obx(() {
                        final currencyService = CurrencyService.to;
                        return Text(
                          '${isIncome ? '+' : '-'}${currencyService.currentCurrency.value.formatAmount(amount)}',
                          style: AppTypography.titleMedium.copyWith(
                            fontWeight: FontWeight.w700,
                            color: isIncome ? DesignSystem.primaryTeal : DesignSystem.secondaryCoral,
                          ),
                        );
                      }),
                      SizedBox(height: DesignSystem.spacingXSmall),
                      Text(
                        DateFormat('h:mm a').format(transaction.date),
                        style: AppTypography.bodySmall.copyWith(
                          color: DesignSystem.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showActionMenu(BuildContext context) {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);

    showMenu(
      context: Get.context!,
      position: RelativeRect.fromLTRB(
        position.dx,
        position.dy + renderBox.size.height,
        position.dx + renderBox.size.width,
        position.dy + renderBox.size.height + 100,
      ),
      items: [
        PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, color: DesignSystem.primaryTeal, size: 20),
              SizedBox(width: DesignSystem.spacingSmall),
              Text('Edit Transaction'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'duplicate',
          child: Row(
            children: [
              Icon(Icons.content_copy, color: DesignSystem.primarySkyBlue, size: 20),
              SizedBox(width: DesignSystem.spacingSmall),
              Text('Duplicate Multiple'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, color: DesignSystem.secondaryCoral, size: 20),
              SizedBox(width: DesignSystem.spacingSmall),
              Text('Delete Transaction'),
            ],
          ),
        ),
      ],
    ).then((value) {
      switch (value) {
        case 'edit':
          onEdit?.call();
          break;
        case 'duplicate':
          onDuplicate?.call();
          break;
        case 'delete':
          onDelete?.call();
          break;
      }
    });
  }

  Widget _buildSwipeBackground(bool isEdit) {
    return Container(
      margin: EdgeInsets.only(bottom: DesignSystem.spacingSmall),
      decoration: BoxDecoration(
        color: isEdit ? DesignSystem.primaryTeal : DesignSystem.secondaryCoral,
        borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
      ),
      child: Align(
        alignment: isEdit ? Alignment.centerLeft : Alignment.centerRight,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: DesignSystem.spacingLarge),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                isEdit ? Icons.edit_rounded : Icons.delete_rounded,
                color: DesignSystem.white,
                size: 24,
              ),
              SizedBox(height: DesignSystem.spacingXSmall),
              Text(
                isEdit ? 'Edit' : 'Delete',
                style: AppTypography.bodySmall.copyWith(
                  color: DesignSystem.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'food':
        return Icons.restaurant_rounded;
      case 'transport':
        return Icons.directions_car_rounded;
      case 'shopping':
        return Icons.shopping_bag_rounded;
      case 'entertainment':
        return Icons.movie_rounded;
      case 'health':
        return Icons.local_hospital_rounded;
      case 'salary':
        return Icons.work_rounded;
      case 'freelance':
        return Icons.laptop_rounded;
      case 'investment':
        return Icons.trending_up_rounded;
      case 'gift':
        return Icons.card_giftcard_rounded;
      default:
        return Icons.category_rounded;
    }
  }

  Color _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'food':
        return const Color(0xFFFF6B6B);
      case 'transport':
        return const Color(0xFF4ECDC4);
      case 'shopping':
        return const Color(0xFFFFE66D);
      case 'entertainment':
        return const Color(0xFFFF8B94);
      case 'health':
        return const Color(0xFF95E1D3);
      case 'salary':
        return DesignSystem.primaryTeal;
      case 'freelance':
        return const Color(0xFF6C5CE7);
      case 'investment':
        return const Color(0xFF00B894);
      case 'gift':
        return const Color(0xFFE17055);
      default:
        return DesignSystem.textSecondary;
    }
  }
}
