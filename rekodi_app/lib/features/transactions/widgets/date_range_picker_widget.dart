import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/theme/typography.dart';
import '../../../shared/widgets/compact_date_picker.dart';

class DateRangePickerWidget extends StatefulWidget {
  final DateTimeRange? initialDateRange;
  final Function(DateTimeRange?) onDateRangeChanged;
  final Function(int) onDaySelected;

  const DateRangePickerWidget({
    super.key,
    this.initialDateRange,
    required this.onDateRangeChanged,
    required this.onDaySelected,
  });

  @override
  State<DateRangePickerWidget> createState() => _DateRangePickerWidgetState();
}

class _DateRangePickerWidgetState extends State<DateRangePickerWidget> {
  DateTimeRange? _selectedDateRange;
  int? _selectedDay;
  late List<int> _dayTabs;

  @override
  void initState() {
    super.initState();
    _selectedDateRange = widget.initialDateRange;
    _generateDayTabs();
  }

  void _generateDayTabs() {
    final now = DateTime.now();
    final daysInMonth = DateTime(now.year, now.month + 1, 0).day;
    _dayTabs = List.generate(daysInMonth, (index) => daysInMonth - index);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: DesignSystem.white,
        borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
        boxShadow: DesignSystem.shadowSmall,
      ),
      child: CompactDatePicker(
        selectedDate: _selectedDay != null
            ? DateTime(DateTime.now().year, DateTime.now().month, _selectedDay!)
            : null,
        selectedRange: _selectedDateRange,
        onDateSelected: (date) {
          setState(() {
            _selectedDay = date.day;
            _selectedDateRange = null; // Clear range when day is selected
          });
          widget.onDaySelected(date.day);
        },
        onRangeSelected: (range) {
          setState(() {
            _selectedDateRange = range;
            _selectedDay = null; // Clear day selection when range is selected
          });
          widget.onDateRangeChanged(range);
        },
        allowRangeSelection: true,
        label: 'Filter by Date',
      ),
    );
  }

  void _clearDateRange() {
    setState(() {
      _selectedDateRange = null;
      _selectedDay = null;
    });
    widget.onDateRangeChanged(null);
  }
}
