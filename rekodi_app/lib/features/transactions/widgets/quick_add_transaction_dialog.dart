import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/models/transaction.dart';
import '../../../core/services/personal_transaction_service.dart';
import '../../../core/services/subscription_service.dart';
import '../../../core/services/theme_service.dart';

class QuickAddTransactionDialog extends StatefulWidget {
  final TransactionType? initialType;

  const QuickAddTransactionDialog({
    super.key,
    this.initialType,
  });

  @override
  State<QuickAddTransactionDialog> createState() => _QuickAddTransactionDialogState();
}

class _QuickAddTransactionDialogState extends State<QuickAddTransactionDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  TransactionType _selectedType = TransactionType.expense;
  TransactionCategory _selectedCategory = TransactionCategory.food;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.initialType != null) {
      _selectedType = widget.initialType!;
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final themeService = ThemeService.to;
      
      return Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 400),
          decoration: BoxDecoration(
            color: themeService.surfaceColor,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: (_selectedType == TransactionType.income 
                              ? themeService.incomeColor 
                              : themeService.expenseColor).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          _selectedType == TransactionType.income 
                              ? Icons.trending_up_rounded 
                              : Icons.trending_down_rounded,
                          color: _selectedType == TransactionType.income 
                              ? themeService.incomeColor 
                              : themeService.expenseColor,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Quick Add ${_selectedType.name.capitalize}',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: themeService.textPrimaryColor,
                              ),
                            ),
                            Text(
                              'Fast transaction entry',
                              style: TextStyle(
                                fontSize: 14,
                                color: themeService.textPrimaryColor.withOpacity(0.6),
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: Icon(
                          Icons.close_rounded,
                          color: themeService.textPrimaryColor.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Type Toggle
                  _buildTypeToggle(themeService),
                  
                  const SizedBox(height: 20),
                  
                  // Amount Field
                  _buildAmountField(themeService),
                  
                  const SizedBox(height: 16),
                  
                  // Category Selection
                  _buildCategorySelection(themeService),
                  
                  const SizedBox(height: 16),
                  
                  // Description Field
                  _buildDescriptionField(themeService),
                  
                  const SizedBox(height: 24),
                  
                  // Action Buttons
                  Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                          child: Text(
                            'Cancel',
                            style: TextStyle(
                              color: themeService.textPrimaryColor.withOpacity(0.6),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        flex: 2,
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _saveTransaction,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: _selectedType == TransactionType.income 
                                ? themeService.incomeColor 
                                : themeService.expenseColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 0,
                          ),
                          child: _isLoading
                              ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.white,
                                  ),
                                )
                              : Text(
                                  'Add ${_selectedType.name.capitalize}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          Get.toNamed('/add-transaction', arguments: {
                            'isIncome': _selectedType == TransactionType.income,
                            'amount': _amountController.text,
                            'description': _descriptionController.text,
                            'category': _selectedCategory,
                          });
                        },
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.all(16),
                        ),
                        child: Icon(
                          Icons.open_in_full_rounded,
                          color: themeService.textPrimaryColor.withOpacity(0.6),
                          size: 20,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildTypeToggle(ThemeService themeService) {
    return Container(
      decoration: BoxDecoration(
        color: themeService.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: themeService.borderColor),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedType = TransactionType.income),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedType == TransactionType.income 
                      ? themeService.incomeColor 
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.trending_up_rounded,
                      color: _selectedType == TransactionType.income 
                          ? Colors.white 
                          : themeService.incomeColor,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Income',
                      style: TextStyle(
                        color: _selectedType == TransactionType.income 
                            ? Colors.white 
                            : themeService.textPrimaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedType = TransactionType.expense),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _selectedType == TransactionType.expense 
                      ? themeService.expenseColor 
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.trending_down_rounded,
                      color: _selectedType == TransactionType.expense 
                          ? Colors.white 
                          : themeService.expenseColor,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Expense',
                      style: TextStyle(
                        color: _selectedType == TransactionType.expense 
                            ? Colors.white 
                            : themeService.textPrimaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountField(ThemeService themeService) {
    return TextFormField(
      controller: _amountController,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
      ],
      style: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: themeService.textPrimaryColor,
      ),
      decoration: InputDecoration(
        labelText: 'Amount',
        prefixText: 'KSh ',
        prefixStyle: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: themeService.textPrimaryColor.withOpacity(0.6),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: themeService.borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: _selectedType == TransactionType.income 
                ? themeService.incomeColor 
                : themeService.expenseColor,
            width: 2,
          ),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter an amount';
        }
        if (double.tryParse(value) == null) {
          return 'Please enter a valid amount';
        }
        if (double.parse(value) <= 0) {
          return 'Amount must be greater than 0';
        }
        return null;
      },
      autofocus: true,
    );
  }

  Widget _buildCategorySelection(ThemeService themeService) {
    final categories = _selectedType == TransactionType.income
        ? [TransactionCategory.salary, TransactionCategory.business, TransactionCategory.investment_income, TransactionCategory.other]
        : [TransactionCategory.food, TransactionCategory.transportation, TransactionCategory.shopping, TransactionCategory.entertainment, TransactionCategory.utilities, TransactionCategory.other];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Category',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: themeService.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: categories.map((category) {
            final isSelected = category == _selectedCategory;
            return GestureDetector(
              onTap: () => setState(() => _selectedCategory = category),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? (_selectedType == TransactionType.income 
                          ? themeService.incomeColor 
                          : themeService.expenseColor)
                      : themeService.surfaceColor,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected 
                        ? Colors.transparent 
                        : themeService.borderColor,
                  ),
                ),
                child: Text(
                  category.name.capitalize ?? '',
                  style: TextStyle(
                    color: isSelected 
                        ? Colors.white 
                        : themeService.textPrimaryColor,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildDescriptionField(ThemeService themeService) {
    return TextFormField(
      controller: _descriptionController,
      style: TextStyle(color: themeService.textPrimaryColor),
      decoration: InputDecoration(
        labelText: 'Description (Optional)',
        hintText: 'What was this for?',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: themeService.borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: _selectedType == TransactionType.income 
                ? themeService.incomeColor 
                : themeService.expenseColor,
            width: 2,
          ),
        ),
      ),
      maxLines: 2,
    );
  }

  Future<void> _saveTransaction() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Check if user can add transactions
    if (!SubscriptionService.to.canAddTransaction()) {
      SubscriptionService.to.showFeatureLockedDialog(PremiumFeature.unlimitedTransactions);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await PersonalTransactionService.to.addTransaction(
        title: _descriptionController.text.trim().isEmpty 
            ? '${_selectedType.name.capitalize} - ${_selectedCategory.name.capitalize}'
            : _descriptionController.text.trim(),
        description: _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
        amount: double.parse(_amountController.text),
        type: _selectedType,
        category: _selectedCategory,
        date: DateTime.now(),
      );

      if (success) {
        Navigator.of(context).pop();
        Get.snackbar(
          'Success',
          '${_selectedType.name.capitalize} added successfully',
          backgroundColor: _selectedType == TransactionType.income 
              ? AppColors.income 
              : AppColors.expense,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
          margin: const EdgeInsets.all(16),
          borderRadius: 12,
          duration: const Duration(seconds: 2),
        );
      } else {
        Get.snackbar(
          'Error',
          'Failed to add transaction',
          backgroundColor: AppColors.error,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to add transaction: ${e.toString()}',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
