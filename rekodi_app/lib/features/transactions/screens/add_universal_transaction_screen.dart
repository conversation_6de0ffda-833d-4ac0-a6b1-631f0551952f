import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/payment_methods.dart';
import '../../../core/services/theme_service.dart';
import '../../../core/services/account_service.dart';
import '../../../core/models/account_type.dart';
import '../../../core/models/transaction.dart';
import '../../../core/models/recurring_transaction.dart';
import '../../../core/services/personal_transaction_service.dart';
import '../../../core/services/recurring_transaction_service.dart';
import '../../common/widgets/common_app_bar.dart';
import '../../common/widgets/payment_method_selector.dart';
import '../../../shared/widgets/compact_date_picker.dart';

class AddUniversalTransactionScreen extends StatefulWidget {
  final String? transactionType; // 'income', 'expense', 'sale', 'purchase'
  final bool? isRecurring;

  const AddUniversalTransactionScreen({
    super.key,
    this.transactionType,
    this.isRecurring = false,
  });

  @override
  State<AddUniversalTransactionScreen> createState() => _AddUniversalTransactionScreenState();
}

class _AddUniversalTransactionScreenState extends State<AddUniversalTransactionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();
  final _transactionCostController = TextEditingController(text: '0.00');
  
  String _selectedType = 'income';
  String _selectedCategory = 'salary';
  DateTime _selectedDate = DateTime.now();
  PaymentMethod? _selectedPaymentMethod;
  bool _isRecurring = false;
  RecurrenceType _selectedRecurrence = RecurrenceType.monthly;
  DateTime? _endDate;
  bool _includeTime = false;
  TimeOfDay _selectedTime = TimeOfDay.now();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    // Get arguments from route
    final arguments = Get.arguments as Map<String, dynamic>?;

    // Set initial values from parameters or arguments
    if (widget.transactionType != null) {
      _selectedType = widget.transactionType!;
    } else if (arguments != null && arguments['transactionType'] != null) {
      _selectedType = arguments['transactionType'];
    }

    if (widget.isRecurring == true) {
      _isRecurring = true;
    }

    _updateCategoriesForType();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _amountController.dispose();
    _transactionCostController.dispose();
    super.dispose();
  }

  void _updateCategoriesForType() {
    // Update available categories based on selected type
    final isBusinessAccount = AccountService.to.currentAccountType == AccountType.business;
    
    switch (_selectedType) {
      case 'income':
        _selectedCategory = 'salary';
        break;
      case 'expense':
        _selectedCategory = 'food';
        break;
      case 'sale':
        _selectedCategory = 'product_sales';
        break;
      case 'purchase':
        _selectedCategory = 'raw_materials';
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final themeService = ThemeService.to;
      final isBusinessAccount = AccountService.to.currentAccountType == AccountType.business;
      
      return Scaffold(
        backgroundColor: themeService.backgroundColor,
        appBar: CommonAppBar(
          title: _isRecurring ? 'Add Recurring ${_selectedType.capitalizeFirst ?? _selectedType}' : 'Add ${_selectedType.capitalizeFirst ?? _selectedType}',
          showAccountInfo: true,
          actions: [
            TextButton(
              onPressed: _isLoading ? null : _saveTransaction,
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Save'),
            ),
          ],
        ),
        body: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Transaction Type Selector
                _buildTransactionTypeSelector(isBusinessAccount, themeService),
                
                const SizedBox(height: 20),
                
                // Title Field
                _buildTextField(
                  controller: _titleController,
                  label: 'Title',
                  hint: 'Enter transaction title',
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter a title';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                // Amount Field
                _buildTextField(
                  controller: _amountController,
                  label: 'Amount',
                  hint: '0.00',
                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter an amount';
                    }
                    if (double.tryParse(value) == null) {
                      return 'Please enter a valid amount';
                    }
                    if (double.parse(value) <= 0) {
                      return 'Amount must be greater than 0';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Transaction Cost Field (for expenses and purchases)
                if (_selectedType == 'expense' || _selectedType == 'purchase') ...[
                  _buildTextField(
                    controller: _transactionCostController,
                    label: 'Transaction Cost/Charge',
                    hint: '0.00',
                    keyboardType: TextInputType.numberWithOptions(decimal: true),
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        if (double.tryParse(value) == null) {
                          return 'Please enter a valid cost';
                        }
                        if (double.parse(value) < 0) {
                          return 'Cost cannot be negative';
                        }
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                ],

                // Category Selector
                _buildCategorySelector(themeService),
                
                const SizedBox(height: 16),
                
                // Description Field
                _buildTextField(
                  controller: _descriptionController,
                  label: 'Description (Optional)',
                  hint: 'Enter description',
                  maxLines: 3,
                ),
                
                const SizedBox(height: 16),
                
                // Date Selector
                _buildDateSelector(themeService),
                
                const SizedBox(height: 16),
                
                // Payment Method Selector
                _buildPaymentMethodSelector(themeService),
                
                const SizedBox(height: 16),
                
                // Recurring Transaction Toggle
                _buildRecurringToggle(themeService),
                
                if (_isRecurring) ...[
                  const SizedBox(height: 16),
                  _buildRecurrenceSelector(themeService),

                  const SizedBox(height: 16),
                  _buildTimeToggle(themeService),

                  if (_includeTime) ...[
                    const SizedBox(height: 16),
                    _buildTimeSelector(themeService),
                  ],

                  const SizedBox(height: 16),
                  _buildEndDateSelector(themeService),
                ],
                
                const SizedBox(height: 32),
                
                // Save Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveTransaction,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _getTypeColor(),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : Text(
                            _isRecurring ? 'Create Recurring ${_selectedType.capitalizeFirst ?? _selectedType}' : 'Add ${_selectedType.capitalizeFirst ?? _selectedType}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  Widget _buildTransactionTypeSelector(bool isBusinessAccount, ThemeService themeService) {
    final types = isBusinessAccount 
        ? ['sale', 'purchase', 'income', 'expense']
        : ['income', 'expense'];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Transaction Type',
          style: TextStyle(
            color: themeService.textPrimaryColor,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: themeService.surfaceColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: themeService.primaryColor.withOpacity(0.2)),
          ),
          child: Row(
            children: types.map((type) {
              final isSelected = _selectedType == type;
              return Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedType = type;
                      _updateCategoriesForType();
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      color: isSelected ? _getTypeColor() : Colors.transparent,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      type.capitalizeFirst ?? type,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: isSelected ? Colors.white : themeService.textSecondaryColor,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Color _getTypeColor() {
    switch (_selectedType) {
      case 'income':
      case 'sale':
        return Colors.green;
      case 'expense':
        return Colors.red;
      case 'purchase':
        return Colors.orange;
      default:
        return Colors.blue;
    }
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Obx(() {
      final themeService = ThemeService.to;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              color: themeService.textPrimaryColor,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: controller,
            keyboardType: keyboardType,
            maxLines: maxLines,
            validator: validator,
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: TextStyle(color: themeService.textSecondaryColor),
              filled: true,
              fillColor: themeService.surfaceColor,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: themeService.primaryColor.withOpacity(0.2)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: themeService.primaryColor.withOpacity(0.2)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: themeService.primaryColor),
              ),
            ),
          ),
        ],
      );
    });
  }

  Widget _buildCategorySelector(ThemeService themeService) {
    final categories = _getCategoriesForType();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Category',
          style: TextStyle(
            color: themeService.textPrimaryColor,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            color: themeService.surfaceColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: themeService.primaryColor.withOpacity(0.2)),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedCategory,
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    _selectedCategory = newValue;
                  });
                }
              },
              items: categories.map<DropdownMenuItem<String>>((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(
                    value.replaceAll('_', ' ').capitalizeFirst ?? value,
                    style: TextStyle(color: themeService.textPrimaryColor),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  List<String> _getCategoriesForType() {
    switch (_selectedType) {
      case 'income':
        return ['salary', 'freelance', 'investment', 'business', 'other_income'];
      case 'expense':
        return ['food', 'transportation', 'housing', 'utilities', 'entertainment', 'healthcare', 'shopping', 'other_expense'];
      case 'sale':
        return ['product_sales', 'service_sales', 'subscription', 'commission'];
      case 'purchase':
        return ['raw_materials', 'equipment', 'office_supplies', 'software', 'services'];
      default:
        return ['other'];
    }
  }

  Widget _buildDateSelector(ThemeService themeService) {
    return CompactDatePicker(
      selectedDate: _selectedDate,
      onDateSelected: (date) {
        setState(() {
          _selectedDate = date;
        });
      },
      label: 'Transaction Date',
      allowFutureDates: true,
    );
  }

  Widget _buildPaymentMethodSelector(ThemeService themeService) {
    return PaymentMethodSelector(
      label: 'Payment Method (Optional)',
      initialPaymentMethod: _selectedPaymentMethod,
      onPaymentMethodChanged: (PaymentMethod? paymentMethod) {
        setState(() {
          _selectedPaymentMethod = paymentMethod;
        });
      },
      isRequired: false,
    );
  }

  Widget _buildRecurringToggle(ThemeService themeService) {
    return Row(
      children: [
        Switch(
          value: _isRecurring,
          onChanged: (bool value) {
            setState(() {
              _isRecurring = value;
            });
          },
          activeColor: themeService.primaryColor,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Recurring Transaction',
                style: TextStyle(
                  color: themeService.textPrimaryColor,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                'Automatically repeat this transaction',
                style: TextStyle(
                  color: themeService.textSecondaryColor,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRecurrenceSelector(ThemeService themeService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recurrence',
          style: TextStyle(
            color: themeService.textPrimaryColor,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            color: themeService.surfaceColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: themeService.primaryColor.withOpacity(0.2)),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<RecurrenceType>(
              value: _selectedRecurrence,
              onChanged: (RecurrenceType? newValue) {
                if (newValue != null) {
                  setState(() {
                    _selectedRecurrence = newValue;
                  });
                }
              },
              items: RecurrenceType.values.map<DropdownMenuItem<RecurrenceType>>((RecurrenceType value) {
                return DropdownMenuItem<RecurrenceType>(
                  value: value,
                  child: Text(
                    value.displayName,
                    style: TextStyle(color: themeService.textPrimaryColor),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEndDateSelector(ThemeService themeService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'End Date (Optional)',
          style: TextStyle(
            color: themeService.textPrimaryColor,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: _selectEndDate,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: themeService.surfaceColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: themeService.primaryColor.withOpacity(0.2)),
            ),
            child: Row(
              children: [
                Icon(Icons.event_rounded, color: themeService.primaryColor),
                const SizedBox(width: 12),
                Text(
                  _endDate != null
                      ? '${_endDate!.day}/${_endDate!.month}/${_endDate!.year}'
                      : 'No end date',
                  style: TextStyle(
                    color: _endDate != null
                        ? themeService.textPrimaryColor
                        : themeService.textSecondaryColor,
                    fontSize: 16,
                  ),
                ),
                const Spacer(),
                if (_endDate != null)
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _endDate = null;
                      });
                    },
                    child: Icon(Icons.clear_rounded, color: themeService.textSecondaryColor),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Removed traditional date picker - using compact date picker in UI instead

  Future<void> _selectEndDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate ?? DateTime.now().add(const Duration(days: 365)),
      firstDate: _selectedDate,
      lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
    );
    if (picked != null) {
      setState(() {
        _endDate = picked;
      });
    }
  }

  Future<void> _saveTransaction() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final amount = double.parse(_amountController.text);

      if (_isRecurring) {
        // Create recurring transaction
        final success = await RecurringTransactionService.to.addRecurringTransactionWithParams(
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim().isEmpty
              ? null
              : _descriptionController.text.trim(),
          amount: amount,
          type: _selectedType,
          category: _selectedCategory,
          recurrenceType: _selectedRecurrence,
          startDate: _selectedDate,
          endDate: _endDate,
          paymentMethod: _selectedPaymentMethod?.toStorageString(),
        );

        if (success) {
          Get.back();
          Get.snackbar(
            'Success',
            'Recurring ${_selectedType} created successfully',
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
        } else {
          Get.snackbar(
            'Error',
            'Failed to create recurring ${_selectedType}',
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      } else {
        // Create one-time transaction
        TransactionType transactionType;
        switch (_selectedType) {
          case 'income':
          case 'sale':
            transactionType = TransactionType.income;
            break;
          case 'expense':
          case 'purchase':
            transactionType = TransactionType.expense;
            break;
          default:
            transactionType = TransactionType.expense;
        }

        TransactionCategory transactionCategory;
        try {
          transactionCategory = TransactionCategory.values
              .firstWhere((c) => c.name.toLowerCase() == _selectedCategory.toLowerCase());
        } catch (e) {
          transactionCategory = transactionType == TransactionType.income
              ? TransactionCategory.other_income
              : TransactionCategory.other_expense;
        }

        final success = await PersonalTransactionService.to.addTransaction(
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim().isEmpty
              ? null
              : _descriptionController.text.trim(),
          amount: amount,
          type: transactionType,
          category: transactionCategory,
          date: _selectedDate,
          paymentMethod: _selectedPaymentMethod?.toStorageString(),
        );

        if (success) {
          Get.back();
          Get.snackbar(
            'Success',
            '${_selectedType.capitalizeFirst ?? _selectedType} added successfully',
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
        } else {
          Get.snackbar(
            'Error',
            'Failed to add ${_selectedType}',
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'An error occurred: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Widget _buildTimeToggle(ThemeService themeService) {
    return Row(
      children: [
        Switch(
          value: _includeTime,
          onChanged: (bool value) {
            setState(() {
              _includeTime = value;
            });
          },
          activeColor: themeService.primaryColor,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Include Specific Time',
                style: TextStyle(
                  color: themeService.textPrimaryColor,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                'Set a specific time for recurring transactions',
                style: TextStyle(
                  color: themeService.textSecondaryColor,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTimeSelector(ThemeService themeService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Time',
          style: TextStyle(
            color: themeService.textPrimaryColor,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: () async {
            final TimeOfDay? picked = await showTimePicker(
              context: context,
              initialTime: _selectedTime,
            );
            if (picked != null) {
              setState(() {
                _selectedTime = picked;
              });
            }
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: themeService.surfaceColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: themeService.primaryColor.withOpacity(0.2)),
            ),
            child: Row(
              children: [
                Icon(Icons.access_time_rounded, color: themeService.primaryColor),
                const SizedBox(width: 12),
                Text(
                  _selectedTime.format(context),
                  style: TextStyle(
                    color: themeService.textPrimaryColor,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
