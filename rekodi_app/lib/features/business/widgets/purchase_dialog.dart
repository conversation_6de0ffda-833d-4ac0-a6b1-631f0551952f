import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';
import '../../../core/models/business_product.dart';
import '../../../core/models/business_transaction.dart';
import '../../../core/services/business_product_service.dart';
import '../../../core/services/currency_service.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/theme/typography.dart';

class PurchaseDialog extends StatefulWidget {
  final BusinessProduct product;
  final Function(BusinessTransaction) onPurchaseCompleted;

  const PurchaseDialog({
    Key? key,
    required this.product,
    required this.onPurchaseCompleted,
  });

  @override
  State<PurchaseDialog> createState() => _PurchaseDialogState();
}

class _PurchaseDialogState extends State<PurchaseDialog> {
  final _formKey = GlobalKey<FormState>();
  final _quantityController = TextEditingController(text: '1');
  final _unitPriceController = TextEditingController();
  final _supplierNameController = TextEditingController();
  final _supplierContactController = TextEditingController();
  final _notesController = TextEditingController();
  
  bool _isLoading = false;
  double _totalAmount = 0.0;

  @override
  void initState() {
    super.initState();
    _unitPriceController.text = widget.product.price.toStringAsFixed(2);
    _calculateTotal();
    
    _quantityController.addListener(_calculateTotal);
    _unitPriceController.addListener(_calculateTotal);
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _unitPriceController.dispose();
    _supplierNameController.dispose();
    _supplierContactController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _calculateTotal() {
    final quantity = int.tryParse(_quantityController.text) ?? 1;
    final unitPrice = double.tryParse(_unitPriceController.text) ?? 0.0;
    setState(() {
      _totalAmount = quantity * unitPrice;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
      ),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600),
        padding: EdgeInsets.all(DesignSystem.spacingLarge),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              SizedBox(height: DesignSystem.spacingLarge),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildProductInfo(),
                      SizedBox(height: DesignSystem.spacingLarge),
                      _buildQuantityField(),
                      SizedBox(height: DesignSystem.spacingMedium),
                      _buildPriceField(),
                      SizedBox(height: DesignSystem.spacingLarge),
                      _buildSupplierInfo(),
                      SizedBox(height: DesignSystem.spacingMedium),
                      _buildNotesField(),
                      SizedBox(height: DesignSystem.spacingLarge),
                      _buildTotalSection(),
                    ],
                  ),
                ),
              ),
              SizedBox(height: DesignSystem.spacingLarge),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.shopping_cart_rounded,
          color: DesignSystem.primaryTeal,
          size: 24,
        ),
        SizedBox(width: DesignSystem.spacingSmall),
        Expanded(
          child: Text(
            'Purchase ${widget.product.name}',
            style: AppTypography.titleLarge.copyWith(
              color: DesignSystem.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
        ),
      ],
    );
  }

  Widget _buildProductInfo() {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacingMedium),
      decoration: BoxDecoration(
        color: DesignSystem.backgroundPrimary,
        borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.product.name,
            style: AppTypography.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          if (widget.product.description != null) ...[
            SizedBox(height: DesignSystem.spacingSmall),
            Text(
              widget.product.description!,
              style: AppTypography.bodySmall.copyWith(
                color: DesignSystem.textSecondary,
              ),
            ),
          ],
          if (widget.product.isProduct) ...[
            SizedBox(height: DesignSystem.spacingSmall),
            Row(
              children: [
                Text(
                  'Current Stock: ',
                  style: AppTypography.bodySmall.copyWith(
                    color: DesignSystem.textSecondary,
                  ),
                ),
                Text(
                  '${widget.product.quantity ?? 0}',
                  style: AppTypography.bodySmall.copyWith(
                    color: DesignSystem.primaryTeal,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuantityField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quantity to Purchase',
          style: AppTypography.labelMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: DesignSystem.spacingSmall),
        TextFormField(
          controller: _quantityController,
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          decoration: InputDecoration(
            hintText: '1',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter quantity';
            }
            final quantity = int.tryParse(value);
            if (quantity == null || quantity <= 0) {
              return 'Please enter a valid quantity';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildPriceField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Unit Purchase Price',
          style: AppTypography.labelMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: DesignSystem.spacingSmall),
        TextFormField(
          controller: _unitPriceController,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          decoration: InputDecoration(
            hintText: '0.00',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter unit price';
            }
            final price = double.tryParse(value);
            if (price == null || price <= 0) {
              return 'Please enter a valid price';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildSupplierInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Supplier Information (Optional)',
          style: AppTypography.labelMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: DesignSystem.spacingSmall),
        TextFormField(
          controller: _supplierNameController,
          decoration: InputDecoration(
            hintText: 'Supplier Name',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
            ),
          ),
        ),
        SizedBox(height: DesignSystem.spacingSmall),
        TextFormField(
          controller: _supplierContactController,
          decoration: InputDecoration(
            hintText: 'Phone/Email',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNotesField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notes (Optional)',
          style: AppTypography.labelMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: DesignSystem.spacingSmall),
        TextFormField(
          controller: _notesController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'Purchase notes...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTotalSection() {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacingMedium),
      decoration: BoxDecoration(
        color: DesignSystem.primaryTeal.withOpacity(0.1),
        borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Total Cost',
            style: AppTypography.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          Obx(() {
            final currencyService = CurrencyService.to;
            return Text(
              currencyService.currentCurrency.value.formatAmount(_totalAmount),
              style: AppTypography.titleLarge.copyWith(
                color: DesignSystem.primaryTeal,
                fontWeight: FontWeight.bold,
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ),
        SizedBox(width: DesignSystem.spacingMedium),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _completePurchase,
            style: ElevatedButton.styleFrom(
              backgroundColor: DesignSystem.primaryTeal,
              foregroundColor: DesignSystem.white,
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Complete Purchase'),
          ),
        ),
      ],
    );
  }

  Future<void> _completePurchase() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final quantity = int.parse(_quantityController.text);
      final unitPrice = double.parse(_unitPriceController.text);
      
      final transaction = BusinessTransaction(
        id: const Uuid().v4(),
        accountId: widget.product.accountId,
        productId: widget.product.id,
        product: widget.product,
        quantity: quantity,
        unitPrice: unitPrice,
        totalAmount: _totalAmount,
        type: BusinessTransactionType.purchase,
        customerName: _supplierNameController.text.trim().isEmpty 
            ? null 
            : _supplierNameController.text.trim(),
        customerContact: _supplierContactController.text.trim().isEmpty 
            ? null 
            : _supplierContactController.text.trim(),
        notes: _notesController.text.trim().isEmpty 
            ? null 
            : _notesController.text.trim(),
        transactionDate: DateTime.now(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final productService = Get.find<BusinessProductService>();
      await productService.addTransaction(transaction);

      widget.onPurchaseCompleted(transaction);
      Navigator.of(context).pop();
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to complete purchase: ${e.toString()}',
        backgroundColor: DesignSystem.secondaryCoral,
        colorText: DesignSystem.white,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
