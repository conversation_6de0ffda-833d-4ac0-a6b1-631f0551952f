import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';
import '../../../core/models/business_product.dart';
import '../../../core/services/business_product_service.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/constants/app_colors.dart';

class AddProductScreen extends StatefulWidget {
  final BusinessProduct? product;
  final ProductType? type;

  const AddProductScreen({
    super.key,
    this.product,
    this.type,
  });

  @override
  State<AddProductScreen> createState() => _AddProductScreenState();
}

class _AddProductScreenState extends State<AddProductScreen> {
  final _formKey = GlobalKey<FormState>();
  final _productService = BusinessProductService.to;
  
  // Controllers
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _costPriceController = TextEditingController();
  final _quantityController = TextEditingController();
  final _minStockController = TextEditingController();
  final _skuController = TextEditingController();
  final _barcodeController = TextEditingController();
  final _categoryController = TextEditingController();

  // State
  ProductType _selectedType = ProductType.product;
  ProductStatus _selectedStatus = ProductStatus.active;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedType = widget.type ?? ProductType.product;
    
    if (widget.product != null) {
      _populateFields();
    }
  }

  void _populateFields() {
    final product = widget.product!;
    _nameController.text = product.name;
    _descriptionController.text = product.description ?? '';
    _priceController.text = product.price.toString();
    _costPriceController.text = product.costPrice?.toString() ?? '';
    _quantityController.text = product.quantity?.toString() ?? '';
    _minStockController.text = product.minStockLevel?.toString() ?? '';
    _skuController.text = product.sku ?? '';
    _barcodeController.text = product.barcode ?? '';
    _categoryController.text = product.category ?? '';
    _selectedType = product.type;
    _selectedStatus = product.status;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _costPriceController.dispose();
    _quantityController.dispose();
    _minStockController.dispose();
    _skuController.dispose();
    _barcodeController.dispose();
    _categoryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          widget.product != null 
              ? 'Edit ${_selectedType.name.capitalize}' 
              : 'Add ${_selectedType.name.capitalize}',
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          if (widget.product != null)
            IconButton(
              onPressed: _deleteProduct,
              icon: const Icon(Icons.delete_rounded),
              color: AppColors.error,
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Type Selection
              _buildTypeSelector(),
              const SizedBox(height: 24),
              
              // Basic Information
              _buildBasicInfoSection(),
              const SizedBox(height: 24),
              
              // Inventory Information (only for products)
              if (_selectedType == ProductType.product) ...[
                _buildInventorySection(),
                const SizedBox(height: 24),
              ],
              
              // Additional Information
              _buildAdditionalInfoSection(),
              const SizedBox(height: 32),
              
              // Save Button
              _buildSaveButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTypeSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Type',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildTypeOption(
                  ProductType.product,
                  Icons.inventory_2_rounded,
                  'Product',
                  'Physical items with inventory',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildTypeOption(
                  ProductType.service,
                  Icons.handyman_rounded,
                  'Service',
                  'Services without inventory',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTypeOption(ProductType type, IconData icon, String title, String subtitle) {
    final isSelected = _selectedType == type;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedType = type;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary.withOpacity(0.1) : AppColors.greyLight,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.borderLight,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? AppColors.primary : AppColors.textSecondary,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: isSelected ? AppColors.primary : AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Basic Information',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          
          // Name
          TextFormField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'Name *',
              hintText: 'Enter product/service name',
              prefixIcon: Icon(Icons.label_rounded),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter a name';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          
          // Description
          TextFormField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: 'Description',
              hintText: 'Enter description (optional)',
              prefixIcon: Icon(Icons.description_rounded),
            ),
            maxLines: 3,
          ),
          const SizedBox(height: 16),
          
          // Price
          TextFormField(
            controller: _priceController,
            decoration: const InputDecoration(
              labelText: 'Selling Price *',
              hintText: 'Enter selling price',
              prefixIcon: Icon(Icons.attach_money_rounded),
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
            ],
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter a selling price';
              }
              final price = double.tryParse(value);
              if (price == null || price <= 0) {
                return 'Please enter a valid selling price';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // Cost Price
          TextFormField(
            controller: _costPriceController,
            decoration: const InputDecoration(
              labelText: 'Cost Price (Optional)',
              hintText: 'Enter cost/purchase price',
              prefixIcon: Icon(Icons.money_off_rounded),
              helperText: 'Used for profit calculations',
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
            ],
            validator: (value) {
              if (value != null && value.trim().isNotEmpty) {
                final costPrice = double.tryParse(value);
                if (costPrice == null || costPrice < 0) {
                  return 'Please enter a valid cost price';
                }
                // Check if cost price is higher than selling price
                final sellingPrice = double.tryParse(_priceController.text);
                if (sellingPrice != null && costPrice > sellingPrice) {
                  return 'Cost price should not exceed selling price';
                }
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInventorySection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Inventory Information',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              // Quantity
              Expanded(
                child: TextFormField(
                  controller: _quantityController,
                  decoration: const InputDecoration(
                    labelText: 'Quantity',
                    hintText: 'Current stock',
                    prefixIcon: Icon(Icons.inventory_rounded),
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                ),
              ),
              const SizedBox(width: 16),
              
              // Min Stock Level
              Expanded(
                child: TextFormField(
                  controller: _minStockController,
                  decoration: const InputDecoration(
                    labelText: 'Min Stock',
                    hintText: 'Alert level',
                    prefixIcon: Icon(Icons.warning_rounded),
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Additional Information',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          
          // SKU and Barcode
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _skuController,
                  decoration: const InputDecoration(
                    labelText: 'SKU',
                    hintText: 'Stock keeping unit',
                    prefixIcon: Icon(Icons.qr_code_rounded),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _barcodeController,
                  decoration: const InputDecoration(
                    labelText: 'Barcode',
                    hintText: 'Product barcode',
                    prefixIcon: Icon(Icons.qr_code_scanner_rounded),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Category
          TextFormField(
            controller: _categoryController,
            decoration: const InputDecoration(
              labelText: 'Category',
              hintText: 'Product category',
              prefixIcon: Icon(Icons.category_rounded),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveProduct,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                widget.product != null ? 'Update ${_selectedType.name.capitalize}' : 'Add ${_selectedType.name.capitalize}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final now = DateTime.now();
      final product = BusinessProduct(
        id: widget.product?.id ?? const Uuid().v4(),
        accountId: _productService.currentAccountId,
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        type: _selectedType,
        price: double.parse(_priceController.text),
        costPrice: _costPriceController.text.trim().isNotEmpty
            ? double.parse(_costPriceController.text)
            : null,
        currency: 'USD', // TODO: Get from currency service
        quantity: _selectedType == ProductType.product && _quantityController.text.isNotEmpty
            ? int.parse(_quantityController.text)
            : null,
        minStockLevel: _selectedType == ProductType.product && _minStockController.text.isNotEmpty
            ? int.parse(_minStockController.text)
            : null,
        sku: _skuController.text.trim().isEmpty ? null : _skuController.text.trim(),
        barcode: _barcodeController.text.trim().isEmpty ? null : _barcodeController.text.trim(),
        status: _selectedStatus,
        category: _categoryController.text.trim().isEmpty ? null : _categoryController.text.trim(),
        imageUrl: widget.product?.imageUrl,
        metadata: widget.product?.metadata,
        createdAt: widget.product?.createdAt ?? now,
        updatedAt: now,
      );

      if (widget.product != null) {
        await _productService.updateProduct(product);
      } else {
        await _productService.addProduct(product);
      }

      Get.back();
      Get.snackbar(
        'Success',
        '${_selectedType.name.capitalize} ${widget.product != null ? 'updated' : 'added'} successfully',
        backgroundColor: AppColors.success,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to save ${_selectedType.name}: ${e.toString()}',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteProduct() async {
    if (widget.product == null) return;

    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('Delete Product'),
        content: Text('Are you sure you want to delete "${widget.product!.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _productService.deleteProduct(widget.product!.id);
        Get.back();
        Get.snackbar(
          'Success',
          'Product deleted successfully',
          backgroundColor: AppColors.success,
          colorText: Colors.white,
        );
      } catch (e) {
        Get.snackbar(
          'Error',
          'Failed to delete product: ${e.toString()}',
          backgroundColor: AppColors.error,
          colorText: Colors.white,
        );
      }
    }
  }
}
