import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';
import '../../../core/models/business_product.dart';
import '../../../core/models/business_transaction.dart';
import '../../../core/services/business_product_service.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/theme/typography.dart';

class EnhancedPurchaseScreen extends StatefulWidget {
  const EnhancedPurchaseScreen({super.key});

  @override
  State<EnhancedPurchaseScreen> createState() => _EnhancedPurchaseScreenState();
}

class _EnhancedPurchaseScreenState extends State<EnhancedPurchaseScreen> {
  final _productService = BusinessProductService.to;
  final _searchController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  
  // Form controllers for new product
  final _nameController = TextEditingController();
  final _costPriceController = TextEditingController();
  final _quantityController = TextEditingController(text: '1');
  final _supplierController = TextEditingController();
  final _notesController = TextEditingController();
  
  BusinessProduct? _selectedProduct;
  bool _isCreatingNew = false;
  bool _isLoading = false;
  ProductType _selectedType = ProductType.product;
  
  List<BusinessProduct> get _filteredProducts {
    final searchTerm = _searchController.text.toLowerCase();
    var products = _productService.getProductsByType(_selectedType);
    
    if (searchTerm.isNotEmpty) {
      products = products.where((product) =>
          product.name.toLowerCase().contains(searchTerm) ||
          (product.description?.toLowerCase().contains(searchTerm) ?? false) ||
          (product.sku?.toLowerCase().contains(searchTerm) ?? false)
      ).toList();
    }
    
    return products;
  }

  @override
  void dispose() {
    _searchController.dispose();
    _nameController.dispose();
    _costPriceController.dispose();
    _quantityController.dispose();
    _supplierController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Purchase Items'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            // Type selector
            _buildTypeSelector(),
            
            // Search or create toggle
            _buildSearchOrCreateToggle(),
            
            if (!_isCreatingNew) ...[
              // Search bar
              _buildSearchBar(),
              
              // Product list
              Expanded(child: _buildProductList()),
            ] else ...[
              // New product form
              Expanded(child: _buildNewProductForm()),
            ],
            
            // Purchase button
            if (_selectedProduct != null || _isCreatingNew)
              _buildPurchaseButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeSelector() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: DesignSystem.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: DesignSystem.shadowSmall,
      ),
      child: Row(
        children: ProductType.values.map((type) {
          final isSelected = _selectedType == type;
          return Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedType = type;
                  _selectedProduct = null;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected ? DesignSystem.primaryTeal : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  type.name.capitalize!,
                  textAlign: TextAlign.center,
                  style: AppTypography.bodyMedium.copyWith(
                    color: isSelected ? DesignSystem.white : DesignSystem.textPrimary,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildSearchOrCreateToggle() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () {
                setState(() {
                  _isCreatingNew = false;
                  _selectedProduct = null;
                });
              },
              icon: Icon(
                Icons.search,
                color: !_isCreatingNew ? DesignSystem.primaryTeal : DesignSystem.textSecondary,
              ),
              label: Text(
                'Select Existing',
                style: TextStyle(
                  color: !_isCreatingNew ? DesignSystem.primaryTeal : DesignSystem.textSecondary,
                ),
              ),
              style: OutlinedButton.styleFrom(
                side: BorderSide(
                  color: !_isCreatingNew ? DesignSystem.primaryTeal : DesignSystem.textSecondary,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () {
                setState(() {
                  _isCreatingNew = true;
                  _selectedProduct = null;
                });
              },
              icon: Icon(
                Icons.add,
                color: _isCreatingNew ? DesignSystem.primaryTeal : DesignSystem.textSecondary,
              ),
              label: Text(
                'Create New',
                style: TextStyle(
                  color: _isCreatingNew ? DesignSystem.primaryTeal : DesignSystem.textSecondary,
                ),
              ),
              style: OutlinedButton.styleFrom(
                side: BorderSide(
                  color: _isCreatingNew ? DesignSystem.primaryTeal : DesignSystem.textSecondary,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search ${_selectedType.name}s...',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: DesignSystem.white,
        ),
        onChanged: (value) => setState(() {}),
      ),
    );
  }

  Widget _buildProductList() {
    final products = _filteredProducts;
    
    if (products.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: DesignSystem.textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'No ${_selectedType.name}s found',
              style: AppTypography.titleMedium.copyWith(
                color: DesignSystem.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try creating a new ${_selectedType.name}',
              style: AppTypography.bodyMedium.copyWith(
                color: DesignSystem.textSecondary,
              ),
            ),
          ],
        ),
      );
    }
    
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: products.length,
      itemBuilder: (context, index) {
        final product = products[index];
        final isSelected = _selectedProduct?.id == product.id;
        
        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedProduct = isSelected ? null : product;
              if (_selectedProduct != null) {
                _costPriceController.text = _selectedProduct!.actualCostPrice.toStringAsFixed(2);
              }
            });
          },
          child: Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isSelected ? DesignSystem.primaryTeal.withOpacity(0.1) : DesignSystem.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected ? DesignSystem.primaryTeal : DesignSystem.borderLight,
                width: isSelected ? 2 : 1,
              ),
              boxShadow: DesignSystem.shadowSmall,
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        product.name,
                        style: AppTypography.titleSmall.copyWith(
                          fontWeight: FontWeight.w600,
                          color: isSelected ? DesignSystem.primaryTeal : DesignSystem.textPrimary,
                        ),
                      ),
                      if (product.description != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          product.description!,
                          style: AppTypography.bodySmall.copyWith(
                            color: DesignSystem.textSecondary,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Text(
                            'Price: \$${product.price.toStringAsFixed(2)}',
                            style: AppTypography.bodySmall.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          if (product.isProduct && product.quantity != null) ...[
                            const SizedBox(width: 16),
                            Text(
                              'Stock: ${product.quantity}',
                              style: AppTypography.bodySmall.copyWith(
                                color: product.isLowStock ? DesignSystem.secondaryCoral : DesignSystem.textSecondary,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: DesignSystem.primaryTeal,
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildNewProductForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Create New ${_selectedType.name.capitalize}',
            style: AppTypography.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          
          // Name
          TextFormField(
            controller: _nameController,
            decoration: InputDecoration(
              labelText: '${_selectedType.name.capitalize} Name *',
              hintText: 'Enter ${_selectedType.name} name',
              prefixIcon: const Icon(Icons.label_outline),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter a ${_selectedType.name} name';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          
          // Cost Price
          TextFormField(
            controller: _costPriceController,
            decoration: InputDecoration(
              labelText: 'Cost Price *',
              hintText: 'Enter cost/purchase price',
              prefixIcon: const Icon(Icons.attach_money),
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
            ],
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter a cost price';
              }
              final price = double.tryParse(value);
              if (price == null || price <= 0) {
                return 'Please enter a valid cost price';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          
          // Quantity (for products only)
          if (_selectedType == ProductType.product)
            TextFormField(
              controller: _quantityController,
              decoration: const InputDecoration(
                labelText: 'Quantity *',
                hintText: 'Enter quantity to purchase',
                prefixIcon: Icon(Icons.inventory),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter quantity';
                }
                final quantity = int.tryParse(value);
                if (quantity == null || quantity <= 0) {
                  return 'Please enter a valid quantity';
                }
                return null;
              },
            ),
          
          const SizedBox(height: 16),
          
          // Supplier
          TextFormField(
            controller: _supplierController,
            decoration: const InputDecoration(
              labelText: 'Supplier (Optional)',
              hintText: 'Enter supplier name',
              prefixIcon: Icon(Icons.business),
            ),
          ),
          const SizedBox(height: 16),
          
          // Notes
          TextFormField(
            controller: _notesController,
            decoration: const InputDecoration(
              labelText: 'Notes (Optional)',
              hintText: 'Enter any additional notes',
              prefixIcon: Icon(Icons.note),
            ),
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildPurchaseButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: _isLoading ? null : _completePurchase,
          style: ElevatedButton.styleFrom(
            backgroundColor: DesignSystem.primaryTeal,
            foregroundColor: DesignSystem.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: _isLoading
              ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Text(
                  _isCreatingNew 
                      ? 'Create & Purchase' 
                      : 'Complete Purchase',
                  style: AppTypography.titleSmall.copyWith(
                    fontWeight: FontWeight.w600,
                    color: DesignSystem.white,
                  ),
                ),
        ),
      ),
    );
  }

  Future<void> _completePurchase() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      BusinessProduct product;
      
      if (_isCreatingNew) {
        // Create new product first
        final costPrice = double.parse(_costPriceController.text);
        product = BusinessProduct(
          id: const Uuid().v4(),
          accountId: _productService.currentAccountId,
          name: _nameController.text.trim(),
          type: _selectedType,
          price: costPrice * 1.3, // Default 30% markup
          costPrice: costPrice,
          currency: 'USD',
          quantity: _selectedType == ProductType.product 
              ? int.parse(_quantityController.text) 
              : null,
          minStockLevel: _selectedType == ProductType.product ? 5 : null,
          status: ProductStatus.active,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        await _productService.addProduct(product);
      } else {
        product = _selectedProduct!;
      }

      // Create purchase transaction
      final quantity = _selectedType == ProductType.product 
          ? int.parse(_quantityController.text)
          : 1;
      final unitPrice = double.parse(_costPriceController.text);
      
      final transaction = BusinessTransaction(
        id: const Uuid().v4(),
        accountId: product.accountId,
        productId: product.id,
        product: product,
        quantity: quantity,
        unitPrice: unitPrice,
        totalAmount: quantity * unitPrice,
        type: BusinessTransactionType.purchase,
        customerName: _supplierController.text.trim().isEmpty 
            ? null 
            : _supplierController.text.trim(),
        notes: _notesController.text.trim().isEmpty 
            ? null 
            : _notesController.text.trim(),
        transactionDate: DateTime.now(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _productService.addTransaction(transaction);

      Get.back();
      Get.snackbar(
        'Success',
        _isCreatingNew 
            ? '${product.name} created and purchased successfully'
            : 'Purchase completed successfully',
        backgroundColor: DesignSystem.success,
        colorText: DesignSystem.white,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to complete purchase: ${e.toString()}',
        backgroundColor: DesignSystem.secondaryCoral,
        colorText: DesignSystem.white,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
