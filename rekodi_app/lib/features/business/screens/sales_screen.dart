import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/models/business_product.dart';
import '../../../core/services/business_product_service.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/theme/typography.dart';
import '../widgets/product_selection_dialog.dart';

class SalesScreen extends StatefulWidget {
  const SalesScreen({Key? key}) : super(key: key);

  @override
  State<SalesScreen> createState() => _SalesScreenState();
}

class _SalesScreenState extends State<SalesScreen>
    with TickerProviderStateMixin {
  final BusinessProductService _productService = Get.find();
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  ProductType _selectedType = ProductType.product;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _selectedType = _tabController.index == 0 ? ProductType.product : ProductType.service;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  List<BusinessProduct> get _filteredItems {
    var items = _productService.filterProducts(
      type: _selectedType,
      status: ProductStatus.active,
    );

    final searchTerm = _searchController.text.toLowerCase();
    if (searchTerm.isNotEmpty) {
      items = items.where((item) =>
          item.name.toLowerCase().contains(searchTerm) ||
          (item.description?.toLowerCase().contains(searchTerm) ?? false) ||
          (item.sku?.toLowerCase().contains(searchTerm) ?? false)
      ).toList();
    }

    return items;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignSystem.backgroundColor,
      appBar: AppBar(
        title: const Text('Sales'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(
              icon: Icon(Icons.inventory_2_rounded),
              text: 'Products',
            ),
            Tab(
              icon: Icon(Icons.design_services_rounded),
              text: 'Services',
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            padding: EdgeInsets.all(DesignSystem.spacingMedium),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search ${_selectedType.name}s to sell...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: DesignSystem.white,
              ),
              onChanged: (value) => setState(() {}),
            ),
          ),
          
          // Items Grid
          Expanded(
            child: Obx(() {
              final items = _filteredItems;
              
              if (items.isEmpty) {
                return _buildEmptyState();
              }
              
              return GridView.builder(
                padding: EdgeInsets.all(DesignSystem.spacingMedium),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 0.8,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                ),
                itemCount: items.length,
                itemBuilder: (context, index) {
                  final item = items[index];
                  return _buildItemCard(item);
                },
              );
            }),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          // Navigate to add product/service
          Get.toNamed('/add-product', arguments: {'type': _selectedType});
        },
        icon: const Icon(Icons.add),
        label: Text('Add ${_selectedType.name.capitalize}'),
        backgroundColor: DesignSystem.primaryTeal,
      ),
    );
  }

  Widget _buildItemCard(BusinessProduct item) {
    final isOutOfStock = item.isProduct && (item.quantity ?? 0) <= 0;
    
    return GestureDetector(
      onTap: isOutOfStock ? null : () => _showSaleDialog(item),
      child: Container(
        decoration: BoxDecoration(
          color: DesignSystem.white,
          borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
          boxShadow: DesignSystem.shadowSmall,
          border: isOutOfStock 
              ? Border.all(color: DesignSystem.secondaryCoral.withOpacity(0.3))
              : null,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image placeholder
            Container(
              height: 100,
              decoration: BoxDecoration(
                color: DesignSystem.backgroundPrimary,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(DesignSystem.radiusMedium),
                  topRight: Radius.circular(DesignSystem.radiusMedium),
                ),
              ),
              child: Center(
                child: Icon(
                  item.isProduct 
                      ? Icons.inventory_2_rounded
                      : Icons.design_services_rounded,
                  size: 40,
                  color: DesignSystem.textSecondary,
                ),
              ),
            ),
            
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(DesignSystem.spacingSmall),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.name,
                      style: AppTypography.titleSmall.copyWith(
                        color: isOutOfStock 
                            ? DesignSystem.textSecondary
                            : DesignSystem.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    if (item.isProduct) ...[
                      SizedBox(height: DesignSystem.spacingSmall),
                      Text(
                        'Stock: ${item.quantity ?? 0}',
                        style: AppTypography.bodySmall.copyWith(
                          color: isOutOfStock 
                              ? DesignSystem.secondaryCoral
                              : DesignSystem.textSecondary,
                          fontWeight: isOutOfStock ? FontWeight.w600 : FontWeight.normal,
                        ),
                      ),
                    ],
                    
                    const Spacer(),
                    
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '\$${item.price.toStringAsFixed(2)}',
                          style: AppTypography.titleMedium.copyWith(
                            color: DesignSystem.primaryTeal,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (isOutOfStock)
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: DesignSystem.spacingSmall,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: DesignSystem.secondaryCoral.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(DesignSystem.radiusSmall),
                            ),
                            child: Text(
                              'Out of Stock',
                              style: AppTypography.bodySmall.copyWith(
                                color: DesignSystem.secondaryCoral,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _selectedType == ProductType.product 
                ? Icons.inventory_2_rounded
                : Icons.design_services_rounded,
            size: 64,
            color: DesignSystem.textSecondary,
          ),
          SizedBox(height: DesignSystem.spacingMedium),
          Text(
            'No ${_selectedType.name}s available',
            style: AppTypography.titleMedium.copyWith(
              color: DesignSystem.textSecondary,
            ),
          ),
          SizedBox(height: DesignSystem.spacingSmall),
          Text(
            'Add ${_selectedType.name}s to start selling',
            style: AppTypography.bodyMedium.copyWith(
              color: DesignSystem.textSecondary,
            ),
          ),
          SizedBox(height: DesignSystem.spacingLarge),
          ElevatedButton.icon(
            onPressed: () {
              Get.toNamed('/add-product', arguments: {'type': _selectedType});
            },
            icon: const Icon(Icons.add),
            label: Text('Add ${_selectedType.name.capitalize}'),
            style: ElevatedButton.styleFrom(
              backgroundColor: DesignSystem.primaryTeal,
              foregroundColor: DesignSystem.white,
            ),
          ),
        ],
      ),
    );
  }

  void _showSaleDialog(BusinessProduct item) {
    showDialog(
      context: context,
      builder: (context) => ProductSelectionDialog(
        product: item,
        onSaleCompleted: (transaction) {
          // Handle sale completion
          Get.snackbar(
            'Sale Completed',
            'Successfully sold ${transaction.quantity} ${item.name}(s)',
            backgroundColor: DesignSystem.success,
            colorText: DesignSystem.white,
          );
        },
      ),
    );
  }
}
