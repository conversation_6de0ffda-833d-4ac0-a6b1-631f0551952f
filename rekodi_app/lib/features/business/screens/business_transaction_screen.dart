import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/payment_methods.dart' as pm;
import '../../../core/services/theme_service.dart';
import '../../../core/services/account_service.dart';
import '../../../core/services/business_product_service.dart';
import '../../../core/models/business_product.dart';
import '../../../core/models/business_transaction.dart' as bt;
import '../../../core/models/account_type.dart';
import '../../common/widgets/common_app_bar.dart';
import '../../common/widgets/payment_method_selector.dart';

class BusinessTransactionScreen extends StatefulWidget {
  final String transactionType; // 'sale' or 'purchase'
  
  const BusinessTransactionScreen({
    super.key,
    required this.transactionType,
  });

  @override
  State<BusinessTransactionScreen> createState() => _BusinessTransactionScreenState();
}

class _BusinessTransactionScreenState extends State<BusinessTransactionScreen> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _customerNameController = TextEditingController();
  final _customerContactController = TextEditingController();
  final _notesController = TextEditingController();
  final _discountController = TextEditingController();
  
  late TabController _tabController;
  DateTime _selectedDate = DateTime.now();
  pm.PaymentMethod? _selectedPaymentMethod;
  bool _isLoading = false;
  
  // Selected items for the transaction
  final List<BusinessTransactionItem> _selectedItems = [];
  double _totalAmount = 0.0;
  double _discountAmount = 0.0;
  double _finalAmount = 0.0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _calculateTotals();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _customerNameController.dispose();
    _customerContactController.dispose();
    _notesController.dispose();
    _discountController.dispose();
    super.dispose();
  }

  void _calculateTotals() {
    _totalAmount = _selectedItems.fold(0.0, (sum, item) => sum + item.totalPrice);
    _discountAmount = double.tryParse(_discountController.text) ?? 0.0;
    _finalAmount = _totalAmount - _discountAmount;
    if (_finalAmount < 0) _finalAmount = 0.0;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final themeService = ThemeService.to;
      final isDark = themeService.isDarkMode;
      
      return Scaffold(
        backgroundColor: themeService.backgroundColor,
        appBar: CommonAppBar(
          title: widget.transactionType == 'sale' ? 'New Sale' : 'New Purchase',
          showAccountInfo: true,
          actions: [
            TextButton(
              onPressed: _isLoading ? null : _saveTransaction,
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Save'),
            ),
          ],
        ),
        body: Form(
          key: _formKey,
          child: Column(
            children: [
              // Tab Bar for Products/Services
              Container(
                color: isDark ? AppColors.surfaceDark : Colors.white,
                child: TabBar(
                  controller: _tabController,
                  labelColor: AppColors.primary,
                  unselectedLabelColor: isDark ? Colors.grey[400] : Colors.grey[600],
                  indicatorColor: AppColors.primary,
                  tabs: const [
                    Tab(text: 'Products', icon: Icon(Icons.inventory_2_rounded)),
                    Tab(text: 'Services', icon: Icon(Icons.design_services_rounded)),
                  ],
                ),
              ),
              
              // Tab Content
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildProductsTab(),
                    _buildServicesTab(),
                  ],
                ),
              ),
              
              // Transaction Summary and Details
              _buildTransactionSummary(),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildProductsTab() {
    return Obx(() {
      final businessService = BusinessProductService.to;
      final products = businessService.products.where((p) => p.isProduct).toList();
      
      if (products.isEmpty) {
        return _buildEmptyState('No products available', 'Add products to your inventory first');
      }
      
      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: products.length,
        itemBuilder: (context, index) {
          final product = products[index];
          return _buildProductCard(product);
        },
      );
    });
  }

  Widget _buildServicesTab() {
    return Obx(() {
      final businessService = BusinessProductService.to;
      final services = businessService.products.where((p) => p.isService).toList();
      
      if (services.isEmpty) {
        return _buildEmptyState('No services available', 'Add services to your business first');
      }
      
      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: services.length,
        itemBuilder: (context, index) {
          final service = services[index];
          return _buildServiceCard(service);
        },
      );
    });
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // Navigate to add product/service
              Get.toNamed('/add-product');
            },
            icon: const Icon(Icons.add),
            label: const Text('Add Items'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductCard(BusinessProduct product) {
    final isSelected = _selectedItems.any((item) => item.product.id == product.id);
    final selectedItem = isSelected ? _selectedItems.firstWhere((item) => item.product.id == product.id) : null;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.primary.withOpacity(0.1),
          child: Icon(
            Icons.inventory_2_rounded,
            color: AppColors.primary,
          ),
        ),
        title: Text(
          product.name,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Price: \$${product.price.toStringAsFixed(2)}'),
            if (product.quantity != null)
              Text('Stock: ${product.quantity} ${product.unit ?? 'units'}'),
          ],
        ),
        trailing: isSelected
            ? Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    onPressed: () => _decreaseQuantity(product),
                    icon: const Icon(Icons.remove_circle_outline),
                  ),
                  Text('${selectedItem!.quantity}'),
                  IconButton(
                    onPressed: () => _increaseQuantity(product),
                    icon: const Icon(Icons.add_circle_outline),
                  ),
                ],
              )
            : IconButton(
                onPressed: () => _addProduct(product),
                icon: const Icon(Icons.add_circle),
                color: AppColors.primary,
              ),
      ),
    );
  }

  Widget _buildServiceCard(BusinessProduct service) {
    final isSelected = _selectedItems.any((item) => item.product.id == service.id);
    final selectedItem = isSelected ? _selectedItems.firstWhere((item) => item.product.id == service.id) : null;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.secondary.withOpacity(0.1),
          child: Icon(
            Icons.design_services_rounded,
            color: AppColors.secondary,
          ),
        ),
        title: Text(
          service.name,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Text('Price: \$${service.price.toStringAsFixed(2)}'),
        trailing: isSelected
            ? Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    onPressed: () => _decreaseQuantity(service),
                    icon: const Icon(Icons.remove_circle_outline),
                  ),
                  Text('${selectedItem!.quantity}'),
                  IconButton(
                    onPressed: () => _increaseQuantity(service),
                    icon: const Icon(Icons.add_circle_outline),
                  ),
                ],
              )
            : IconButton(
                onPressed: () => _addService(service),
                icon: const Icon(Icons.add_circle),
                color: AppColors.secondary,
              ),
      ),
    );
  }

  Widget _buildTransactionSummary() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeService.to.isDarkMode ? AppColors.surfaceDark : Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Selected Items Summary
          if (_selectedItems.isNotEmpty) ...[
            Text(
              'Selected Items (${_selectedItems.length})',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 100,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedItems.length,
                itemBuilder: (context, index) {
                  final item = _selectedItems[index];
                  return Container(
                    width: 120,
                    margin: const EdgeInsets.only(right: 8),
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: AppColors.primary.withOpacity(0.3),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item.product.name,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Qty: ${item.quantity}',
                          style: const TextStyle(fontSize: 11),
                        ),
                        Text(
                          '\$${item.totalPrice.toStringAsFixed(2)}',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: AppColors.primary,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
          ],
          
          // Transaction Details Form
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _customerNameController,
                  decoration: const InputDecoration(
                    labelText: 'Customer Name (Optional)',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: TextFormField(
                  controller: _discountController,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: 'Discount (\$)',
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (_) => _calculateTotals(),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Payment Method
          PaymentMethodSelector(
            label: 'Payment Method',
            initialPaymentMethod: _selectedPaymentMethod,
            onPaymentMethodChanged: (pm.PaymentMethod? paymentMethod) {
              setState(() {
                _selectedPaymentMethod = paymentMethod;
              });
            },
            isRequired: true,
          ),
          
          const SizedBox(height: 16),
          
          // Totals
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('Subtotal:'),
                    Text('\$${_totalAmount.toStringAsFixed(2)}'),
                  ],
                ),
                if (_discountAmount > 0) ...[
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Discount:'),
                      Text('-\$${_discountAmount.toStringAsFixed(2)}'),
                    ],
                  ),
                ],
                const Divider(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Total:',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                    Text(
                      '\$${_finalAmount.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _addProduct(BusinessProduct product) {
    // Check stock availability for sales
    if (widget.transactionType == 'sale' && product.quantity != null && product.quantity! <= 0) {
      Get.snackbar(
        'Out of Stock',
        '${product.name} is currently out of stock',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
      );
      return;
    }
    
    setState(() {
      _selectedItems.add(BusinessTransactionItem(
        product: product,
        quantity: 1,
        unitPrice: product.price,
      ));
    });
    _calculateTotals();
  }

  void _addService(BusinessProduct service) {
    setState(() {
      _selectedItems.add(BusinessTransactionItem(
        product: service,
        quantity: 1,
        unitPrice: service.price,
      ));
    });
    _calculateTotals();
  }

  void _increaseQuantity(BusinessProduct product) {
    final item = _selectedItems.firstWhere((item) => item.product.id == product.id);
    
    // Check stock availability for sales
    if (widget.transactionType == 'sale' && product.quantity != null && 
        item.quantity >= product.quantity!) {
      Get.snackbar(
        'Stock Limit',
        'Cannot exceed available stock of ${product.quantity}',
        backgroundColor: AppColors.warning,
        colorText: Colors.white,
      );
      return;
    }
    
    setState(() {
      item.quantity++;
    });
    _calculateTotals();
  }

  void _decreaseQuantity(BusinessProduct product) {
    final item = _selectedItems.firstWhere((item) => item.product.id == product.id);
    
    if (item.quantity > 1) {
      setState(() {
        item.quantity--;
      });
    } else {
      setState(() {
        _selectedItems.removeWhere((item) => item.product.id == product.id);
      });
    }
    _calculateTotals();
  }

  Future<void> _saveTransaction() async {
    if (_selectedItems.isEmpty) {
      Get.snackbar(
        'No Items Selected',
        'Please select at least one product or service',
        backgroundColor: AppColors.warning,
        colorText: Colors.white,
      );
      return;
    }

    if (_selectedPaymentMethod == null) {
      Get.snackbar(
        'Payment Method Required',
        'Please select a payment method',
        backgroundColor: AppColors.warning,
        colorText: Colors.white,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Create business transactions for each item
      bool allSuccess = true;
      
      for (final item in _selectedItems) {
        final transaction = bt.BusinessTransaction(
          id: 'btxn_${DateTime.now().millisecondsSinceEpoch}_${item.product.id}',
          accountId: AccountService.to.currentAccountId,
          productId: item.product.id,
          product: item.product,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalAmount: item.totalPrice,
          type: widget.transactionType == 'sale'
              ? bt.BusinessTransactionType.sale
              : bt.BusinessTransactionType.purchase,
          customerName: _customerNameController.text.trim().isEmpty 
              ? null 
              : _customerNameController.text.trim(),
          customerContact: _customerContactController.text.trim().isEmpty 
              ? null 
              : _customerContactController.text.trim(),
          notes: _notesController.text.trim().isEmpty
              ? null
              : _notesController.text.trim(),
          paymentMethod: _selectedPaymentMethod?.toStorageString(),
          discountAmount: _discountAmount > 0 ? _discountAmount : null,
          transactionDate: _selectedDate,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        try {
          await BusinessProductService.to.addTransaction(transaction);
        } catch (e) {
          print('Error adding transaction for ${item.product.name}: $e');
          allSuccess = false;
        }
      }

      if (allSuccess) {
        Get.back();
        Get.snackbar(
          'Success',
          '${widget.transactionType.capitalizeFirst} recorded successfully',
          backgroundColor: AppColors.success,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          'Partial Success',
          'Some items were not recorded. Please check and try again.',
          backgroundColor: AppColors.warning,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to record ${widget.transactionType}: $e',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}

/// Helper class for transaction items
class BusinessTransactionItem {
  final BusinessProduct product;
  int quantity;
  final double unitPrice;

  BusinessTransactionItem({
    required this.product,
    required this.quantity,
    required this.unitPrice,
  });

  double get totalPrice => quantity * unitPrice;
}
