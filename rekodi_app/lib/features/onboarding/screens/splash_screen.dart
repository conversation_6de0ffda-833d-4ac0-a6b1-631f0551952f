import 'package:flutter/material.dart';
import 'package:animate_do/animate_do.dart';
import 'package:get_storage/get_storage.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/theme/typography.dart';
import '../../../core/constants/app_strings.dart';
import '../../../core/services/auth_service.dart';
import '../../../core/routes/app_routes.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  final GetStorage _storage = GetStorage();

  late AnimationController _logoController;
  late AnimationController _glowController;
  late AnimationController _fadeController;

  late Animation<double> _logoScale;
  late Animation<double> _logoRotation;
  late Animation<double> _glowOpacity;
  late Animation<double> _fadeOpacity;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
    _navigateToNextScreen();
  }

  void _initializeAnimations() {
    // Logo animation controller
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // Glow animation controller
    _glowController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );

    // Fade animation controller
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    // Logo scale animation
    _logoScale = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    // Logo rotation animation
    _logoRotation = Tween<double>(
      begin: 0.0,
      end: 0.1,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.easeInOut,
    ));

    // Glow opacity animation
    _glowOpacity = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));

    // Fade opacity animation
    _fadeOpacity = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
  }

  void _startAnimations() {
    // Start logo animation
    _logoController.forward();

    // Start glow animation with repeat
    _glowController.repeat(reverse: true);

    // Start fade animation after delay
    Future.delayed(const Duration(milliseconds: 2500), () {
      if (mounted) {
        _fadeController.forward();
      }
    });
  }

  @override
  void dispose() {
    _logoController.dispose();
    _glowController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  Future<void> _navigateToNextScreen() async {
    // Wait for 3.5 seconds to show splash screen with animations
    await Future.delayed(const Duration(milliseconds: 3500));

    if (!mounted) return;

    try {
      // Check authentication status
      final authService = AuthService.to;

      if (authService.isAuthenticated) {
        // User is already logged in, go to dashboard
        Get.offAllNamed(AppRoutes.dashboard);
        return;
      }

      // Check if user has seen onboarding
      final hasSeenOnboarding = _storage.read('has_seen_onboarding') ?? false;

      if (hasSeenOnboarding) {
        // Navigate to login screen
        Get.offAllNamed(AppRoutes.login);
      } else {
        // Navigate to onboarding screen
        Get.offAllNamed(AppRoutes.onboarding);
      }
    } catch (e) {
      // If there's an error, go to login screen
      Get.offAllNamed(AppRoutes.login);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _fadeController,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeOpacity.value,
          child: Scaffold(
            backgroundColor: DesignSystem.neutralBackground,
            body: Container(
              decoration: const BoxDecoration(
                gradient: DesignSystem.primaryGradient,
              ),
              child: SafeArea(
                child: Center(
                  child: SingleChildScrollView(
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        minHeight: MediaQuery.of(context).size.height -
                                  MediaQuery.of(context).padding.top -
                                  MediaQuery.of(context).padding.bottom,
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                    // Animated Logo with Glow Effect
                    AnimatedBuilder(
                      animation: Listenable.merge([_logoController, _glowController]),
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _logoScale.value,
                          child: Transform.rotate(
                            angle: _logoRotation.value,
                            child: Stack(
                              alignment: Alignment.center,
                              children: [
                                // Glow effect
                                AnimatedBuilder(
                                  animation: _glowController,
                                  builder: (context, child) {
                                    return Container(
                                      width: 140 + (20 * _glowOpacity.value),
                                      height: 140 + (20 * _glowOpacity.value),
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        boxShadow: [
                                          BoxShadow(
                                            color: DesignSystem.primaryTeal.withOpacity(
                                              0.3 * _glowOpacity.value,
                                            ),
                                            blurRadius: 30 + (20 * _glowOpacity.value),
                                            spreadRadius: 5 + (10 * _glowOpacity.value),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                                // Main logo container
                                Container(
                                  width: 120,
                                  height: 120,
                                  decoration: BoxDecoration(
                                    color: DesignSystem.cardBackground,
                                    shape: BoxShape.circle,
                                    boxShadow: DesignSystem.shadowHigh,
                                  ),
                                  child: Center(
                                    child: ShaderMask(
                                      shaderCallback: (bounds) => DesignSystem.primaryGradient
                                          .createShader(bounds),
                                      child: const Icon(
                                        Icons.account_balance_wallet_rounded,
                                        size: 60,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),

                    const SizedBox(height: DesignSystem.spaceXL),

                    // App Name with sophisticated typography
                    FadeInUp(
                      duration: DesignSystem.animationSlow,
                      delay: const Duration(milliseconds: 500),
                      child: ShaderMask(
                        shaderCallback: (bounds) => const LinearGradient(
                          colors: [Colors.white, Colors.white70],
                        ).createShader(bounds),
                        child: Text(
                          'Rekodi',
                          style: AppTypography.displayLarge.copyWith(
                            color: Colors.white,
                            fontSize: 42,
                            fontWeight: FontWeight.w700,
                            letterSpacing: 1.5,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: DesignSystem.spaceS),

                    // Subtitle
                    FadeInUp(
                      duration: DesignSystem.animationSlow,
                      delay: const Duration(milliseconds: 800),
                      child: Text(
                        'Smartledger',
                        style: AppTypography.titleLarge.copyWith(
                          color: Colors.white.withOpacity(0.8),
                          fontWeight: FontWeight.w300,
                          letterSpacing: 3.0,
                        ),
                      ),
                    ),

                    const SizedBox(height: DesignSystem.spaceXXL),

                    // Elegant loading indicator
                    FadeInUp(
                      duration: DesignSystem.animationSlow,
                      delay: const Duration(milliseconds: 1200),
                      child: SizedBox(
                        width: 32,
                        height: 32,
                        child: CircularProgressIndicator(
                          valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                          strokeWidth: 2,
                          backgroundColor: Colors.white.withOpacity(0.2),
                        ),
                      ),
                    ),

                    const SizedBox(height: DesignSystem.spaceL),

                    // Loading text
                    FadeInUp(
                      duration: DesignSystem.animationSlow,
                      delay: const Duration(milliseconds: 1500),
                      child: Text(
                        'Initializing your financial journey...',
                        style: AppTypography.bodyMedium.copyWith(
                          color: Colors.white.withOpacity(0.7),
                          fontWeight: FontWeight.w300,
                        ),
                      ),
                    ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
