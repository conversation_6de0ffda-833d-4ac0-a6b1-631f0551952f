import 'package:flutter/material.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:animate_do/animate_do.dart';
import 'package:get_storage/get_storage.dart';
import 'package:get/get.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/theme/typography.dart';
import '../../../core/constants/app_strings.dart';
import '../../../core/routes/app_routes.dart';
import '../../../shared/widgets/gradient_button.dart';
import '../widgets/sophisticated_onboarding_page.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  final GetStorage _storage = GetStorage();
  int _currentPage = 0;

  final List<SophisticatedOnboardingData> _onboardingData = [
    SophisticatedOnboardingData(
      icon: Icons.account_balance_wallet_rounded,
      title: 'Master Your Money',
      subtitle: 'Financial Freedom Starts Here',
      description:
          'Take control of your finances with intelligent tracking, automated categorization, and real-time insights that help you make smarter financial decisions.',
      gradient: DesignSystem.primaryGradient,
      illustration: _buildFinanceIllustration(),
    ),
    SophisticatedOnboardingData(
      icon: Icons.auto_awesome_rounded,
      title: 'Smart Automation',
      subtitle: 'Let AI Do the Work',
      description:
          'Our advanced SMS parsing automatically captures and categorizes your transactions from M-Pesa, bank notifications, and more. No more manual entry.',
      gradient: DesignSystem.incomeGradient,
      illustration: _buildAutomationIllustration(),
    ),
    SophisticatedOnboardingData(
      icon: Icons.insights_rounded,
      title: 'Powerful Analytics',
      subtitle: 'Data-Driven Decisions',
      description:
          'Beautiful charts, spending patterns, and personalized insights help you understand your financial habits and optimize your money management.',
      gradient: DesignSystem.expenseGradient,
      illustration: _buildAnalyticsIllustration(),
    ),
    SophisticatedOnboardingData(
      icon: Icons.security_rounded,
      title: 'Bank-Level Security',
      subtitle: 'Your Privacy Matters',
      description:
          'End-to-end encryption, biometric authentication, and secure cloud sync ensure your financial data stays private and protected.',
      gradient: const LinearGradient(
        colors: [DesignSystem.success, Color(0xFF27AE60)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      illustration: _buildSecurityIllustration(),
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _completeOnboarding() async {
    _storage.write('has_seen_onboarding', true);

    if (!mounted) return;

    Get.offAllNamed(AppRoutes.login);
  }

  void _nextPage() {
    if (_currentPage < _onboardingData.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  void _skipOnboarding() {
    _completeOnboarding();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignSystem.neutralBackground,
      body: SafeArea(
        child:
        Column(
          children: [
            // Skip Button
            Padding(
              padding: const EdgeInsets.all(DesignSystem.spaceL),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const SizedBox(width: 60), // Spacer
                  FadeInDown(
                    duration: DesignSystem.animationMedium,
                    child: TextButton(
                      onPressed: _skipOnboarding,
                      child: Text(
                        'Skip',
                        style: AppTypography.labelLarge.copyWith(
                          color: DesignSystem.textSecondary,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Page View
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemCount: _onboardingData.length,
                itemBuilder: (context, index) {
                  return SophisticatedOnboardingPage(
                    data: _onboardingData[index],
                    pageIndex: index,
                  );
                },
              ),
            ),

            // Bottom Section
            Container(
              padding: const EdgeInsets.all(DesignSystem.spaceL),
              decoration: BoxDecoration(
                color: DesignSystem.cardBackground,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(DesignSystem.radiusXLarge),
                  topRight: Radius.circular(DesignSystem.radiusXLarge),
                ),
                boxShadow: DesignSystem.shadowMedium,
              ),
              child: Column(
                children: [
                  // Page Indicator
                  FadeInUp(
                    duration: DesignSystem.animationMedium,
                    child: SmoothPageIndicator(
                      controller: _pageController,
                      count: _onboardingData.length,
                      effect: ExpandingDotsEffect(
                        activeDotColor: DesignSystem.primaryTeal,
                        dotColor: DesignSystem.textSecondary.withOpacity(0.2),
                        dotHeight: 6,
                        dotWidth: 6,
                        expansionFactor: 4,
                        spacing: 8,
                      ),
                    ),
                  ),

                  const SizedBox(height: DesignSystem.spaceXL),

                  // Next/Get Started Button
                  FadeInUp(
                    duration: DesignSystem.animationMedium,
                    delay: const Duration(milliseconds: 200),
                    child: SizedBox(
                      width: double.infinity,
                      child: GradientButton(
                        text: _currentPage == _onboardingData.length - 1
                            ? 'Get Started'
                            : 'Next',
                        onPressed: _nextPage,
                        gradient: _onboardingData[_currentPage].gradient,
                        height: 56,
                        textStyle: AppTypography.buttonLarge,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Illustration builders
Widget _buildFinanceIllustration() {
  return Container(
    width: 200,
    height: 200,
    decoration: BoxDecoration(
      gradient: DesignSystem.primaryGradient,
      borderRadius: BorderRadius.circular(DesignSystem.radiusXLarge),
      boxShadow: DesignSystem.shadowMedium,
    ),
    child: Stack(
      alignment: Alignment.center,
      children: [
        // Background pattern
        Positioned.fill(
          child: CustomPaint(
            painter: _FinancePatternPainter(),
          ),
        ),
        // Main icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
          ),
          child: const Icon(
            Icons.account_balance_wallet_rounded,
            size: 40,
            color: Colors.white,
          ),
        ),
      ],
    ),
  );
}

Widget _buildAutomationIllustration() {
  return Container(
    width: 200,
    height: 200,
    decoration: BoxDecoration(
      gradient: DesignSystem.incomeGradient,
      borderRadius: BorderRadius.circular(DesignSystem.radiusXLarge),
      boxShadow: DesignSystem.shadowMedium,
    ),
    child: Stack(
      alignment: Alignment.center,
      children: [
        // Animated circles
        ...List.generate(
          3,
          (index) => Positioned(
            top: 50 + (index * 20.0),
            left: 50 + (index * 30.0),
            child: Container(
              width: 20 - (index * 4.0),
              height: 20 - (index * 4.0),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.3 - (index * 0.1)),
                shape: BoxShape.circle,
              ),
            ),
          ),
        ),
        // Main icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
          ),
          child: const Icon(
            Icons.auto_awesome_rounded,
            size: 40,
            color: Colors.white,
          ),
        ),
      ],
    ),
  );
}

Widget _buildAnalyticsIllustration() {
  return Container(
    width: 200,
    height: 200,
    decoration: BoxDecoration(
      gradient: DesignSystem.expenseGradient,
      borderRadius: BorderRadius.circular(DesignSystem.radiusXLarge),
      boxShadow: DesignSystem.shadowMedium,
    ),
    child: Stack(
      alignment: Alignment.center,
      children: [
        // Chart bars
        Positioned(
          bottom: 40,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: List.generate(
              5,
              (index) => Container(
                width: 12,
                height: 20 + (index * 8.0),
                margin: const EdgeInsets.symmetric(horizontal: 2),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
          ),
        ),
        // Main icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
          ),
          child: const Icon(
            Icons.insights_rounded,
            size: 40,
            color: Colors.white,
          ),
        ),
      ],
    ),
  );
}

Widget _buildSecurityIllustration() {
  return Container(
    width: 200,
    height: 200,
    decoration: BoxDecoration(
      gradient: const LinearGradient(
        colors: [DesignSystem.success, Color(0xFF27AE60)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderRadius: BorderRadius.circular(DesignSystem.radiusXLarge),
      boxShadow: DesignSystem.shadowMedium,
    ),
    child: Stack(
      alignment: Alignment.center,
      children: [
        // Security rings
        ...List.generate(
          3,
          (index) => Container(
            width: 120 + (index * 20.0),
            height: 120 + (index * 20.0),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white.withOpacity(0.2 - (index * 0.05)),
                width: 2,
              ),
            ),
          ),
        ),
        // Main icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
          ),
          child: const Icon(
            Icons.security_rounded,
            size: 40,
            color: Colors.white,
          ),
        ),
      ],
    ),
  );
}

class SophisticatedOnboardingData {
  final IconData icon;
  final String title;
  final String subtitle;
  final String description;
  final LinearGradient gradient;
  final Widget illustration;

  SophisticatedOnboardingData({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.description,
    required this.gradient,
    required this.illustration,
  });
}

class _FinancePatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    // Draw some financial pattern lines
    for (int i = 0; i < 5; i++) {
      final path = Path();
      path.moveTo(0, size.height * 0.2 + (i * 20));
      path.quadraticBezierTo(
        size.width * 0.5,
        size.height * 0.1 + (i * 15),
        size.width,
        size.height * 0.3 + (i * 25),
      );
      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
