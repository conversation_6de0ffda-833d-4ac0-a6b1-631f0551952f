import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/services/theme_service.dart';

class PersonalBottomNav extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  final VoidCallback onFabPressed;

  const PersonalBottomNav({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.onFabPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final themeService = ThemeService.to;
      
      return Stack(
        alignment: Alignment.center,
        children: [
          // Bottom Navigation Background
          Container(
            height: 80,
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: ClipPath(
              clipper: _BottomNavClipper(),
              child: Container(
                decoration: BoxDecoration(
                  color: themeService.surfaceColor,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    // Home
                    _buildNavItem(
                      icon: Icons.home_rounded,
                      label: 'Home',
                      index: 0,
                      isSelected: currentIndex == 0,
                      themeService: themeService,
                    ),
                    
                    // Income
                    _buildNavItem(
                      icon: Icons.trending_up_rounded,
                      label: 'Income',
                      index: 1,
                      isSelected: currentIndex == 1,
                      themeService: themeService,
                    ),
                    
                    // Spacer for FAB
                    const SizedBox(width: 60),
                    
                    // Expenses
                    _buildNavItem(
                      icon: Icons.trending_down_rounded,
                      label: 'Expenses',
                      index: 2,
                      isSelected: currentIndex == 2,
                      themeService: themeService,
                    ),
                    
                    // Profile
                    _buildNavItem(
                      icon: Icons.person_rounded,
                      label: 'Profile',
                      index: 3,
                      isSelected: currentIndex == 3,
                      themeService: themeService,
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // Floating Action Button
          Positioned(
            top: -10,
            child: _buildFAB(themeService),
          ),
        ],
      );
    });
  }

  Widget _buildNavItem({
    required IconData icon,
    required String label,
    required int index,
    required bool isSelected,
    required ThemeService themeService,
  }) {
    return GestureDetector(
      onTap: () => onTap(index),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isSelected 
                    ? themeService.primaryColor.withOpacity(0.1)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: isSelected 
                    ? themeService.primaryColor
                    : themeService.textSecondaryColor,
                size: 24,
              ),
            ),
            const SizedBox(height: 4),
            AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 200),
              style: TextStyle(
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                color: isSelected 
                    ? themeService.primaryColor
                    : themeService.textSecondaryColor,
              ),
              child: Text(label),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFAB(ThemeService themeService) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          colors: [
            themeService.primaryColor,
            themeService.primaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: themeService.primaryColor.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onFabPressed,
          borderRadius: BorderRadius.circular(30),
          child: Container(
            width: 60,
            height: 60,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.add_rounded,
              color: Colors.white,
              size: 28,
            ),
          ),
        ),
      ),
    );
  }
}

class _BottomNavClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    
    // Start from top-left
    path.moveTo(0, 20);
    
    // Top-left corner
    path.quadraticBezierTo(0, 0, 20, 0);
    
    // Left side to FAB cutout
    path.lineTo(size.width * 0.35, 0);
    
    // FAB cutout
    path.quadraticBezierTo(size.width * 0.4, 0, size.width * 0.4, 10);
    path.arcToPoint(
      Offset(size.width * 0.6, 10),
      radius: const Radius.circular(30),
      clockwise: false,
    );
    path.quadraticBezierTo(size.width * 0.6, 0, size.width * 0.65, 0);
    
    // Right side
    path.lineTo(size.width - 20, 0);
    
    // Top-right corner
    path.quadraticBezierTo(size.width, 0, size.width, 20);
    
    // Right side
    path.lineTo(size.width, size.height - 20);
    
    // Bottom-right corner
    path.quadraticBezierTo(size.width, size.height, size.width - 20, size.height);
    
    // Bottom side
    path.lineTo(20, size.height);
    
    // Bottom-left corner
    path.quadraticBezierTo(0, size.height, 0, size.height - 20);
    
    // Close path
    path.close();
    
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
