import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_strings.dart';
import '../../../core/services/theme_service.dart';
import '../../../core/services/scanner_service.dart';
import '../../navigation/widgets/curved_bottom_nav.dart';
import '../../transactions/screens/transactions_screen.dart';
import '../../transactions/widgets/quick_add_transaction_dialog.dart';
import '../../analytics/screens/analytics_screen.dart';
import '../../settings/screens/theme_settings_screen.dart';
import '../../common/widgets/account_drawer.dart';
import 'dashboard_screen.dart';

class MainDashboard extends StatefulWidget {
  const MainDashboard({super.key});

  @override
  State<MainDashboard> createState() => _MainDashboardState();
}

class _MainDashboardState extends State<MainDashboard> {
  int _currentIndex = 0;
  
  final List<Widget> _screens = [
    const DashboardScreen(),
    const TransactionsScreen(),
    const AnalyticsScreen(),
    const ThemeSettingsScreen(),
  ];

  final List<BottomNavigationBarItem> _bottomNavItems = [
    const BottomNavigationBarItem(
      icon: Icon(Icons.dashboard_rounded),
      activeIcon: Icon(Icons.dashboard_rounded),
      label: 'Dashboard',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.receipt_long_rounded),
      activeIcon: Icon(Icons.receipt_long_rounded),
      label: 'Transactions',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.analytics_rounded),
      activeIcon: Icon(Icons.analytics_rounded),
      label: 'Analytics',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.inventory_2_rounded),
      activeIcon: Icon(Icons.inventory_2_rounded),
      label: 'Inventory',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.person_rounded),
      activeIcon: Icon(Icons.person_rounded),
      label: 'Profile',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      endDrawer: const AccountDrawer(),
      bottomNavigationBar: CurvedBottomNav(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        onAddTransaction: () => _showAddTransactionOptions(),
        onScanReceipt: () => ScannerService.to.showScannerOptions(),
      ),
      extendBody: true,
    );
  }

  void _showAddTransactionOptions() {
    showDialog(
      context: context,
      builder: (context) => const QuickAddTransactionDialog(),
    );
  }

  void _showAdvancedTransactionOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Obx(() {
        final themeService = ThemeService.to;
        return Container(
          decoration: BoxDecoration(
            color: themeService.surfaceColor,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: themeService.borderColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 20),
              Text(
                'Quick Add Transaction',
                style: Get.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: themeService.textPrimaryColor,
                ),
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildQuickAddOption(
                    icon: Icons.add_circle_rounded,
                    label: 'Income',
                    color: themeService.incomeColor,
                    onTap: () {
                      Navigator.pop(context);
                      Get.toNamed('/add-transaction', arguments: {'isIncome': true});
                    },
                  ),
                  _buildQuickAddOption(
                    icon: Icons.remove_circle_rounded,
                    label: 'Expense',
                    color: themeService.expenseColor,
                    onTap: () {
                      Navigator.pop(context);
                      Get.toNamed('/add-transaction', arguments: {'isIncome': false});
                    },
                  ),
                  _buildQuickAddOption(
                    icon: Icons.qr_code_scanner_rounded,
                    label: 'Scan Receipt',
                    color: themeService.secondaryColor,
                    onTap: () {
                      Navigator.pop(context);
                      ScannerService.to.showScannerOptions();
                    },
                  ),
                ],
              ),
              const SizedBox(height: 20),
            ],
          ),
        );
      }),

    );
  }

  Widget _buildQuickAddOption({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: color.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Icon(
              icon,
              color: color,
              size: 30,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: Get.textTheme.labelMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: ThemeService.to.textPrimaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
