import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:animate_do/animate_do.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/theme/typography.dart';
import '../../../core/services/personal_transaction_service.dart';
import '../../../core/services/currency_service.dart';
import '../../../core/models/transaction.dart';

class SpendingTrackerCard extends StatefulWidget {
  const SpendingTrackerCard({super.key});

  @override
  State<SpendingTrackerCard> createState() => _SpendingTrackerCardState();
}

class _SpendingTrackerCardState extends State<SpendingTrackerCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    // Start animation when widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _animationController.forward();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: DesignSystem.spacingMedium),
      padding: EdgeInsets.all(DesignSystem.spacingLarge),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            DesignSystem.primaryTeal,
            DesignSystem.primarySkyBlue,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
        boxShadow: DesignSystem.shadowLarge,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          SizedBox(height: DesignSystem.spacingLarge),
          _buildSpendingProgress(),
          SizedBox(height: DesignSystem.spacingLarge),
          _buildCategoryBreakdown(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Spending Tracker',
              style: AppTypography.titleLarge.copyWith(
                color: DesignSystem.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: DesignSystem.spacingSmall),
            Text(
              'This Month',
              style: AppTypography.bodyMedium.copyWith(
                color: DesignSystem.white.withOpacity(0.8),
              ),
            ),
          ],
        ),
        Container(
          padding: EdgeInsets.all(DesignSystem.spacingSmall),
          decoration: BoxDecoration(
            color: DesignSystem.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
          ),
          child: Icon(
            Icons.analytics_rounded,
            color: DesignSystem.white,
            size: 24,
          ),
        ),
      ],
    );
  }

  Widget _buildSpendingProgress() {
    return Obx(() {
      final transactionService = PersonalTransactionService.to;
      final currencyService = CurrencyService.to;
      
      // Calculate this month's spending
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final thisMonthExpenses = transactionService.transactions
          .where((t) => 
              t.type == TransactionType.expense && 
              t.date.isAfter(startOfMonth.subtract(const Duration(days: 1))))
          .fold(0.0, (sum, t) => sum + t.amount);
      
      // Estimate monthly budget (could be from budget service)
      final estimatedBudget = thisMonthExpenses * 1.2; // 20% buffer
      final spendingPercentage = estimatedBudget > 0 
          ? (thisMonthExpenses / estimatedBudget).clamp(0.0, 1.0)
          : 0.0;
      
      return Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Spent',
                style: AppTypography.bodyMedium.copyWith(
                  color: DesignSystem.white.withOpacity(0.8),
                ),
              ),
              Text(
                currencyService.currentCurrency.value.formatAmount(thisMonthExpenses),
                style: AppTypography.titleLarge.copyWith(
                  color: DesignSystem.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: DesignSystem.spacingMedium),
          AnimatedBuilder(
            animation: _progressAnimation,
            builder: (context, child) {
              return Column(
                children: [
                  Container(
                    height: 8,
                    decoration: BoxDecoration(
                      color: DesignSystem.white.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(DesignSystem.radiusSmall),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(DesignSystem.radiusSmall),
                      child: LinearProgressIndicator(
                        value: spendingPercentage * _progressAnimation.value,
                        backgroundColor: Colors.transparent,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          spendingPercentage > 0.8 
                              ? DesignSystem.secondaryCoral
                              : DesignSystem.white,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: DesignSystem.spacingSmall),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${(spendingPercentage * 100).toStringAsFixed(0)}% of estimated budget',
                        style: AppTypography.bodySmall.copyWith(
                          color: DesignSystem.white.withOpacity(0.8),
                        ),
                      ),
                      if (spendingPercentage > 0.8)
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: DesignSystem.spacingSmall,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: DesignSystem.secondaryCoral.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(DesignSystem.radiusSmall),
                          ),
                          child: Text(
                            'High Spending',
                            style: AppTypography.bodySmall.copyWith(
                              color: DesignSystem.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              );
            },
          ),
        ],
      );
    });
  }

  Widget _buildCategoryBreakdown() {
    return Obx(() {
      final transactionService = PersonalTransactionService.to;
      final currencyService = CurrencyService.to;
      
      // Get this month's expenses by category
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final thisMonthExpenses = transactionService.transactions
          .where((t) => 
              t.type == TransactionType.expense && 
              t.date.isAfter(startOfMonth.subtract(const Duration(days: 1))))
          .toList();
      
      // Group by category
      final categoryTotals = <String, double>{};
      for (final expense in thisMonthExpenses) {
        categoryTotals[expense.category] =
            (categoryTotals[expense.category] ?? 0.0) + expense.amount;
      }
      
      // Get top 3 categories
      final sortedCategories = categoryTotals.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));
      final topCategories = sortedCategories.take(3).toList();
      
      if (topCategories.isEmpty) {
        return Container(
          padding: EdgeInsets.all(DesignSystem.spacingMedium),
          decoration: BoxDecoration(
            color: DesignSystem.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline_rounded,
                color: DesignSystem.white.withOpacity(0.8),
                size: 16,
              ),
              SizedBox(width: DesignSystem.spacingSmall),
              Text(
                'No expenses this month',
                style: AppTypography.bodySmall.copyWith(
                  color: DesignSystem.white.withOpacity(0.8),
                ),
              ),
            ],
          ),
        );
      }
      
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Top Categories',
            style: AppTypography.titleMedium.copyWith(
              color: DesignSystem.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: DesignSystem.spacingMedium),
          ...topCategories.asMap().entries.map((entry) {
            final index = entry.key;
            final categoryEntry = entry.value;
            final category = categoryEntry.key;
            final amount = categoryEntry.value;
            
            return FadeInUp(
              duration: Duration(milliseconds: 300 + (index * 100)),
              child: Container(
                margin: EdgeInsets.only(bottom: DesignSystem.spacingSmall),
                padding: EdgeInsets.all(DesignSystem.spacingMedium),
                decoration: BoxDecoration(
                  color: DesignSystem.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(DesignSystem.spacingSmall),
                      decoration: BoxDecoration(
                        color: DesignSystem.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(DesignSystem.radiusSmall),
                      ),
                      child: Icon(
                        _getCategoryIcon(category),
                        color: DesignSystem.white,
                        size: 16,
                      ),
                    ),
                    SizedBox(width: DesignSystem.spacingMedium),
                    Expanded(
                      child: Text(
                        _getCategoryName(category),
                        style: AppTypography.bodyMedium.copyWith(
                          color: DesignSystem.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    Text(
                      currencyService.currentCurrency.value.formatAmount(amount),
                      style: AppTypography.bodyMedium.copyWith(
                        color: DesignSystem.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ],
      );
    });
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'food':
        return Icons.restaurant_rounded;
      case 'transportation':
        return Icons.directions_car_rounded;
      case 'housing':
        return Icons.home_rounded;
      case 'utilities':
        return Icons.electrical_services_rounded;
      case 'healthcare':
        return Icons.local_hospital_rounded;
      case 'entertainment':
        return Icons.movie_rounded;
      case 'shopping':
        return Icons.shopping_bag_rounded;
      case 'education':
        return Icons.school_rounded;
      default:
        return Icons.category_rounded;
    }
  }

  String _getCategoryName(String category) {
    return category.replaceAll('_', ' ').toUpperCase();
  }
}
