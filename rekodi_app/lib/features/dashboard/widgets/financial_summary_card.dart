import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/theme/typography.dart';
import '../../../core/services/personal_transaction_service.dart';
import '../../../core/services/currency_service.dart';
import '../../../core/services/savings_service.dart';
import '../../../core/services/transaction_charges_service.dart';

class FinancialSummaryCard extends StatelessWidget {
  const FinancialSummaryCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final transactionService = PersonalTransactionService.to;
      final currencyService = CurrencyService.to;
      final savingsService = SavingsService.to;
      final chargesService = TransactionChargesService.to;

      // Get real data from service
      final double totalBalance = transactionService.balance;
      final double monthlyIncome = transactionService.totalIncome;
      final double monthlyExpenses = transactionService.totalExpenses;
      final double totalSavings = savingsService.getTotalSavings();
      final double monthlyCharges = chargesService.totalMonthlyCharges;
      final double netIncome = monthlyIncome - monthlyExpenses - monthlyCharges;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            DesignSystem.primaryTeal,
            DesignSystem.primarySkyBlue,
          ],
        ),
        borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
        boxShadow: DesignSystem.shadowLarge,
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total Balance',
                  style: AppTypography.titleMedium.copyWith(
                    color: DesignSystem.white.withOpacity(0.9),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: DesignSystem.spacingMedium,
                    vertical: DesignSystem.spacingSmall,
                  ),
                  decoration: BoxDecoration(
                    color: DesignSystem.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
                    border: Border.all(
                      color: DesignSystem.white.withOpacity(0.1),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.visibility_outlined,
                        size: 16,
                        color: DesignSystem.white.withOpacity(0.9),
                      ),
                      SizedBox(width: DesignSystem.spacingXSmall),
                      Text(
                        'Show',
                        style: AppTypography.labelSmall.copyWith(
                          color: DesignSystem.white.withOpacity(0.9),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Balance Amount
            Text(
              currencyService.currentCurrency.value.formatAmount(totalBalance),
              style: AppTypography.displayMedium.copyWith(
                color: DesignSystem.white,
                fontWeight: FontWeight.bold,
                fontSize: 42,
                letterSpacing: -1.0,
              ),
            ),

            SizedBox(height: DesignSystem.spacingSmall),

            // Net Income Indicator
            Row(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: DesignSystem.spacingSmall,
                    vertical: DesignSystem.spacingXSmall,
                  ),
                  decoration: BoxDecoration(
                    color: netIncome >= 0
                        ? DesignSystem.success.withOpacity(0.2)
                        : DesignSystem.secondaryCoral.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
                    border: Border.all(
                      color: DesignSystem.white.withOpacity(0.1),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        netIncome >= 0
                            ? Icons.trending_up_rounded
                            : Icons.trending_down_rounded,
                        size: 16,
                        color: DesignSystem.white,
                      ),
                      SizedBox(width: DesignSystem.spacingXSmall),
                      Text(
                        '${netIncome >= 0 ? '+' : ''}${currencyService.currentCurrency.value.formatAmount(netIncome.abs())}',
                        style: AppTypography.labelMedium.copyWith(
                          color: DesignSystem.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: DesignSystem.spacingSmall),
                Text(
                  'this month',
                  style: AppTypography.bodySmall.copyWith(
                    color: DesignSystem.white.withOpacity(0.8),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: DesignSystem.spacingLarge),

            // Financial Summary Grid
            Column(
              children: [
                // First Row: Income and Expenses
                Row(
                  children: [
                    Expanded(
                      child: _buildSummaryItem(
                        context,
                        icon: Icons.arrow_upward_rounded,
                        label: 'Income',
                        amount: monthlyIncome,
                        color: DesignSystem.white,
                      ),
                    ),
                    Container(
                      width: 1,
                      height: 40,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            DesignSystem.white.withOpacity(0.1),
                            DesignSystem.white.withOpacity(0.3),
                            DesignSystem.white.withOpacity(0.1),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: _buildSummaryItem(
                        context,
                        icon: Icons.arrow_downward_rounded,
                        label: 'Expenses',
                        amount: monthlyExpenses,
                        color: DesignSystem.white,
                      ),
                    ),
                  ],
                ),

                SizedBox(height: DesignSystem.spacingMedium),

                // Second Row: Savings and Transaction Costs
                Row(
                  children: [
                    Expanded(
                      child: _buildSummaryItem(
                        context,
                        icon: Icons.savings_rounded,
                        label: 'Savings',
                        amount: totalSavings,
                        color: DesignSystem.white,
                      ),
                    ),
                    Container(
                      width: 1,
                      height: 40,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            DesignSystem.white.withOpacity(0.1),
                            DesignSystem.white.withOpacity(0.3),
                            DesignSystem.white.withOpacity(0.1),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: _buildSummaryItem(
                        context,
                        icon: Icons.receipt_long_rounded,
                        label: 'Charges',
                        amount: monthlyCharges,
                        color: DesignSystem.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
    });
  }

  Widget _buildSummaryItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required double amount,
    required Color color,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: DesignSystem.spacingMedium),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: EdgeInsets.all(DesignSystem.spacingXSmall),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(DesignSystem.radiusSmall),
                ),
                child: Icon(
                  icon,
                  size: 16,
                  color: color.withOpacity(0.9),
                ),
              ),
              SizedBox(width: DesignSystem.spacingXSmall),
              Text(
                label,
                style: AppTypography.bodySmall.copyWith(
                  color: color.withOpacity(0.9),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          SizedBox(height: DesignSystem.spacingXSmall),
          Obx(() {
            final currencyService = CurrencyService.to;
            return Text(
              currencyService.currentCurrency.value.formatAmount(amount),
              style: AppTypography.titleLarge.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
                fontSize: 20,
              ),
            );
          }),
        ],
      ),
    );
  }

  String _formatAmount(double amount) {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}M';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return amount.toStringAsFixed(2);
    }
  }
}
