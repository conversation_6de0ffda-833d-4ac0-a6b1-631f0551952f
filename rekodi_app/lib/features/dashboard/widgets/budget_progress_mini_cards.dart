import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/theme/typography.dart';
import '../../../core/services/personal_transaction_service.dart';
import '../../../core/models/transaction.dart';
import '../../../core/database/database.dart';

class BudgetProgressMiniCards extends StatefulWidget {
  const BudgetProgressMiniCards({super.key});

  @override
  State<BudgetProgressMiniCards> createState() => _BudgetProgressMiniCardsState();
}

class _BudgetProgressMiniCardsState extends State<BudgetProgressMiniCards>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          SizedBox(height: DesignSystem.spacingMedium),
          _buildBudgetCards(),
        ],
      );
    });
  }

  Widget _buildHeader() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: DesignSystem.spacingMedium),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Budget Progress',
                style: AppTypography.titleLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  color: DesignSystem.textPrimary,
                ),
              ),
              SizedBox(height: DesignSystem.spacingXSmall),
              Text(
                'Track your spending limits',
                style: AppTypography.bodyMedium.copyWith(
                  color: DesignSystem.textSecondary,
                ),
              ),
            ],
          ),
          GestureDetector(
            onTap: () {
              // Navigate to budgets screen
              Get.toNamed('/budgets');
            },
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: DesignSystem.spacingMedium,
                vertical: DesignSystem.spacingSmall,
              ),
              decoration: BoxDecoration(
                gradient: DesignSystem.primaryGradient,
                borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'View All',
                    style: AppTypography.labelMedium.copyWith(
                      color: DesignSystem.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(width: DesignSystem.spacingXSmall),
                  Icon(
                    Icons.arrow_forward_rounded,
                    size: 16,
                    color: DesignSystem.white,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBudgetCards() {
    final budgetData = _getBudgetData();
    
    if (budgetData.isEmpty) {
      return _buildEmptyState();
    }

    return SizedBox(
      height: 160, // Increased height to prevent overflow
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(horizontal: DesignSystem.spacingMedium),
            itemCount: budgetData.length,
            clipBehavior: Clip.none, // Prevent clipping
            itemBuilder: (context, index) {
              final budget = budgetData[index];
              return Padding(
                padding: EdgeInsets.only(
                  right: index < budgetData.length - 1 ? DesignSystem.spacingMedium : 0,
                ),
                child: _buildBudgetCard(budget, index),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildBudgetCard(BudgetData budget, int index) {
    final progress = budget.spent / budget.limit;
    final isOverBudget = progress > 1.0;
    final animatedProgress = progress * _animation.value;
    
    return Container(
      width: 170, // Increased width to prevent text overflow
      padding: EdgeInsets.all(DesignSystem.spacingMedium),
      decoration: BoxDecoration(
        color: DesignSystem.white,
        borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
        boxShadow: DesignSystem.shadowMedium,
        border: Border.all(
          color: isOverBudget
              ? DesignSystem.secondaryCoral.withOpacity(0.3)
              : DesignSystem.textSecondary.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Category icon and status
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(DesignSystem.spacingSmall),
                decoration: BoxDecoration(
                  color: budget.category.color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(DesignSystem.radiusSmall),
                ),
                child: Icon(
                  budget.category.icon,
                  size: 20,
                  color: budget.category.color,
                ),
              ),
              if (isOverBudget)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: DesignSystem.spacingSmall,
                    vertical: DesignSystem.spacingXSmall,
                  ),
                  decoration: BoxDecoration(
                    color: DesignSystem.secondaryCoral.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(DesignSystem.radiusSmall),
                  ),
                  child: Text(
                    'Over',
                    style: AppTypography.labelSmall.copyWith(
                      color: DesignSystem.secondaryCoral,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
          
          SizedBox(height: DesignSystem.spacingSmall),

          // Category name
          Text(
            budget.category.displayName,
            style: AppTypography.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: DesignSystem.textPrimary,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          
          SizedBox(height: DesignSystem.spacingXSmall),
          
          // Amount spent vs budget
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: '\$${_formatAmount(budget.spent)}',
                  style: AppTypography.labelMedium.copyWith(
                    color: isOverBudget 
                        ? DesignSystem.secondaryCoral 
                        : DesignSystem.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextSpan(
                  text: ' / \$${_formatAmount(budget.limit)}',
                  style: AppTypography.labelMedium.copyWith(
                    color: DesignSystem.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          
          SizedBox(height: DesignSystem.spacingSmall),

          // Progress bar
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 6,
                decoration: BoxDecoration(
                  color: DesignSystem.backgroundPrimary,
                  borderRadius: BorderRadius.circular(DesignSystem.radiusSmall),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(DesignSystem.radiusSmall),
                  child: LinearProgressIndicator(
                    value: animatedProgress.clamp(0.0, 1.0),
                    backgroundColor: Colors.transparent,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      isOverBudget 
                          ? DesignSystem.secondaryCoral
                          : budget.category.color,
                    ),
                  ),
                ),
              ),
              SizedBox(height: DesignSystem.spacingXSmall),
              Text(
                '${(progress * 100).toStringAsFixed(0)}% used',
                style: AppTypography.labelSmall.copyWith(
                  color: isOverBudget 
                      ? DesignSystem.secondaryCoral 
                      : DesignSystem.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      height: 150,
      margin: EdgeInsets.symmetric(horizontal: DesignSystem.spacingMedium),
      padding: EdgeInsets.all(DesignSystem.spacingLarge),
      decoration: BoxDecoration(
        color: DesignSystem.backgroundPrimary,
        borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
        border: Border.all(
          color: DesignSystem.textSecondary.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.pie_chart_outline_rounded,
            size: 32,
            color: DesignSystem.textSecondary,
          ),
          SizedBox(height: DesignSystem.spacingSmall),
          Text(
            'No budgets set',
            style: AppTypography.bodyMedium.copyWith(
              color: DesignSystem.textSecondary,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: DesignSystem.spacingXSmall),
          Text(
            'Create budgets to track spending',
            style: AppTypography.labelSmall.copyWith(
              color: DesignSystem.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  List<BudgetData> _getBudgetData() {
    final transactionService = PersonalTransactionService.to;
    final transactions = transactionService.transactions;

    // Get current month transactions
    final now = DateTime.now();
    final monthStart = DateTime(now.year, now.month, 1);
    final monthEnd = DateTime(now.year, now.month + 1, 1);

    final monthTransactions = transactions.where((t) =>
      t.type == 'expense' &&
      t.date.isAfter(monthStart) &&
      t.date.isBefore(monthEnd)
    ).toList();

    // Sample budget data - in a real app, this would come from a budget service
    final sampleBudgets = [
      BudgetData(
        category: TransactionCategory.food,
        limit: 800.0,
        spent: _getSpentForCategory(monthTransactions, TransactionCategory.food),
      ),
      BudgetData(
        category: TransactionCategory.transportation,
        limit: 300.0,
        spent: _getSpentForCategory(monthTransactions, TransactionCategory.transportation),
      ),
      BudgetData(
        category: TransactionCategory.entertainment,
        limit: 200.0,
        spent: _getSpentForCategory(monthTransactions, TransactionCategory.entertainment),
      ),
      BudgetData(
        category: TransactionCategory.shopping,
        limit: 400.0,
        spent: _getSpentForCategory(monthTransactions, TransactionCategory.shopping),
      ),
      BudgetData(
        category: TransactionCategory.healthcare,
        limit: 150.0,
        spent: _getSpentForCategory(monthTransactions, TransactionCategory.healthcare),
      ),
    ];

    // Filter out budgets with no spending and no limit
    return sampleBudgets.where((budget) =>
      budget.limit > 0 || budget.spent > 0
    ).toList();
  }

  double _getSpentForCategory(List<PersonalTransaction> transactions, TransactionCategory category) {
    return transactions
        .where((t) => t.category == category.name)
        .fold(0.0, (sum, t) => sum + t.amount);
  }

  String _formatAmount(double amount) {
    if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return amount.toStringAsFixed(0);
    }
  }
}

class BudgetData {
  final TransactionCategory category;
  final double limit;
  final double spent;

  BudgetData({
    required this.category,
    required this.limit,
    required this.spent,
  });

  double get progress => spent / limit;
  bool get isOverBudget => progress > 1.0;
  double get remaining => limit - spent;
}
