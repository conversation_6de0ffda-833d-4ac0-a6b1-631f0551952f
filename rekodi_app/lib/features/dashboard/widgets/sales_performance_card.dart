import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../core/services/business_product_service.dart';
import '../../../core/services/currency_service.dart';
import '../../../core/services/theme_service.dart';
import '../../../core/services/account_service.dart';
import '../../../core/models/business_transaction.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/theme/design_system.dart';

class SalesPerformanceCard extends StatelessWidget {
  final int periodDays;
  
  const SalesPerformanceCard({
    super.key,
    this.periodDays = 30,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final businessService = BusinessProductService.to;
      final analytics = businessService.getOverallAnalytics(days: periodDays);
      final currencyService = CurrencyService.to;
      final themeService = ThemeService.to;
      
      final totalSales = analytics['totalSales'] ?? 0;
      final totalRevenue = analytics['totalSalesRevenue'] ?? 0.0;
      final averageOrderValue = totalSales > 0 ? totalRevenue / totalSales : 0.0;
      
      // Get sales trend (simplified - comparing current period to previous period)
      final previousAnalytics = businessService.getOverallAnalytics(days: periodDays * 2);
      final previousRevenue = (previousAnalytics['totalSalesRevenue'] ?? 0.0) - totalRevenue;
      final growthRate = previousRevenue > 0 ? ((totalRevenue - previousRevenue) / previousRevenue) * 100 : 0.0;
      
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.success.withOpacity(0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.success.withOpacity(0.2),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.success.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.trending_up_rounded,
                    size: 20,
                    color: AppColors.success,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Sales',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      Text(
                        'Last $periodDays days',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Sales Metrics
            _buildMetricItem(
              'Total Sales',
              totalSales.toString(),
              AppColors.primary,
              Icons.shopping_cart_rounded,
            ),
            const SizedBox(height: 8),
            _buildMetricItem(
              'Revenue',
              currencyService.formatAmount(totalRevenue, AccountService.to.currentAccount?.currency ?? 'USD'),
              AppColors.success,
              Icons.attach_money_rounded,
            ),
            const SizedBox(height: 8),
            _buildMetricItem(
              'Avg Order',
              currencyService.formatAmount(averageOrderValue, AccountService.to.currentAccount?.currency ?? 'USD'),
              AppColors.secondary,
              Icons.receipt_rounded,
            ),
            
            if (growthRate != 0) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: growthRate >= 0 ? AppColors.success.withOpacity(0.1) : AppColors.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      growthRate >= 0 ? Icons.arrow_upward_rounded : Icons.arrow_downward_rounded,
                      size: 14,
                      color: growthRate >= 0 ? AppColors.success : AppColors.error,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${growthRate.abs().toStringAsFixed(1)}%',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: growthRate >= 0 ? AppColors.success : AppColors.error,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'vs prev period',
                      style: TextStyle(
                        fontSize: 10,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      );
    });
  }

  Widget _buildMetricItem(String label, String value, Color color, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Icon(
            icon,
            size: 12,
            color: color,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }
}

/// Detailed Sales Performance Widget with charts
class DetailedSalesPerformanceCard extends StatelessWidget {
  final int periodDays;
  
  const DetailedSalesPerformanceCard({
    super.key,
    this.periodDays = 30,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final businessService = BusinessProductService.to;
      final analytics = businessService.getOverallAnalytics(days: periodDays);
      final currencyService = CurrencyService.to;
      
      final totalSales = analytics['totalSales'] ?? 0;
      final totalRevenue = analytics['totalSalesRevenue'] ?? 0.0;
      final averageOrderValue = totalSales > 0 ? totalRevenue / totalSales : 0.0;
      
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Sales Performance',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.success.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${periodDays}d',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppColors.success,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            // Key Metrics Row
            Row(
              children: [
                Expanded(
                  child: _buildKeyMetric(
                    'Total Sales',
                    totalSales.toString(),
                    'transactions',
                    AppColors.primary,
                    Icons.shopping_cart_rounded,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildKeyMetric(
                    'Revenue',
                    currencyService.formatAmount(totalRevenue, AccountService.to.currentAccount?.currency ?? 'USD'),
                    'earned',
                    AppColors.success,
                    Icons.attach_money_rounded,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildKeyMetric(
                    'Avg Order',
                    currencyService.formatAmount(averageOrderValue, AccountService.to.currentAccount?.currency ?? 'USD'),
                    'per sale',
                    AppColors.secondary,
                    Icons.receipt_rounded,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildKeyMetric(
                    'Daily Avg',
                    currencyService.formatAmount(totalRevenue / periodDays, AccountService.to.currentAccount?.currency ?? 'USD'),
                    'per day',
                    AppColors.warning,
                    Icons.calendar_today_rounded,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Sales Trend Chart (simplified)
            Container(
              height: 120,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.success.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
              ),
              child: _buildSalesTrendChart(),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildKeyMetric(String title, String value, String subtitle, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 10,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSalesTrendChart() {
    // Simplified chart - in a real implementation, you'd get actual daily sales data
    final spots = List.generate(7, (index) {
      return FlSpot(index.toDouble(), (index * 10 + 20).toDouble());
    });

    return LineChart(
      LineChartData(
        gridData: FlGridData(show: false),
        titlesData: FlTitlesData(show: false),
        borderData: FlBorderData(show: false),
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: AppColors.success,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: FlDotData(show: false),
            belowBarData: BarAreaData(
              show: true,
              color: AppColors.success.withOpacity(0.2),
            ),
          ),
        ],
      ),
    );
  }
}
