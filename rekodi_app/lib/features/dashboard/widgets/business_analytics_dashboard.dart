import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/services/business_product_service.dart';
import '../../../core/services/currency_service.dart';
import '../../../core/services/theme_service.dart';
import '../../../core/services/account_service.dart';
import '../../../core/models/business_transaction.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/theme/design_system.dart';
import 'profit_loss_summary_card.dart';
import 'inventory_status_card.dart';
import 'sales_performance_card.dart';
import 'top_products_services_card.dart';

class BusinessAnalyticsDashboard extends StatefulWidget {
  const BusinessAnalyticsDashboard({super.key});

  @override
  State<BusinessAnalyticsDashboard> createState() => _BusinessAnalyticsDashboardState();
}

class _BusinessAnalyticsDashboardState extends State<BusinessAnalyticsDashboard> {
  int _selectedPeriod = 30; // Default to 30 days
  final List<int> _periodOptions = [7, 30, 90, 365];

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final themeService = ThemeService.to;
      final isDark = themeService.isDarkMode;
      
      return Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDark ? AppColors.surfaceDark : Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const Divider(height: 1),
            _buildAnalyticsContent(),
          ],
        ),
      );
    });
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Business Analytics',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Last ${_selectedPeriod} days',
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
          _buildPeriodSelector(),
        ],
      ),
    );
  }

  Widget _buildPeriodSelector() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.primary.withOpacity(0.3),
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<int>(
          value: _selectedPeriod,
          isDense: true,
          style: TextStyle(
            fontSize: 14,
            color: AppColors.primary,
            fontWeight: FontWeight.w500,
          ),
          items: _periodOptions.map((period) {
            return DropdownMenuItem<int>(
              value: period,
              child: Text('${period}d'),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedPeriod = value;
              });
            }
          },
        ),
      ),
    );
  }

  Widget _buildAnalyticsContent() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Profit/Loss Summary Card
          ProfitLossSummaryCard(periodDays: _selectedPeriod),
          const SizedBox(height: 16),
          
          // Row with Inventory Status and Sales Performance
          Row(
            children: [
              Expanded(
                child: InventoryStatusCard(),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: SalesPerformanceCard(periodDays: _selectedPeriod),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Top Products/Services Card
          TopProductsServicesCard(periodDays: _selectedPeriod),
        ],
      ),
    );
  }
}

/// Quick Business Metrics Widget for smaller spaces
class QuickBusinessMetrics extends StatelessWidget {
  final int periodDays;
  
  const QuickBusinessMetrics({
    super.key,
    this.periodDays = 30,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final businessService = BusinessProductService.to;
      final analytics = businessService.getOverallAnalytics(days: periodDays);
      final currencyService = CurrencyService.to;
      
      final totalRevenue = analytics['totalSalesRevenue'] ?? 0.0;
      final totalCosts = analytics['totalPurchasesCost'] ?? 0.0;
      final profit = totalRevenue - totalCosts;
      final profitMargin = totalRevenue > 0 ? (profit / totalRevenue) * 100 : 0.0;
      
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.primary.withOpacity(0.1),
              AppColors.secondary.withOpacity(0.1),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.primary.withOpacity(0.2),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Quick Metrics',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${periodDays}d',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Revenue
            _buildMetricRow(
              'Revenue',
              currencyService.formatAmount(totalRevenue, AccountService.to.currentAccount?.currency ?? 'USD'),
              Icons.trending_up_rounded,
              AppColors.success,
            ),
            const SizedBox(height: 8),
            
            // Profit
            _buildMetricRow(
              'Profit',
              currencyService.formatAmount(profit, AccountService.to.currentAccount?.currency ?? 'USD'),
              profit >= 0 ? Icons.arrow_upward_rounded : Icons.arrow_downward_rounded,
              profit >= 0 ? AppColors.success : AppColors.error,
            ),
            const SizedBox(height: 8),
            
            // Transaction Costs
            _buildMetricRow(
              'Transaction Costs',
              currencyService.formatAmount(totalCosts * 0.05, AccountService.to.currentAccount?.currency ?? 'USD'), // Estimated 5% transaction costs
              Icons.receipt_long_rounded,
              AppColors.warning,
            ),
            const SizedBox(height: 8),

            // Profit Margin
            _buildMetricRow(
              'Margin',
              '${profitMargin.toStringAsFixed(1)}%',
              Icons.percent_rounded,
              profitMargin >= 20 ? AppColors.success :
              profitMargin >= 10 ? AppColors.warning : AppColors.error,
            ),
          ],
        ),
      );
    });
  }

  Widget _buildMetricRow(String label, String value, IconData icon, Color color) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            icon,
            size: 16,
            color: color,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }
}

/// Business Performance Indicator Widget
class BusinessPerformanceIndicator extends StatelessWidget {
  final double currentValue;
  final double targetValue;
  final String label;
  final IconData icon;
  final Color color;
  
  const BusinessPerformanceIndicator({
    super.key,
    required this.currentValue,
    required this.targetValue,
    required this.label,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    final progress = targetValue > 0 ? (currentValue / targetValue).clamp(0.0, 1.0) : 0.0;
    final percentage = (progress * 100).toInt();
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
              Text(
                '$percentage%',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: color.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
          const SizedBox(height: 4),
          Text(
            '${CurrencyService.to.formatAmount(currentValue, AccountService.to.currentAccount?.currency ?? 'USD')} / ${CurrencyService.to.formatAmount(targetValue, AccountService.to.currentAccount?.currency ?? 'USD')}',
            style: TextStyle(
              fontSize: 10,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }
}
