import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/models/business_projection.dart';
import '../../../core/services/business_projection_service.dart';
import '../../../core/services/currency_service.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/constants/app_colors.dart';
import 'add_projection_dialog.dart';

class BusinessGrowthProjectionsCard extends StatelessWidget {
  const BusinessGrowthProjectionsCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final projectionService = BusinessProjectionService.to;
      final activeProjections = projectionService.getActiveProjections();
      
      if (activeProjections.isEmpty) {
        return _buildEmptyState();
      }

      return Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const Divider(height: 1),
            _buildProjectionsList(activeProjections),
            _buildFooter(),
          ],
        ),
      );
    });
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: DesignSystem.primaryTeal.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.trending_up_rounded,
              color: DesignSystem.primaryTeal,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Growth Projections',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                Text(
                  'Track your business targets',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _showAddProjectionDialog(),
            icon: Icon(
              Icons.add_rounded,
              color: DesignSystem.primaryTeal,
            ),
            style: IconButton.styleFrom(
              backgroundColor: DesignSystem.primaryTeal.withOpacity(0.1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProjectionsList(List<BusinessProjection> projections) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: projections.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final projection = projections[index];
        return _buildProjectionItem(projection);
      },
    );
  }

  Widget _buildProjectionItem(BusinessProjection projection) {
    final currency = CurrencyService.to.currentCurrency.value;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: projection.overallStatus.color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      projection.name,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      projection.period.displayName,
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: projection.overallStatus.color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  projection.statusMessage,
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: projection.overallStatus.color,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // Sales Progress
          _buildProgressItem(
            'Sales',
            projection.actualSales,
            projection.targetSales,
            currency,
            DesignSystem.success,
          ),
          const SizedBox(height: 8),
          
          // Profit Progress
          _buildProgressItem(
            'Profit',
            projection.actualProfit,
            projection.targetProfit,
            currency,
            DesignSystem.primaryTeal,
          ),
          const SizedBox(height: 8),
          
          // Expenses Progress
          _buildProgressItem(
            'Expenses',
            projection.actualExpenses,
            projection.targetExpenses,
            currency,
            DesignSystem.warning,
            isExpense: true,
          ),
        ],
      ),
    );
  }

  Widget _buildProgressItem(
    String label,
    double actual,
    double target,
    dynamic currency,
    Color color, {
    bool isExpense = false,
  }) {
    final progress = target > 0 ? (actual / target).clamp(0.0, 1.0) : 0.0;
    final isOverTarget = actual > target;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${currency.formatAmount(actual)} / ${currency.formatAmount(target)}',
              style: TextStyle(
                fontSize: 11,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: color.withOpacity(0.2),
          valueColor: AlwaysStoppedAnimation<Color>(
            isExpense && isOverTarget ? DesignSystem.error : color,
          ),
          minHeight: 6,
        ),
      ],
    );
  }

  Widget _buildFooter() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: TextButton.icon(
              onPressed: () => _navigateToProjections(),
              icon: Icon(
                Icons.analytics_outlined,
                size: 16,
                color: DesignSystem.primaryTeal,
              ),
              label: Text(
                'View Details',
                style: TextStyle(
                  color: DesignSystem.primaryTeal,
                  fontSize: 12,
                ),
              ),
            ),
          ),
          TextButton.icon(
            onPressed: () => _showAddProjectionDialog(),
            icon: Icon(
              Icons.add_rounded,
              size: 16,
              color: DesignSystem.primaryTeal,
            ),
            label: Text(
              'New Target',
              style: TextStyle(
                color: DesignSystem.primaryTeal,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.trending_up_rounded,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Set Growth Targets',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create projections to track your business growth and performance',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => _showAddProjectionDialog(),
            icon: const Icon(Icons.add_rounded),
            label: const Text('Create Projection'),
            style: ElevatedButton.styleFrom(
              backgroundColor: DesignSystem.primaryTeal,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _showAddProjectionDialog() {
    showDialog(
      context: Get.context!,
      builder: (context) => const AddProjectionDialog(),
    );
  }

  void _navigateToProjections() {
    // TODO: Navigate to projections screen
    Get.snackbar(
      'Coming Soon',
      'Detailed projections view will be implemented',
      backgroundColor: DesignSystem.primaryTeal,
      colorText: Colors.white,
    );
  }
}
