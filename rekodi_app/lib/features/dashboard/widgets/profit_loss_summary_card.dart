import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../core/services/business_product_service.dart';
import '../../../core/services/currency_service.dart';
import '../../../core/services/theme_service.dart';
import '../../../core/services/account_service.dart';
import '../../../core/models/business_transaction.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/theme/design_system.dart';

class ProfitLossSummaryCard extends StatelessWidget {
  final int periodDays;
  
  const ProfitLossSummaryCard({
    super.key,
    this.periodDays = 30,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final businessService = BusinessProductService.to;
      final analytics = businessService.getOverallAnalytics(days: periodDays);
      final currencyService = CurrencyService.to;
      final themeService = ThemeService.to;
      
      final totalRevenue = analytics['totalSalesRevenue'] ?? 0.0;
      final totalCosts = analytics['totalPurchasesCost'] ?? 0.0;
      final profit = totalRevenue - totalCosts;
      final profitMargin = totalRevenue > 0 ? (profit / totalRevenue) * 100 : 0.0;
      
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.primary.withOpacity(0.1),
              AppColors.secondary.withOpacity(0.1),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.primary.withOpacity(0.2),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Profit & Loss Summary',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    Text(
                      'Last $periodDays days',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: profit >= 0 ? AppColors.success.withOpacity(0.1) : AppColors.error.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: profit >= 0 ? AppColors.success.withOpacity(0.3) : AppColors.error.withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        profit >= 0 ? Icons.trending_up_rounded : Icons.trending_down_rounded,
                        size: 16,
                        color: profit >= 0 ? AppColors.success : AppColors.error,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${profitMargin.toStringAsFixed(1)}%',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: profit >= 0 ? AppColors.success : AppColors.error,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            // Main Profit/Loss Display
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        profit >= 0 ? 'Net Profit' : 'Net Loss',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        currencyService.formatAmount(profit.abs(), AccountService.to.currentAccount?.currency ?? 'USD'),
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: profit >= 0 ? AppColors.success : AppColors.error,
                        ),
                      ),
                      const SizedBox(height: 16),
                      
                      // Revenue and Costs breakdown
                      _buildBreakdownItem(
                        'Revenue',
                        currencyService.formatAmount(totalRevenue, AccountService.to.currentAccount?.currency ?? 'USD'),
                        AppColors.success,
                        Icons.arrow_upward_rounded,
                      ),
                      const SizedBox(height: 8),
                      _buildBreakdownItem(
                        'Costs',
                        currencyService.formatAmount(totalCosts, AccountService.to.currentAccount?.currency ?? 'USD'),
                        AppColors.error,
                        Icons.arrow_downward_rounded,
                      ),
                    ],
                  ),
                ),
                
                // Profit/Loss Chart
                Expanded(
                  child: SizedBox(
                    height: 120,
                    child: _buildProfitLossChart(totalRevenue, totalCosts),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  Widget _buildBreakdownItem(String label, String amount, Color color, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Icon(
            icon,
            size: 12,
            color: color,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
          ),
        ),
        Text(
          amount,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildProfitLossChart(double revenue, double costs) {
    if (revenue == 0 && costs == 0) {
      return Center(
        child: Text(
          'No data',
          style: TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
      );
    }

    final total = revenue + costs;
    final revenuePercentage = total > 0 ? (revenue / total) * 100 : 0.0;
    final costsPercentage = total > 0 ? (costs / total) * 100 : 0.0;

    return PieChart(
      PieChartData(
        sectionsSpace: 2,
        centerSpaceRadius: 25,
        sections: [
          PieChartSectionData(
            color: AppColors.success,
            value: revenuePercentage,
            title: '${revenuePercentage.toInt()}%',
            radius: 35,
            titleStyle: const TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          PieChartSectionData(
            color: AppColors.error,
            value: costsPercentage,
            title: '${costsPercentage.toInt()}%',
            radius: 35,
            titleStyle: const TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}

/// Compact Profit/Loss Widget for smaller spaces
class CompactProfitLossWidget extends StatelessWidget {
  final int periodDays;
  
  const CompactProfitLossWidget({
    super.key,
    this.periodDays = 30,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final businessService = BusinessProductService.to;
      final analytics = businessService.getOverallAnalytics(days: periodDays);
      final currencyService = CurrencyService.to;
      
      final totalRevenue = analytics['totalSalesRevenue'] ?? 0.0;
      final totalCosts = analytics['totalPurchasesCost'] ?? 0.0;
      final profit = totalRevenue - totalCosts;
      
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: profit >= 0 ? AppColors.success.withOpacity(0.1) : AppColors.error.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: profit >= 0 ? AppColors.success.withOpacity(0.3) : AppColors.error.withOpacity(0.3),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  profit >= 0 ? Icons.trending_up_rounded : Icons.trending_down_rounded,
                  size: 16,
                  color: profit >= 0 ? AppColors.success : AppColors.error,
                ),
                const SizedBox(width: 8),
                Text(
                  profit >= 0 ? 'Profit' : 'Loss',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              currencyService.formatAmount(profit.abs(), AccountService.to.currentAccount?.currency ?? 'USD'),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: profit >= 0 ? AppColors.success : AppColors.error,
              ),
            ),
            Text(
              'Last $periodDays days',
              style: TextStyle(
                fontSize: 10,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    });
  }
}
