import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/services/business_product_service.dart';
import '../../../core/services/theme_service.dart';
import '../../../core/models/business_product.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/theme/design_system.dart';

class InventoryStatusCard extends StatelessWidget {
  const InventoryStatusCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final businessService = BusinessProductService.to;
      final products = businessService.products.where((p) => p.isProduct).toList();
      final themeService = ThemeService.to;
      
      final totalProducts = products.length;
      final lowStockProducts = products.where((p) => p.isLowStock && !p.isOutOfStock).length;
      final outOfStockProducts = products.where((p) => p.isOutOfStock).length;
      final inStockProducts = totalProducts - lowStockProducts - outOfStockProducts;
      
      final totalValue = products.fold<double>(0.0, (sum, product) {
        final quantity = product.quantity ?? 0;
        final price = product.price;
        return sum + (quantity * price);
      });
      
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.primary.withOpacity(0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.primary.withOpacity(0.2),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.inventory_2_rounded,
                    size: 20,
                    color: AppColors.primary,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Inventory',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      Text(
                        '$totalProducts items',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Inventory Status Breakdown
            _buildStatusItem(
              'In Stock',
              inStockProducts,
              AppColors.success,
              Icons.check_circle_rounded,
            ),
            const SizedBox(height: 8),
            _buildStatusItem(
              'Low Stock',
              lowStockProducts,
              AppColors.warning,
              Icons.warning_rounded,
            ),
            const SizedBox(height: 8),
            _buildStatusItem(
              'Out of Stock',
              outOfStockProducts,
              AppColors.error,
              Icons.error_rounded,
            ),
            
            if (totalValue > 0) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Total Value',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    Text(
                      '\$${totalValue.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      );
    });
  }

  Widget _buildStatusItem(String label, int count, Color color, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Icon(
            icon,
            size: 12,
            color: color,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Text(
            count.toString(),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ),
      ],
    );
  }
}

/// Detailed Inventory Status Widget with more information
class DetailedInventoryStatusCard extends StatelessWidget {
  const DetailedInventoryStatusCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final businessService = BusinessProductService.to;
      final products = businessService.products.where((p) => p.isProduct).toList();
      
      final totalProducts = products.length;
      final lowStockProducts = products.where((p) => p.isLowStock && !p.isOutOfStock).toList();
      final outOfStockProducts = products.where((p) => p.isOutOfStock).toList();
      
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Inventory Status',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                IconButton(
                  onPressed: () {
                    Get.toNamed('/inventory');
                  },
                  icon: Icon(
                    Icons.arrow_forward_ios_rounded,
                    size: 16,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Quick Stats
            Row(
              children: [
                Expanded(
                  child: _buildQuickStat(
                    'Total Items',
                    totalProducts.toString(),
                    AppColors.primary,
                    Icons.inventory_2_rounded,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickStat(
                    'Alerts',
                    (lowStockProducts.length + outOfStockProducts.length).toString(),
                    AppColors.warning,
                    Icons.warning_rounded,
                  ),
                ),
              ],
            ),
            
            if (lowStockProducts.isNotEmpty || outOfStockProducts.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),
              
              // Alert Items
              if (outOfStockProducts.isNotEmpty) ...[
                _buildAlertSection(
                  'Out of Stock',
                  outOfStockProducts,
                  AppColors.error,
                  Icons.error_rounded,
                ),
                if (lowStockProducts.isNotEmpty) const SizedBox(height: 12),
              ],
              
              if (lowStockProducts.isNotEmpty) ...[
                _buildAlertSection(
                  'Low Stock',
                  lowStockProducts,
                  AppColors.warning,
                  Icons.warning_rounded,
                ),
              ],
            ],
          ],
        ),
      );
    });
  }

  Widget _buildQuickStat(String label, String value, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAlertSection(String title, List<BusinessProduct> products, Color color, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
            const Spacer(),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                products.length.toString(),
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ...products.take(3).map((product) => Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Row(
            children: [
              const SizedBox(width: 24),
              Expanded(
                child: Text(
                  product.name,
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Text(
                'Qty: ${product.quantity ?? 0}',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: color,
                ),
              ),
            ],
          ),
        )).toList(),
        if (products.length > 3)
          Padding(
            padding: const EdgeInsets.only(left: 24, top: 4),
            child: Text(
              '+${products.length - 3} more items',
              style: TextStyle(
                fontSize: 11,
                color: AppColors.textSecondary,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }
}
