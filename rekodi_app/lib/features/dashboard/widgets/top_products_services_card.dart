import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/services/business_product_service.dart';
import '../../../core/services/currency_service.dart';
import '../../../core/services/theme_service.dart';
import '../../../core/services/account_service.dart';
import '../../../core/models/business_product.dart';
import '../../../core/models/business_transaction.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/theme/design_system.dart';

class TopProductsServicesCard extends StatefulWidget {
  final int periodDays;
  
  const TopProductsServicesCard({
    super.key,
    this.periodDays = 30,
  });

  @override
  State<TopProductsServicesCard> createState() => _TopProductsServicesCardState();
}

class _TopProductsServicesCardState extends State<TopProductsServicesCard> with TickerProviderStateMixin {
  late TabController _tabController;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final businessService = BusinessProductService.to;
      final themeService = ThemeService.to;
      final isDark = themeService.isDarkMode;
      
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDark ? AppColors.surfaceDark : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.primary.withOpacity(0.2),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Top Performers',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${widget.periodDays}d',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Tab Bar
            Container(
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: TabBar(
                controller: _tabController,
                labelColor: AppColors.primary,
                unselectedLabelColor: AppColors.textSecondary,
                indicatorSize: TabBarIndicatorSize.tab,
                indicator: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                tabs: const [
                  Tab(text: 'Products'),
                  Tab(text: 'Services'),
                ],
              ),
            ),
            const SizedBox(height: 16),
            
            // Tab Content
            SizedBox(
              height: 200,
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildTopProductsList(),
                  _buildTopServicesList(),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildTopProductsList() {
    return Obx(() {
      final businessService = BusinessProductService.to;
      final topProducts = businessService.getTopSellingProducts(days: widget.periodDays);
      
      if (topProducts.isEmpty) {
        return _buildEmptyState('No product sales yet', Icons.inventory_2_outlined);
      }
      
      return ListView.builder(
        itemCount: topProducts.length.clamp(0, 5),
        itemBuilder: (context, index) {
          final productData = topProducts[index];
          final product = productData['product'] as BusinessProduct;
          final quantity = productData['quantity'] as int;
          final revenue = productData['revenue'] as double;
          
          return _buildTopItemCard(
            product.name,
            '$quantity sold',
            revenue,
            product.isProduct ? Icons.inventory_2_rounded : Icons.design_services_rounded,
            AppColors.primary,
            index + 1,
          );
        },
      );
    });
  }

  Widget _buildTopServicesList() {
    return Obx(() {
      final businessService = BusinessProductService.to;
      final topServices = businessService.getTopSellingServices(days: widget.periodDays);
      
      if (topServices.isEmpty) {
        return _buildEmptyState('No service sales yet', Icons.design_services_outlined);
      }
      
      return ListView.builder(
        itemCount: topServices.length.clamp(0, 5),
        itemBuilder: (context, index) {
          final serviceData = topServices[index];
          final service = serviceData['product'] as BusinessProduct;
          final quantity = serviceData['quantity'] as int;
          final revenue = serviceData['revenue'] as double;
          
          return _buildTopItemCard(
            service.name,
            '$quantity sold',
            revenue,
            Icons.design_services_rounded,
            AppColors.secondary,
            index + 1,
          );
        },
      );
    });
  }

  Widget _buildTopItemCard(
    String name,
    String subtitle,
    double revenue,
    IconData icon,
    Color color,
    int rank,
  ) {
    final currencyService = CurrencyService.to;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          // Rank Badge
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                rank.toString(),
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          
          // Icon
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              icon,
              size: 16,
              color: color,
            ),
          ),
          const SizedBox(width: 12),
          
          // Product/Service Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          
          // Revenue
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                currencyService.formatAmount(revenue, AccountService.to.currentAccount?.currency ?? 'USD'),
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                'revenue',
                style: TextStyle(
                  fontSize: 10,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 48,
            color: AppColors.textSecondary.withOpacity(0.5),
          ),
          const SizedBox(height: 12),
          Text(
            message,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Compact Top Products Widget for smaller spaces
class CompactTopProductsWidget extends StatelessWidget {
  final int periodDays;
  final int maxItems;
  
  const CompactTopProductsWidget({
    super.key,
    this.periodDays = 30,
    this.maxItems = 3,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final businessService = BusinessProductService.to;
      final topProducts = businessService.getTopSellingProducts(days: periodDays);
      final currencyService = CurrencyService.to;
      
      if (topProducts.isEmpty) {
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.05),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Text(
              'No sales data yet',
              style: TextStyle(
                fontSize: 12,
                color: AppColors.textSecondary,
              ),
            ),
          ),
        );
      }
      
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppColors.primary.withOpacity(0.05),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: AppColors.primary.withOpacity(0.2),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.star_rounded,
                  size: 16,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Top Products',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            ...topProducts.take(maxItems).map((productData) {
              final product = productData['product'] as BusinessProduct;
              final quantity = productData['quantity'] as int;
              final revenue = productData['revenue'] as double;
              
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    Container(
                      width: 4,
                      height: 4,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        product.name,
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.textPrimary,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Text(
                      currencyService.formatAmount(revenue, AccountService.to.currentAccount?.currency ?? 'USD'),
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
            
            if (topProducts.length > maxItems)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  '+${topProducts.length - maxItems} more',
                  style: TextStyle(
                    fontSize: 10,
                    color: AppColors.textSecondary,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
          ],
        ),
      );
    });
  }
}
