import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/services/theme_service.dart';
import '../../../core/services/account_service.dart';
import '../../../core/services/personal_transaction_service.dart';
import '../../../core/models/transaction.dart';
import '../../../core/database/database.dart';
import '../../common/widgets/common_app_bar.dart';
import '../../common/widgets/account_drawer.dart';

class SalesScreen extends StatefulWidget {
  const SalesScreen({super.key});

  @override
  State<SalesScreen> createState() => _SalesScreenState();
}

class _SalesScreenState extends State<SalesScreen> {
  String _selectedPeriod = 'This Month';
  final List<String> _periods = ['Today', 'This Week', 'This Month', 'This Year', 'All Time'];

  List<PersonalTransaction> _salesTransactions = [];
  double _totalSales = 0.0;
  int _totalOrders = 0;
  double _averageOrder = 0.0;
  double _profitMargin = 32.0; // Default margin, can be calculated from cost data

  @override
  void initState() {
    super.initState();
    _loadSalesData();
  }

  void _loadSalesData() {
    final transactionService = PersonalTransactionService.to;
    final allTransactions = transactionService.transactions;

    // Filter sales transactions (income transactions for business accounts)
    final salesTransactions = allTransactions
        .where((t) => t.type == 'income')
        .toList();

    // Filter by selected period
    _salesTransactions = _filterTransactionsByPeriod(salesTransactions);

    // Calculate analytics
    _calculateSalesAnalytics();

    setState(() {});
  }

  List<PersonalTransaction> _filterTransactionsByPeriod(List<PersonalTransaction> transactions) {
    final now = DateTime.now();
    DateTime startDate;

    switch (_selectedPeriod) {
      case 'Today':
        startDate = DateTime(now.year, now.month, now.day);
        break;
      case 'This Week':
        startDate = now.subtract(Duration(days: now.weekday - 1));
        startDate = DateTime(startDate.year, startDate.month, startDate.day);
        break;
      case 'This Month':
        startDate = DateTime(now.year, now.month, 1);
        break;
      case 'This Year':
        startDate = DateTime(now.year, 1, 1);
        break;
      case 'All Time':
        return transactions;
      default:
        startDate = DateTime(now.year, now.month, 1);
    }

    return transactions.where((t) => t.date.isAfter(startDate) || t.date.isAtSameMomentAs(startDate)).toList();
  }

  void _calculateSalesAnalytics() {
    _totalSales = _salesTransactions.fold(0.0, (sum, t) => sum + t.amount);
    _totalOrders = _salesTransactions.length;
    _averageOrder = _totalOrders > 0 ? _totalSales / _totalOrders : 0.0;
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final themeService = ThemeService.to;
      final accountService = AccountService.to;
      
      return Scaffold(
        backgroundColor: themeService.backgroundColor,
        appBar: CommonAppBar(
          title: 'Sales',
          showAccountInfo: true,
          actions: [
            IconButton(
              onPressed: () {
                _showFilterOptions(context);
              },
              icon: const Icon(Icons.filter_list_rounded),
            ),
            IconButton(
              onPressed: () {
                _showSalesReport(context);
              },
              icon: const Icon(Icons.analytics_rounded),
            ),
          ],
        ),
        endDrawer: const AccountDrawer(),
        body: RefreshIndicator(
          onRefresh: _refreshData,
          color: themeService.primaryColor,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Period Selector
                _buildPeriodSelector(themeService),
                
                const SizedBox(height: 16),
                
                // Sales Summary Cards
                _buildSalesSummary(themeService),
                
                const SizedBox(height: 20),
                
                // Top Products
                _buildTopProducts(themeService),
                
                const SizedBox(height: 20),
                
                // Recent Sales
                _buildRecentSales(themeService),
                
                const SizedBox(height: 100), // Space for bottom nav
              ],
            ),
          ),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () {
            _showNewSaleDialog(context);
          },
          backgroundColor: Colors.green,
          heroTag: "sales_fab",
          child: const Icon(Icons.point_of_sale_rounded, color: Colors.white),
        ),
      );
    });
  }

  Widget _buildPeriodSelector(ThemeService themeService) {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _periods.length,
        itemBuilder: (context, index) {
          final period = _periods[index];
          final isSelected = period == _selectedPeriod;
          
          return Container(
            margin: const EdgeInsets.only(right: 12),
            child: FilterChip(
              label: Text(period),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedPeriod = period;
                  _loadSalesData();
                });
              },
              backgroundColor: themeService.surfaceColor,
              selectedColor: Colors.green.withOpacity(0.2),
              labelStyle: TextStyle(
                color: isSelected ? Colors.green : themeService.textSecondaryColor,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSalesSummary(ThemeService themeService) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  title: 'Total Sales',
                  amount: '\$${_totalSales.toStringAsFixed(2)}',
                  subtitle: 'for $_selectedPeriod',
                  color: Colors.green,
                  icon: Icons.trending_up_rounded,
                  themeService: themeService,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildSummaryCard(
                  title: 'Orders',
                  amount: '$_totalOrders',
                  subtitle: 'total orders',
                  color: Colors.blue,
                  icon: Icons.shopping_cart_rounded,
                  themeService: themeService,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  title: 'Average Order',
                  amount: '\$${_averageOrder.toStringAsFixed(2)}',
                  subtitle: 'per order',
                  color: Colors.orange,
                  icon: Icons.analytics_rounded,
                  themeService: themeService,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildSummaryCard(
                  title: 'Profit Margin',
                  amount: '${_profitMargin.toStringAsFixed(1)}%',
                  subtitle: 'gross margin',
                  color: Colors.purple,
                  icon: Icons.percent_rounded,
                  themeService: themeService,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String amount,
    required String subtitle,
    required Color color,
    required IconData icon,
    required ThemeService themeService,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: themeService.surfaceColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
              Icon(Icons.more_vert_rounded, color: themeService.textSecondaryColor, size: 16),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: TextStyle(
              color: themeService.textSecondaryColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            amount,
            style: TextStyle(
              color: themeService.textPrimaryColor,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: TextStyle(
              color: themeService.textSecondaryColor,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopProducts(ThemeService themeService) {
    final products = [
      {'name': 'Premium Coffee Beans', 'sales': '\$2,340.00', 'units': '78 units', 'color': Colors.brown},
      {'name': 'Wireless Headphones', 'sales': '\$1,890.00', 'units': '45 units', 'color': Colors.blue},
      {'name': 'Organic Tea Set', 'sales': '\$1,560.00', 'units': '52 units', 'color': Colors.green},
      {'name': 'Smart Watch', 'sales': '\$1,200.00', 'units': '24 units', 'color': Colors.purple},
    ];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Top Products',
                style: TextStyle(
                  color: themeService.textPrimaryColor,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () {
                  // Navigate to all products
                },
                child: Text(
                  'View All',
                  style: TextStyle(color: themeService.primaryColor),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...products.map((product) => _buildProductItem(product, themeService)),
        ],
      ),
    );
  }

  Widget _buildProductItem(Map<String, dynamic> product, ThemeService themeService) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: themeService.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: product['color'].withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.inventory_2_rounded,
              color: product['color'],
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  product['name'],
                  style: TextStyle(
                    color: themeService.textPrimaryColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  product['units'],
                  style: TextStyle(
                    color: themeService.textSecondaryColor,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Text(
            product['sales'],
            style: TextStyle(
              color: Colors.green,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentSales(ThemeService themeService) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Sales',
                style: TextStyle(
                  color: themeService.textPrimaryColor,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () {
                  // Navigate to all sales
                },
                child: Text(
                  'View All',
                  style: TextStyle(color: themeService.primaryColor),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Placeholder for recent sales
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: themeService.surfaceColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Column(
                children: [
                  Icon(
                    Icons.point_of_sale_rounded,
                    size: 48,
                    color: themeService.textSecondaryColor,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'No recent sales',
                    style: TextStyle(
                      color: themeService.textSecondaryColor,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showFilterOptions(BuildContext context) {
    // TODO: Implement filter options
  }

  void _showSalesReport(BuildContext context) {
    // TODO: Navigate to sales analytics
  }

  void _showNewSaleDialog(BuildContext context) {
    // TODO: Implement new sale dialog
  }

  Future<void> _refreshData() async {
    _loadSalesData();
  }
}
