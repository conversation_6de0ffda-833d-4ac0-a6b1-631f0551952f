import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../core/constants/app_colors.dart';

class HelpSupportModal extends StatefulWidget {
  const HelpSupportModal({super.key});

  @override
  State<HelpSupportModal> createState() => _HelpSupportModalState();
}

class _HelpSupportModalState extends State<HelpSupportModal>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  List<HelpItem> get _helpItems => [
    HelpItem(
      icon: Icons.help_outline_rounded,
      title: 'Getting Started',
      subtitle: 'Learn the basics of using Rekodi',
      onTap: () => _showGettingStarted(),
    ),
    HelpItem(
      icon: Icons.account_balance_wallet_rounded,
      title: 'Managing Budgets',
      subtitle: 'How to create and track your budgets',
      onTap: () => _showBudgetHelp(),
    ),
    HelpItem(
      icon: Icons.receipt_rounded,
      title: 'Transaction Management',
      subtitle: 'Adding, editing, and categorizing transactions',
      onTap: () => _showTransactionHelp(),
    ),
    HelpItem(
      icon: Icons.analytics_rounded,
      title: 'Reports & Analytics',
      subtitle: 'Understanding your financial reports',
      onTap: () => _showReportsHelp(),
    ),
    HelpItem(
      icon: Icons.security_rounded,
      title: 'Security & Privacy',
      subtitle: 'Keeping your data safe and secure',
      onTap: () => _showSecurityHelp(),
    ),
    HelpItem(
      icon: Icons.sync_rounded,
      title: 'Data Sync & Backup',
      subtitle: 'Syncing data across devices',
      onTap: () => _showSyncHelp(),
    ),
  ];

  List<ContactOption> get _contactOptions => [
    ContactOption(
      icon: Icons.email_rounded,
      title: 'Email Support',
      subtitle: '<EMAIL>',
      onTap: () => _launchEmail(),
    ),
    ContactOption(
      icon: Icons.chat_rounded,
      title: 'Live Chat',
      subtitle: 'Chat with our support team',
      onTap: () => _openLiveChat(),
    ),
    ContactOption(
      icon: Icons.phone_rounded,
      title: 'Phone Support',
      subtitle: '+****************',
      onTap: () => _launchPhone(),
    ),
    ContactOption(
      icon: Icons.bug_report_rounded,
      title: 'Report a Bug',
      subtitle: 'Help us improve the app',
      onTap: () => _reportBug(),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Container(
          color: Colors.black.withOpacity(0.5 * _fadeAnimation.value),
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 1),
              end: Offset.zero,
            ).animate(_slideAnimation),
            child: DraggableScrollableSheet(
              initialChildSize: 0.85,
              minChildSize: 0.7,
              maxChildSize: 0.95,
              builder: (context, scrollController) {
                return Container(
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
                  ),
                  child: Column(
                    children: [
                      // Handle bar
                      Container(
                        margin: const EdgeInsets.only(top: 12),
                        width: 40,
                        height: 4,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      
                      // Header
                      Padding(
                        padding: const EdgeInsets.all(24),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: AppColors.primary.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                Icons.help_center_rounded,
                                color: AppColors.primary,
                                size: 24,
                              ),
                            ),
                            const SizedBox(width: 16),
                            const Expanded(
                              child: Text(
                                'Help & Support',
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                            ),
                            IconButton(
                              onPressed: _closeModal,
                              icon: const Icon(Icons.close_rounded),
                              style: IconButton.styleFrom(
                                backgroundColor: Colors.grey[100],
                                foregroundColor: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Content
                      Expanded(
                        child: SingleChildScrollView(
                          controller: scrollController,
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildSectionHeader('Frequently Asked Questions'),
                              ..._helpItems.map((item) => _buildHelpItem(item)),
                              
                              const SizedBox(height: 32),
                              
                              _buildSectionHeader('Contact Support'),
                              ..._contactOptions.map((option) => _buildContactOption(option)),
                              
                              const SizedBox(height: 32),
                              
                              _buildAppInfo(),
                              const SizedBox(height: 24),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Colors.grey[800],
        ),
      ),
    );
  }

  Widget _buildHelpItem(HelpItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            item.icon,
            color: AppColors.primary,
            size: 20,
          ),
        ),
        title: Text(
          item.title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        subtitle: Text(
          item.subtitle,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios_rounded,
          size: 16,
          color: Colors.grey[400],
        ),
        onTap: () {
          HapticFeedback.lightImpact();
          item.onTap();
        },
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        tileColor: Colors.grey[50],
      ),
    );
  }

  Widget _buildContactOption(ContactOption option) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.secondary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            option.icon,
            color: AppColors.secondary,
            size: 20,
          ),
        ),
        title: Text(
          option.title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        subtitle: Text(
          option.subtitle,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        trailing: Icon(
          Icons.open_in_new_rounded,
          size: 16,
          color: Colors.grey[400],
        ),
        onTap: () {
          HapticFeedback.lightImpact();
          option.onTap();
        },
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        tileColor: Colors.grey[50],
      ),
    );
  }

  Widget _buildAppInfo() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [AppColors.primary, AppColors.secondary],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.info_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Text(
                  'App Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Version',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
              const Text(
                '1.0.0',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Build',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
              const Text(
                '100',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Help section methods
  void _showGettingStarted() {
    _showHelpDialog(
      'Getting Started',
      'Welcome to Rekodi! Here are the basics:\n\n'
      '• Create your first account (Personal or Business)\n'
      '• Add your income and expenses\n'
      '• Set up budgets to track your spending\n'
      '• View reports to understand your finances\n'
      '• Use the scanner to quickly add receipts',
    );
  }

  void _showBudgetHelp() {
    _showHelpDialog(
      'Managing Budgets',
      'Budgets help you control your spending:\n\n'
      '• Tap the + button to create a new budget\n'
      '• Choose a category and set your limit\n'
      '• Select weekly, monthly, or yearly periods\n'
      '• Monitor progress with visual indicators\n'
      '• Get alerts when approaching limits',
    );
  }

  void _showTransactionHelp() {
    _showHelpDialog(
      'Transaction Management',
      'Managing your transactions:\n\n'
      '• Use the FAB to quickly add transactions\n'
      '• Scan receipts for automatic entry\n'
      '• Categorize transactions for better tracking\n'
      '• Edit or delete transactions as needed\n'
      '• Set up recurring transactions for regular payments',
    );
  }

  void _showReportsHelp() {
    _showHelpDialog(
      'Reports & Analytics',
      'Understanding your financial reports:\n\n'
      '• View spending trends over time\n'
      '• Analyze category breakdowns\n'
      '• Compare income vs expenses\n'
      '• Filter reports by date and category\n'
      '• Export data for external analysis',
    );
  }

  void _showSecurityHelp() {
    _showHelpDialog(
      'Security & Privacy',
      'Your data security is our priority:\n\n'
      '• All data is encrypted and stored securely\n'
      '• Use biometric authentication for extra security\n'
      '• Regular security updates and patches\n'
      '• No sharing of personal financial data\n'
      '• Local storage with optional cloud sync',
    );
  }

  void _showSyncHelp() {
    _showHelpDialog(
      'Data Sync & Backup',
      'Keep your data safe and synchronized:\n\n'
      '• Enable cloud sync in settings\n'
      '• Data automatically backs up daily\n'
      '• Sync across multiple devices\n'
      '• Export data for manual backups\n'
      '• Restore from backup if needed',
    );
  }

  void _showHelpDialog(String title, String content) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        content: Text(
          content,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[700],
            height: 1.5,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Got it',
              style: TextStyle(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }

  // Contact methods
  void _launchEmail() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      query: 'subject=Rekodi Support Request',
    );

    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    } else {
      Get.snackbar(
        'Error',
        'Could not open email client',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
      );
    }
  }

  void _launchPhone() async {
    final Uri phoneUri = Uri(scheme: 'tel', path: '+15551234567');

    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    } else {
      Get.snackbar(
        'Error',
        'Could not open phone dialer',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
      );
    }
  }

  void _openLiveChat() {
    Get.dialog(
      AlertDialog(
        title: const Text('Contact Support'),
        content: const Text(
          'For immediate assistance, please:\n\n'
          '• Email <NAME_EMAIL>\n'
          '• Call our support line: +254-XXX-XXXX\n'
          '• Visit our website: www.rekodi.app\n\n'
          'Live chat will be available soon!',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              // TODO: Open email client or website
            },
            child: const Text('Contact Us'),
          ),
        ],
      ),
    );
  }

  void _reportBug() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'Report a Bug',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        content: const Text(
          'To report a bug, please email <NAME_EMAIL> with:\n\n'
          '• Description of the issue\n'
          '• Steps to reproduce\n'
          '• Screenshots if applicable\n'
          '• Your device information\n\n'
          'We appreciate your help in making Rekodi better!',
          style: TextStyle(
            fontSize: 14,
            height: 1.5,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _launchEmail();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: const Text('Send Email'),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }

  void _closeModal() {
    _animationController.reverse().then((_) {
      Navigator.of(context).pop();
    });
  }
}

class HelpItem {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  HelpItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });
}

class ContactOption {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  ContactOption({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });
}
