import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../../../core/constants/app_colors.dart';
import '../../../core/services/theme_service.dart';
import '../../../core/services/account_service.dart';
import '../../../core/services/subscription_service.dart';
import '../../../core/services/data_management_service.dart';
import '../../../core/services/data_export_service.dart';

import '../../../core/services/biometric_service.dart';
import '../../../core/services/currency_service.dart';
import '../../../core/services/auth_service.dart';
import '../../../core/services/sync_service.dart';
import '../../../core/services/personal_transaction_service.dart';
import '../../../core/models/account_type.dart';

import '../../common/widgets/common_app_bar.dart';
import '../../common/widgets/account_drawer.dart';
import '../../transactions/screens/recurring_transactions_screen.dart';
import '../widgets/profile_avatar_widget.dart';
import '../widgets/notification_settings_modal.dart';
import '../widgets/help_support_modal.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final BiometricService _biometricService = Get.find();
  final RxBool _appLockEnabled = false.obs;

  @override
  void initState() {
    super.initState();
    _loadAppLockSetting();
  }

  void _loadAppLockSetting() {
    // Load app lock setting from storage
    final storage = GetStorage();
    _appLockEnabled.value = storage.read('app_lock_enabled') ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final themeService = ThemeService.to;
      final accountService = AccountService.to;
      final subscriptionService = SubscriptionService.to;
      final currentAccount = accountService.currentAccount;

      return Scaffold(
        backgroundColor: themeService.backgroundColor,
        appBar: CommonAppBar(
          title: 'Profile',
          showAccountInfo: true,
          actions: [
            IconButton(
              onPressed: () {
                Get.toNamed('/theme-settings');
              },
              icon: const Icon(Icons.settings_rounded),
            ),
          ],
        ),
        endDrawer: const AccountDrawer(),
        body: currentAccount == null
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _refreshData,
              color: themeService.primaryColor,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  children: [
                    // Profile Header
                    _buildProfileHeader(currentAccount, themeService, subscriptionService),

                    const SizedBox(height: 20),

                    // Account Information
                    _buildAccountInfo(currentAccount, themeService),

                    const SizedBox(height: 20),

                    // Settings & Preferences
                    _buildSettingsSection(themeService),

                    const SizedBox(height: 20),

                    // Subscription & Premium Features
                    _buildSubscriptionSection(currentAccount, themeService, subscriptionService),

                    const SizedBox(height: 20),

                    // Account Actions
                    _buildAccountActions(themeService),

                    const SizedBox(height: 100), // Space for bottom nav
                  ],
                ),
              ),
            ),
      );
    });
  }

  Widget _buildProfileHeader(dynamic currentAccount, ThemeService themeService, SubscriptionService subscriptionService) {
    final isBusinessAccount = AccountService.to.currentAccountType == AccountType.business;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            themeService.primaryColor,
            themeService.primaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: themeService.primaryColor.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // Profile Avatar
          const ProfileAvatarWidget(
            size: 100,
            showEditButton: true,
          ),

          const SizedBox(height: 16),

          // Account Name
          Text(
            currentAccount.name,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),

          const SizedBox(height: 4),

          // Account Type & Email
          Text(
            currentAccount.email,
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),

          const SizedBox(height: 8),

          // Account Type Badge
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              isBusinessAccount ? 'Business Account' : 'Personal Account',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),

          // Premium Badge
          if (currentAccount.isPremium) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.amber.withOpacity(0.9),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.star_rounded, color: Colors.white, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    'Premium',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAccountInfo(dynamic currentAccount, ThemeService themeService) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Account Information',
            style: TextStyle(
              color: themeService.textPrimaryColor,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _buildInfoCard(
            icon: Icons.email_rounded,
            title: 'Email',
            value: currentAccount.email,
            themeService: themeService,
          ),
          _buildInfoCard(
            icon: Icons.account_circle_rounded,
            title: 'Account Type',
            value: AccountService.to.currentAccountType.displayName,
            themeService: themeService,
          ),
          Obx(() {
            final currencyService = CurrencyService.to;
            return _buildInfoCard(
              icon: Icons.attach_money_rounded,
              title: 'Currency',
              value: '${currencyService.currentCurrency.value.name} (${currencyService.currentCurrency.value.code})',
              themeService: themeService,
            );
          }),
          _buildInfoCard(
            icon: Icons.access_time_rounded,
            title: 'Member Since',
            value: _formatDate(currentAccount.createdAt),
            themeService: themeService,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard({
    required IconData icon,
    required String title,
    required String value,
    required ThemeService themeService,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: themeService.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: themeService.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: themeService.primaryColor, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: themeService.textSecondaryColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: TextStyle(
                    color: themeService.textPrimaryColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(ThemeService themeService) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Settings & Preferences',
            style: TextStyle(
              color: themeService.textPrimaryColor,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _buildSettingsItem(
            icon: Icons.palette_rounded,
            title: 'Theme Settings',
            subtitle: 'Customize app appearance',
            onTap: () => Get.toNamed('/theme-settings'),
            themeService: themeService,
          ),
          _buildSettingsItem(
            icon: Icons.attach_money_rounded,
            title: 'Currency Settings',
            subtitle: 'Change your preferred currency',
            onTap: _showCurrencySettings,
            themeService: themeService,
          ),
          _buildSettingsItem(
            icon: Icons.notifications_rounded,
            title: 'Notifications',
            subtitle: 'Manage notification preferences',
            onTap: _showNotificationSettings,
            themeService: themeService,
          ),
          _buildSettingsItem(
            icon: Icons.security_rounded,
            title: 'Privacy & Security',
            subtitle: 'Biometric authentication & security',
            onTap: _showSecuritySettings,
            themeService: themeService,
          ),
          _buildSettingsItem(
            icon: Icons.help_center_rounded,
            title: 'Help & Support',
            subtitle: 'Get help and contact support',
            onTap: _showHelpSupport,
            themeService: themeService,
          ),
          _buildSettingsItem(
            icon: Icons.backup_rounded,
            title: 'Data Management',
            subtitle: 'Backup, export, and clear data',
            onTap: _showDataManagement,
            themeService: themeService,
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required ThemeService themeService,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: themeService.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: themeService.primaryColor, size: 20),
        ),
        title: Text(
          title,
          style: TextStyle(
            color: themeService.textPrimaryColor,
            fontWeight: FontWeight.w600,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            color: themeService.textSecondaryColor,
            fontSize: 12,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        trailing: Icon(
          Icons.chevron_right_rounded,
          color: themeService.textSecondaryColor,
        ),
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        tileColor: themeService.surfaceColor,
      ),
    );
  }

  Widget _buildSubscriptionSection(dynamic currentAccount, ThemeService themeService, SubscriptionService subscriptionService) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Subscription',
            style: TextStyle(
              color: themeService.textPrimaryColor,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: currentAccount.isPremium ? Colors.amber.withOpacity(0.1) : themeService.surfaceColor,
              borderRadius: BorderRadius.circular(12),
              border: currentAccount.isPremium
                  ? Border.all(color: Colors.amber.withOpacity(0.3))
                  : null,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      currentAccount.isPremium ? Icons.star_rounded : Icons.star_border_rounded,
                      color: currentAccount.isPremium ? Colors.amber : themeService.textSecondaryColor,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      currentAccount.isPremium ? 'Premium Member' : 'Free Plan',
                      style: TextStyle(
                        color: themeService.textPrimaryColor,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  currentAccount.isPremium
                      ? 'Enjoy unlimited features and priority support'
                      : 'Upgrade to unlock premium features',
                  style: TextStyle(
                    color: themeService.textSecondaryColor,
                    fontSize: 14,
                  ),
                ),
                if (currentAccount.isPremium && currentAccount.premiumExpiresAt != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    'Expires: ${_formatDate(currentAccount.premiumExpiresAt!)}',
                    style: TextStyle(
                      color: themeService.textSecondaryColor,
                      fontSize: 12,
                    ),
                  ),
                ],
                if (!currentAccount.isPremium) ...[
                  const SizedBox(height: 12),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        _showSubscriptionPlans();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.amber,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Upgrade to Premium'),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountActions(ThemeService themeService) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Account Actions',
            style: TextStyle(
              color: themeService.textPrimaryColor,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _buildActionItem(
            icon: Icons.repeat_rounded,
            title: 'Recurring Transactions',
            subtitle: 'Manage recurring transactions',
            onTap: () {
              Get.to(() => const RecurringTransactionsScreen());
            },
            themeService: themeService,
          ),
          _buildActionItem(
            icon: Icons.backup_rounded,
            title: 'Backup Data',
            subtitle: 'Export your data',
            onTap: () {
              DataManagementService.to.exportAccountData();
            },
            themeService: themeService,
          ),
          _buildActionItem(
            icon: Icons.sync_rounded,
            title: 'Sync Data',
            subtitle: 'Sync with cloud',
            onTap: () {
              _performDataSync();
            },
            themeService: themeService,
          ),
          _buildActionItem(
            icon: Icons.clear_all_rounded,
            title: 'Clear Account Data',
            subtitle: 'Delete all data for this account',
            onTap: () {
              DataManagementService.to.showClearDataDialog(context, allData: false);
            },
            themeService: themeService,
            isDestructive: true,
          ),
          _buildActionItem(
            icon: Icons.help_rounded,
            title: 'Help & Support',
            subtitle: 'Get help and contact support',
            onTap: () {
              _showHelpSupport();
            },
            themeService: themeService,
          ),
          _buildActionItem(
            icon: Icons.logout_rounded,
            title: 'Sign Out',
            subtitle: 'Sign out of your account',
            onTap: () {
              _showSignOutDialog();
            },
            themeService: themeService,
            isDestructive: true,
          ),
        ],
      ),
    );
  }

  Widget _buildActionItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required ThemeService themeService,
    bool isDestructive = false,
  }) {
    final color = isDestructive ? Colors.red : themeService.primaryColor;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isDestructive ? Colors.red : themeService.textPrimaryColor,
            fontWeight: FontWeight.w600,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            color: themeService.textSecondaryColor,
            fontSize: 12,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        trailing: Icon(
          Icons.chevron_right_rounded,
          color: themeService.textSecondaryColor,
        ),
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        tileColor: themeService.surfaceColor,
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showSignOutDialog() {
    HapticFeedback.lightImpact();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.logout_rounded,
              color: Colors.red,
              size: 24,
            ),
            const SizedBox(width: 8),
            const Text(
              'Sign Out',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),
        content: const Text(
          'Are you sure you want to sign out? You will need to sign in again to access your account.',
          style: TextStyle(height: 1.5),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(
                color: ThemeService.to.textSecondaryColor,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _performSignOut();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Sign Out'),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }

  Future<void> _performSignOut() async {
    try {
      // Show loading indicator
      Get.dialog(
        const Center(
          child: CircularProgressIndicator(),
        ),
        barrierDismissible: false,
      );

      // Perform sign out
      final authService = Get.find<AuthService>();
      await authService.signOut();

      // Close loading dialog
      Get.back();

      // Navigate to login screen
      Get.offAllNamed('/login');

      Get.snackbar(
        'Signed Out',
        'You have been successfully signed out',
        backgroundColor: AppColors.success,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      // Close loading dialog if still open
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        'Error',
        'Failed to sign out: ${e.toString()}',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> _refreshData() async {
    try {
      // Refresh account data
      final accountService = AccountService.to;
      if (Get.find<AuthService>().currentUser != null) {
        await accountService.loadUserAccounts(Get.find<AuthService>().currentUser!.id);
      }

      // Refresh subscription data
      SubscriptionService.to;
      // Note: checkSubscriptionStatus method to be implemented

      // Refresh currency data
      CurrencyService.to;
      // Note: loadSavedCurrency method to be implemented

      // Show success message
      Get.snackbar(
        'Refreshed',
        'Profile data has been refreshed',
        backgroundColor: ThemeService.to.primaryColor,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to refresh data: ${e.toString()}',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> _performDataSync() async {
    try {
      HapticFeedback.lightImpact();

      // Show loading dialog
      Get.dialog(
        AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                'Syncing data with cloud...',
                style: TextStyle(
                  color: ThemeService.to.textPrimaryColor,
                ),
              ),
            ],
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        barrierDismissible: false,
      );

      // Check if sync service is available
      if (Get.isRegistered<SyncService>()) {
        final syncService = Get.find<SyncService>();
        // Use existing sync method
        await syncService.syncAll();
      } else {
        // Fallback: sync individual services
        if (Get.isRegistered<PersonalTransactionService>()) {
          final transactionService = Get.find<PersonalTransactionService>();
          await transactionService.loadTransactionsFromAppwrite();
        }

        // Add small delay for user experience
        await Future.delayed(const Duration(seconds: 1));
      }

      // Close loading dialog
      Get.back();

      Get.snackbar(
        'Sync Complete',
        'Your data has been synchronized with the cloud',
        backgroundColor: AppColors.success,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
        icon: const Icon(Icons.cloud_done_rounded, color: Colors.white),
      );
    } catch (e) {
      // Close loading dialog if still open
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        'Sync Failed',
        'Failed to sync data: ${e.toString()}',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
        icon: const Icon(Icons.cloud_off_rounded, color: Colors.white),
      );
    }
  }

  void _showNotificationSettings() {
    HapticFeedback.lightImpact();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const NotificationSettingsModal(),
    );
  }

  void _showCurrencySettings() {
    HapticFeedback.lightImpact();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildCurrencySettingsModal(),
    );
  }

  void _showSecuritySettings() {
    HapticFeedback.lightImpact();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'Security Settings',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.fingerprint_rounded),
              title: const Text('Biometric Authentication'),
              subtitle: const Text('Use fingerprint or face ID'),
              trailing: Obx(() => Switch(
                value: _biometricService.isEnabled,
                onChanged: (value) async {
                  if (value) {
                    await _biometricService.enableBiometric();
                  } else {
                    await _biometricService.disableBiometric();
                  }
                },
                activeColor: AppColors.primary,
              )),
            ),
            ListTile(
              leading: const Icon(Icons.lock_rounded),
              title: const Text('App Lock'),
              subtitle: const Text('Require authentication to open app'),
              trailing: Obx(() => Switch(
                value: _appLockEnabled.value,
                onChanged: (value) async {
                  await _toggleAppLock(value);
                },
                activeColor: AppColors.primary,
              )),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }

  void _showHelpSupport() {
    HapticFeedback.lightImpact();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const HelpSupportModal(),
    );
  }

  void _showDataManagement() {
    HapticFeedback.lightImpact();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'Data Management',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.backup_rounded),
              title: const Text('Backup Data'),
              subtitle: const Text('Create a backup of your data'),
              onTap: () {
                Navigator.pop(context);
                _backupData();
              },
            ),
            ListTile(
              leading: const Icon(Icons.download_rounded),
              title: const Text('Export Data'),
              subtitle: const Text('Export data to CSV'),
              onTap: () {
                Navigator.pop(context);
                _exportData();
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete_forever_rounded),
              title: const Text('Clear All Data'),
              subtitle: const Text('Permanently delete all data'),
              onTap: () {
                Navigator.pop(context);
                _showClearDataConfirmation();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }

  void _backupData() async {
    try {
      HapticFeedback.lightImpact();

      // Show loading dialog
      Get.dialog(
        AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                'Creating backup...',
                style: TextStyle(
                  color: ThemeService.to.textPrimaryColor,
                ),
              ),
            ],
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        barrierDismissible: false,
      );

      // Perform backup using DataManagementService
      final dataService = DataManagementService.to;
      await dataService.exportAccountData();

      // Close loading dialog
      Get.back();

      Get.snackbar(
        'Backup Complete',
        'Your data has been backed up successfully',
        backgroundColor: AppColors.success,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
        icon: const Icon(Icons.backup_rounded, color: Colors.white),
      );
    } catch (e) {
      // Close loading dialog if still open
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        'Backup Failed',
        'Failed to backup data: ${e.toString()}',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
        icon: const Icon(Icons.error_rounded, color: Colors.white),
      );
    }
  }

  void _exportData() async {
    try {
      HapticFeedback.lightImpact();

      // Show export options dialog
      final exportFormat = await showDialog<String>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text(
            'Export Format',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.table_chart_rounded),
                title: const Text('CSV Format'),
                subtitle: const Text('Spreadsheet compatible'),
                onTap: () => Navigator.pop(context, 'csv'),
              ),
              ListTile(
                leading: const Icon(Icons.code_rounded),
                title: const Text('JSON Format'),
                subtitle: const Text('Raw data format'),
                onTap: () => Navigator.pop(context, 'json'),
              ),
              ListTile(
                leading: const Icon(Icons.picture_as_pdf_rounded),
                title: const Text('PDF Report'),
                subtitle: const Text('Formatted report'),
                onTap: () => Navigator.pop(context, 'pdf'),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ],
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
      );

      if (exportFormat == null) return;

      // Show loading dialog
      Get.dialog(
        AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                'Exporting data to ${exportFormat.toUpperCase()}...',
                style: TextStyle(
                  color: ThemeService.to.textPrimaryColor,
                ),
              ),
            ],
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        barrierDismissible: false,
      );

      // Perform export using DataExportService
      final exportService = DataExportService.to;
      bool success = false;

      switch (exportFormat) {
        case 'csv':
          success = await exportService.exportData(
            format: ExportFormat.csv,
            type: ExportType.full,
          );
          break;
        case 'json':
          success = await exportService.exportData(
            format: ExportFormat.json,
            type: ExportType.full,
          );
          break;
        case 'pdf':
          success = await exportService.exportData(
            format: ExportFormat.pdf,
            type: ExportType.full,
          );
          break;
      }

      if (!success) {
        throw Exception('Export failed');
      }

      // Close loading dialog
      Get.back();

      Get.snackbar(
        'Export Complete',
        'Data exported to ${exportFormat.toUpperCase()} successfully',
        backgroundColor: AppColors.success,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
        icon: const Icon(Icons.download_done_rounded, color: Colors.white),
      );
    } catch (e) {
      // Close loading dialog if still open
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        'Export Failed',
        'Failed to export data: ${e.toString()}',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
        icon: const Icon(Icons.error_rounded, color: Colors.white),
      );
    }
  }

  void _showClearDataConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'Clear All Data',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.error,
          ),
        ),
        content: const Text(
          'This will permanently delete all your financial data including transactions, budgets, and accounts. This action cannot be undone.\n\nAre you sure you want to continue?',
          style: TextStyle(height: 1.5),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _clearAllData();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Clear Data'),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }

  void _clearAllData() async {
    try {
      final dataService = DataManagementService.to;
      await dataService.clearAllData();

      Get.snackbar(
        'Success',
        'All data cleared successfully',
        backgroundColor: AppColors.success,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to clear data: ${e.toString()}',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
      );
    }
  }

  Widget _buildCurrencySettingsModal() {
    return Obx(() {
      final themeService = ThemeService.to;
      final currencyService = CurrencyService.to;

      return Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        decoration: BoxDecoration(
          color: themeService.surfaceColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: themeService.textPrimaryColor.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Icon(
                    Icons.attach_money_rounded,
                    color: themeService.primaryColor,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Currency Settings',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: themeService.textPrimaryColor,
                          ),
                        ),
                        Text(
                          'Choose your preferred currency',
                          style: TextStyle(
                            fontSize: 14,
                            color: themeService.textPrimaryColor.withOpacity(0.6),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Current Currency Display
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 20),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [themeService.primaryColor, themeService.primaryColor.withOpacity(0.8)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Text(
                    currencyService.currentCurrency.value.flag,
                    style: const TextStyle(fontSize: 24),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Current Currency',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.8),
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          '${currencyService.currentCurrency.value.name} (${currencyService.currentCurrency.value.code})',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    currencyService.currentCurrency.value.symbol,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // Popular Currencies
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Popular Currencies',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: themeService.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ...currencyService.getPopularCurrencies().map((currency) {
                      final isSelected = currencyService.currentCurrency.value.code == currency.code;
                      return Container(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: ListTile(
                          leading: Text(
                            currency.flag,
                            style: const TextStyle(fontSize: 24),
                          ),
                          title: Text(
                            currency.name,
                            style: TextStyle(
                              color: themeService.textPrimaryColor,
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          subtitle: Text(
                            '${currency.symbol} ${currency.code}',
                            style: TextStyle(
                              color: themeService.textPrimaryColor.withOpacity(0.6),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          trailing: isSelected
                              ? Icon(
                                  Icons.check_circle_rounded,
                                  color: themeService.primaryColor,
                                )
                              : null,
                          onTap: () async {
                            await currencyService.setCurrentCurrency(currency);
                            Navigator.of(context).pop();
                            Get.snackbar(
                              'Currency Updated',
                              'Currency changed to ${currency.name}',
                              backgroundColor: themeService.primaryColor,
                              colorText: Colors.white,
                              snackPosition: SnackPosition.BOTTOM,
                            );
                          },
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          tileColor: isSelected
                              ? themeService.primaryColor.withOpacity(0.1)
                              : null,
                        ),
                      );
                    }).toList(),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),
          ],
        ),
      );
    });
  }

  void _showSubscriptionPlans() {
    HapticFeedback.lightImpact();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildSubscriptionPlansModal(),
    );
  }

  Widget _buildSubscriptionPlansModal() {
    return Obx(() {
      final themeService = ThemeService.to;

      return Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: themeService.surfaceColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: themeService.textPrimaryColor.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Icon(
                    Icons.star_rounded,
                    color: Colors.amber,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Upgrade to Premium',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: themeService.textPrimaryColor,
                          ),
                        ),
                        Text(
                          'Unlock all features and get priority support',
                          style: TextStyle(
                            fontSize: 14,
                            color: themeService.textPrimaryColor.withOpacity(0.6),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Premium Features
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Premium Features',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: themeService.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Feature list
                    ...[
                      'Unlimited transactions and budgets',
                      'Advanced analytics and insights',
                      'Receipt scanning with AI',
                      'Multi-currency support',
                      'Cloud backup and sync',
                      'Export to multiple formats',
                      'Priority customer support',
                      'Ad-free experience',
                    ].map((feature) => _buildFeatureItem(feature, themeService)),

                    const SizedBox(height: 24),

                    // Pricing
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.amber, Colors.amber.withOpacity(0.8)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Column(
                        children: [
                          const Text(
                            'Premium Plan',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            '\$9.99/month',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Text(
                            'or \$99.99/year (Save 17%)',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: () {
                                Navigator.pop(context);
                                _startSubscription();
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.white,
                                foregroundColor: Colors.amber,
                                padding: const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: const Text(
                                'Start Free Trial',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildFeatureItem(String feature, ThemeService themeService) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            Icons.check_circle_rounded,
            color: Colors.green,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              feature,
              style: TextStyle(
                color: themeService.textPrimaryColor,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _startSubscription() async {
    try {
      HapticFeedback.lightImpact();

      // Show subscription confirmation dialog
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.star_rounded,
                color: Colors.amber,
                size: 24,
              ),
              const SizedBox(width: 8),
              const Text(
                'Start Premium Trial',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Start your 7-day free trial of Rekodi Premium:',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 12),
              const Text('• Unlimited transactions and budgets'),
              const Text('• Advanced analytics and insights'),
              const Text('• Receipt scanning with AI'),
              const Text('• Multi-currency support'),
              const Text('• Cloud backup and sync'),
              const Text('• Priority customer support'),
              const SizedBox(height: 12),
              Text(
                'After the trial, you\'ll be charged \$9.99/month. Cancel anytime.',
                style: TextStyle(
                  fontSize: 12,
                  color: ThemeService.to.textSecondaryColor,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: Text(
                'Cancel',
                style: TextStyle(
                  color: ThemeService.to.textSecondaryColor,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.amber,
                foregroundColor: Colors.white,
              ),
              child: const Text('Start Trial'),
            ),
          ],
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
      );

      if (confirmed != true) return;

      // Show loading dialog
      Get.dialog(
        AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.amber),
              ),
              const SizedBox(height: 16),
              Text(
                'Starting your premium trial...',
                style: TextStyle(
                  color: ThemeService.to.textPrimaryColor,
                ),
              ),
            ],
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        barrierDismissible: false,
      );

      // Simulate subscription process
      await Future.delayed(const Duration(seconds: 2));

      // Update subscription status
      final subscriptionService = SubscriptionService.to;
      await subscriptionService.startPremiumTrial();

      // Close loading dialog
      Get.back();

      Get.snackbar(
        'Premium Trial Started!',
        'Welcome to Rekodi Premium! Your 7-day trial has begun.',
        backgroundColor: Colors.amber,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
        icon: const Icon(Icons.star_rounded, color: Colors.white),
        duration: const Duration(seconds: 4),
      );

      // Refresh the profile screen to show premium status
      setState(() {});

    } catch (e) {
      // Close loading dialog if still open
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        'Subscription Error',
        'Failed to start premium trial: ${e.toString()}',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
        icon: const Icon(Icons.error_rounded, color: Colors.white),
      );
    }
  }

  Future<void> _toggleAppLock(bool enabled) async {
    try {
      HapticFeedback.lightImpact();

      if (enabled) {
        // Check if biometric authentication is available
        final isAvailable = await _biometricService.isBiometricAvailable();

        if (!isAvailable) {
          Get.snackbar(
            'Biometric Not Available',
            'Biometric authentication is not available on this device',
            backgroundColor: AppColors.warning,
            colorText: Colors.white,
            snackPosition: SnackPosition.BOTTOM,
          );
          return;
        }

        // Authenticate before enabling app lock
        final isAuthenticated = await _biometricService.authenticate(
          reason: 'Enable app lock to secure your financial data',
        );

        if (!isAuthenticated) {
          Get.snackbar(
            'Authentication Failed',
            'App lock was not enabled',
            backgroundColor: AppColors.error,
            colorText: Colors.white,
            snackPosition: SnackPosition.BOTTOM,
          );
          return;
        }
      }

      // Save the setting
      final storage = GetStorage();
      await storage.write('app_lock_enabled', enabled);
      _appLockEnabled.value = enabled;

      Get.snackbar(
        enabled ? 'App Lock Enabled' : 'App Lock Disabled',
        enabled
          ? 'Your app is now secured with biometric authentication'
          : 'App lock has been disabled',
        backgroundColor: enabled ? AppColors.success : AppColors.info,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
        icon: Icon(
          enabled ? Icons.lock_rounded : Icons.lock_open_rounded,
          color: Colors.white,
        ),
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to ${enabled ? 'enable' : 'disable'} app lock: ${e.toString()}',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
}
