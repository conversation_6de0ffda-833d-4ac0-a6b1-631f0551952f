import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/models/confidence_review.dart';

class ReviewFilterModal extends StatefulWidget {
  final RxList<ConfidenceLevel> selectedConfidenceLevels;
  final RxList<ReviewStatus> selectedStatuses;
  final bool showOnlyUrgent;
  final Function(List<ConfidenceLevel>, List<ReviewStatus>, bool) onApplyFilters;

  const ReviewFilterModal({
    super.key,
    required this.selectedConfidenceLevels,
    required this.selectedStatuses,
    required this.showOnlyUrgent,
    required this.onApplyFilters,
  });

  @override
  State<ReviewFilterModal> createState() => _ReviewFilterModalState();
}

class _ReviewFilterModalState extends State<ReviewFilterModal>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  List<ConfidenceLevel> _selectedConfidenceLevels = [];
  List<ReviewStatus> _selectedStatuses = [];
  bool _showOnlyUrgent = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    // Initialize with current values
    _selectedConfidenceLevels = List.from(widget.selectedConfidenceLevels);
    _selectedStatuses = List.from(widget.selectedStatuses);
    _showOnlyUrgent = widget.showOnlyUrgent;

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Container(
          color: Colors.black.withOpacity(0.5 * _fadeAnimation.value),
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 1),
              end: Offset.zero,
            ).animate(_slideAnimation),
            child: DraggableScrollableSheet(
              initialChildSize: 0.7,
              minChildSize: 0.5,
              maxChildSize: 0.9,
              builder: (context, scrollController) {
                return Container(
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
                  ),
                  child: Column(
                    children: [
                      // Handle
                      Container(
                        margin: const EdgeInsets.only(top: 12),
                        width: 40,
                        height: 4,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      
                      // Header
                      Padding(
                        padding: const EdgeInsets.all(24),
                        child: Row(
                          children: [
                            const Text(
                              'Filter Reviews',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.w700,
                                color: AppColors.textPrimary,
                              ),
                            ),
                            const Spacer(),
                            IconButton(
                              onPressed: _closeModal,
                              icon: const Icon(Icons.close),
                              style: IconButton.styleFrom(
                                backgroundColor: Colors.grey[100],
                                foregroundColor: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Filter Content
                      Expanded(
                        child: SingleChildScrollView(
                          controller: scrollController,
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildConfidenceLevelSection(),
                              const SizedBox(height: 32),
                              _buildStatusSection(),
                              const SizedBox(height: 32),
                              _buildUrgentSection(),
                              const SizedBox(height: 32),
                              _buildActionButtons(),
                              const SizedBox(height: 24),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildConfidenceLevelSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Confidence Level',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: ConfidenceLevel.values.map((level) {
            final isSelected = _selectedConfidenceLevels.contains(level);
            return FilterChip(
              label: Text(_getConfidenceLevelText(level)),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _selectedConfidenceLevels.add(level);
                  } else {
                    _selectedConfidenceLevels.remove(level);
                  }
                });
              },
              selectedColor: _getConfidenceColor(level).withOpacity(0.2),
              checkmarkColor: _getConfidenceColor(level),
              backgroundColor: Colors.grey[100],
              labelStyle: TextStyle(
                color: isSelected ? _getConfidenceColor(level) : AppColors.textSecondary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildStatusSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Review Status',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: ReviewStatus.values.map((status) {
            final isSelected = _selectedStatuses.contains(status);
            return FilterChip(
              label: Text(_getStatusText(status)),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _selectedStatuses.add(status);
                  } else {
                    _selectedStatuses.remove(status);
                  }
                });
              },
              selectedColor: _getStatusColor(status).withOpacity(0.2),
              checkmarkColor: _getStatusColor(status),
              backgroundColor: Colors.grey[100],
              labelStyle: TextStyle(
                color: isSelected ? _getStatusColor(status) : AppColors.textSecondary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildUrgentSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Priority',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 16),
        SwitchListTile(
          title: const Text('Show only urgent reviews'),
          subtitle: const Text('Reviews with very low confidence that need immediate attention'),
          value: _showOnlyUrgent,
          onChanged: (value) {
            setState(() {
              _showOnlyUrgent = value;
            });
          },
          activeColor: AppColors.primary,
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _resetFilters,
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.textSecondary,
              side: BorderSide(color: Colors.grey[300]!),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text('Reset'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _applyFilters,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text('Apply Filters'),
          ),
        ),
      ],
    );
  }

  void _resetFilters() {
    setState(() {
      _selectedConfidenceLevels.clear();
      _selectedStatuses.clear();
      _showOnlyUrgent = false;
    });
  }

  void _applyFilters() {
    widget.onApplyFilters(_selectedConfidenceLevels, _selectedStatuses, _showOnlyUrgent);
    _closeModal();
  }

  void _closeModal() {
    _animationController.reverse().then((_) {
      Navigator.of(context).pop();
    });
  }

  String _getConfidenceLevelText(ConfidenceLevel level) {
    switch (level) {
      case ConfidenceLevel.veryHigh:
        return 'Very High';
      case ConfidenceLevel.high:
        return 'High';
      case ConfidenceLevel.medium:
        return 'Medium';
      case ConfidenceLevel.low:
        return 'Low';
      case ConfidenceLevel.veryLow:
        return 'Very Low';
    }
  }

  String _getStatusText(ReviewStatus status) {
    switch (status) {
      case ReviewStatus.pending:
        return 'Pending';
      case ReviewStatus.reviewed:
        return 'Reviewed';
      case ReviewStatus.autoAccepted:
        return 'Auto Accepted';
      case ReviewStatus.autoRejected:
        return 'Auto Rejected';
      case ReviewStatus.expired:
        return 'Expired';
    }
  }

  Color _getConfidenceColor(ConfidenceLevel level) {
    switch (level) {
      case ConfidenceLevel.veryHigh:
        return AppColors.success;
      case ConfidenceLevel.high:
        return Colors.lightGreen;
      case ConfidenceLevel.medium:
        return AppColors.warning;
      case ConfidenceLevel.low:
        return Colors.orange;
      case ConfidenceLevel.veryLow:
        return AppColors.error;
    }
  }

  Color _getStatusColor(ReviewStatus status) {
    switch (status) {
      case ReviewStatus.pending:
        return AppColors.warning;
      case ReviewStatus.reviewed:
        return AppColors.success;
      case ReviewStatus.autoAccepted:
        return AppColors.primary;
      case ReviewStatus.autoRejected:
        return AppColors.error;
      case ReviewStatus.expired:
        return AppColors.textSecondary;
    }
  }
}
