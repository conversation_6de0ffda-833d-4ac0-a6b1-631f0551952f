import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/models/confidence_review.dart';

class ConfidenceStatisticsCard extends StatelessWidget {
  final Map<String, dynamic> statistics;

  const ConfidenceStatisticsCard({
    super.key,
    required this.statistics,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildOverviewSection(),
                  const SizedBox(height: 24),
                  _buildConfidenceLevelBreakdown(),
                  const SizedBox(height: 24),
                  _buildStatusBreakdown(),
                  const SizedBox(height: 24),
                  _buildPerformanceMetrics(),
                ],
              ),
            ),
          ),
          _buildCloseButton(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primary.withOpacity(0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.analytics_rounded,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Confidence Statistics',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  'Review performance overview',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewSection() {
    final totalReviews = statistics['totalReviews'] ?? 0;
    final pendingReviews = statistics['pendingReviews'] ?? 0;
    final completedReviews = statistics['completedReviews'] ?? 0;
    final averageConfidence = statistics['averageConfidence'] ?? 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Overview',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Total Reviews',
                totalReviews.toString(),
                Icons.assignment_rounded,
                AppColors.primary,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'Pending',
                pendingReviews.toString(),
                Icons.pending_actions_rounded,
                AppColors.warning,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Completed',
                completedReviews.toString(),
                Icons.check_circle_rounded,
                AppColors.success,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'Avg Confidence',
                '${(averageConfidence * 100).toStringAsFixed(1)}%',
                Icons.trending_up_rounded,
                AppColors.info,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildConfidenceLevelBreakdown() {
    final confidenceLevels = statistics['confidenceLevels'] as Map<String, int>? ?? {};
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Confidence Level Breakdown',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 16),
        ...ConfidenceLevel.values.map((level) {
          final count = confidenceLevels[level.name] ?? 0;
          final color = _getConfidenceColor(level);
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: _buildProgressBar(
              _getConfidenceLevelText(level),
              count,
              color,
              statistics['totalReviews'] ?? 1,
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildStatusBreakdown() {
    final statuses = statistics['statuses'] as Map<String, int>? ?? {};
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Review Status Breakdown',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 16),
        ...ReviewStatus.values.map((status) {
          final count = statuses[status.name] ?? 0;
          final color = _getStatusColor(status);
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: _buildProgressBar(
              _getStatusText(status),
              count,
              color,
              statistics['totalReviews'] ?? 1,
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildPerformanceMetrics() {
    final accuracyRate = statistics['accuracyRate'] ?? 0.0;
    final avgProcessingTime = statistics['avgProcessingTime'] ?? 0.0;
    final autoAcceptanceRate = statistics['autoAcceptanceRate'] ?? 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Performance Metrics',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 16),
        _buildMetricRow('Accuracy Rate', '${(accuracyRate * 100).toStringAsFixed(1)}%'),
        _buildMetricRow('Avg Processing Time', '${avgProcessingTime.toStringAsFixed(1)}s'),
        _buildMetricRow('Auto Acceptance Rate', '${(autoAcceptanceRate * 100).toStringAsFixed(1)}%'),
      ],
    );
  }

  Widget _buildProgressBar(String label, int value, Color color, int total) {
    final percentage = total > 0 ? value / total : 0.0;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.textPrimary,
              ),
            ),
            Text(
              value.toString(),
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: percentage,
          backgroundColor: Colors.grey[200],
          valueColor: AlwaysStoppedAnimation<Color>(color),
        ),
      ],
    );
  }

  Widget _buildMetricRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCloseButton() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: () => Get.back(),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: const Text(
            'Close',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  Color _getConfidenceColor(ConfidenceLevel level) {
    switch (level) {
      case ConfidenceLevel.veryLow:
        return AppColors.error;
      case ConfidenceLevel.low:
        return AppColors.warning;
      case ConfidenceLevel.medium:
        return AppColors.info;
      case ConfidenceLevel.high:
        return AppColors.success;
      case ConfidenceLevel.veryHigh:
        return AppColors.primary;
    }
  }

  String _getConfidenceLevelText(ConfidenceLevel level) {
    switch (level) {
      case ConfidenceLevel.veryLow:
        return 'Very Low';
      case ConfidenceLevel.low:
        return 'Low';
      case ConfidenceLevel.medium:
        return 'Medium';
      case ConfidenceLevel.high:
        return 'High';
      case ConfidenceLevel.veryHigh:
        return 'Very High';
    }
  }

  Color _getStatusColor(ReviewStatus status) {
    switch (status) {
      case ReviewStatus.pending:
        return AppColors.warning;
      case ReviewStatus.reviewed:
        return AppColors.success;
      case ReviewStatus.autoAccepted:
        return AppColors.primary;
      case ReviewStatus.autoRejected:
        return AppColors.error;
      case ReviewStatus.expired:
        return AppColors.textSecondary;
    }
  }

  String _getStatusText(ReviewStatus status) {
    switch (status) {
      case ReviewStatus.pending:
        return 'Pending';
      case ReviewStatus.reviewed:
        return 'Reviewed';
      case ReviewStatus.autoAccepted:
        return 'Auto Accepted';
      case ReviewStatus.autoRejected:
        return 'Auto Rejected';
      case ReviewStatus.expired:
        return 'Expired';
    }
  }
}
