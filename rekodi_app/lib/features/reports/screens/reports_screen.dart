import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/models/transaction.dart';
import '../../../core/models/account_type.dart';
import '../../../core/services/personal_transaction_service.dart';
import '../../../core/services/business_analytics_service.dart';
import '../../../core/services/account_service.dart';
import '../../../core/services/data_export_service.dart';
import '../widgets/date_range_selector.dart';
import '../widgets/spending_trends_chart.dart';
import '../widgets/category_breakdown_chart.dart';
import '../widgets/summary_cards.dart';
import '../widgets/business_summary_cards.dart';
import '../widgets/business_trends_chart.dart';
import '../widgets/business_insights_widget.dart';
import '../widgets/report_filter_modal.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final PersonalTransactionService _transactionService = Get.find();
  final AccountService _accountService = Get.find();
  final DataExportService _exportService = Get.find();

  BusinessAnalyticsService? _businessAnalyticsService;

  DateTimeRange _selectedDateRange = DateTimeRange(
    start: DateTime.now().subtract(const Duration(days: 30)),
    end: DateTime.now(),
  );

  TransactionType? _selectedType;
  TransactionCategory? _selectedCategory;
  bool _isLoading = false;

  // Sample data - would come from service in real app
  final List<FlSpot> _incomeData = [
    const FlSpot(1, 2000),
    const FlSpot(2, 2200),
    const FlSpot(3, 1800),
    const FlSpot(4, 2500),
    const FlSpot(5, 2300),
    const FlSpot(6, 2700),
    const FlSpot(7, 2400),
  ];

  final List<FlSpot> _expenseData = [
    const FlSpot(1, 1500),
    const FlSpot(2, 1800),
    const FlSpot(3, 1600),
    const FlSpot(4, 2000),
    const FlSpot(5, 1900),
    const FlSpot(6, 2100),
    const FlSpot(7, 1750),
  ];

  final List<PieChartSectionData> _categoryData = [
    PieChartSectionData(
      color: AppColors.primary,
      value: 35,
      title: 'Food',
      radius: 60,
      titleStyle: const TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
    ),
    PieChartSectionData(
      color: AppColors.secondary,
      value: 25,
      title: 'Transport',
      radius: 60,
      titleStyle: const TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
    ),
    PieChartSectionData(
      color: AppColors.accent,
      value: 20,
      title: 'Shopping',
      radius: 60,
      titleStyle: const TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
    ),
    PieChartSectionData(
      color: AppColors.success,
      value: 20,
      title: 'Others',
      radius: 60,
      titleStyle: const TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Initialize business analytics service if needed
    if (_accountService.currentAccountType == AccountType.business) {
      try {
        _businessAnalyticsService = Get.find<BusinessAnalyticsService>();
      } catch (e) {
        // Service not found, initialize it
        Get.put(BusinessAnalyticsService());
        _businessAnalyticsService = Get.find<BusinessAnalyticsService>();
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isBusinessAccount = _accountService.currentAccountType == AccountType.business;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          isBusinessAccount ? 'Business Reports' : 'Personal Reports',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          if (isBusinessAccount && _businessAnalyticsService != null)
            Obx(() => IconButton(
              onPressed: _businessAnalyticsService!.isAnalyzing
                  ? null
                  : () => _businessAnalyticsService!.refreshAnalytics(),
              icon: _businessAnalyticsService!.isAnalyzing
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.refresh_rounded),
              tooltip: 'Refresh Analytics',
            )),
          IconButton(
            onPressed: _showFilterModal,
            icon: const Icon(Icons.filter_list_rounded),
            tooltip: 'Filter Reports',
            style: IconButton.styleFrom(
              backgroundColor: AppColors.primary.withOpacity(0.1),
              foregroundColor: AppColors.primary,
            ),
          ),
          IconButton(
            onPressed: _exportReport,
            icon: const Icon(Icons.download_rounded),
            tooltip: 'Export Report',
            style: IconButton.styleFrom(
              backgroundColor: AppColors.secondary.withOpacity(0.1),
              foregroundColor: AppColors.secondary,
            ),
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Column(
        children: [
          // Date Range Selector
          DateRangeSelector(
            selectedRange: _selectedDateRange,
            onRangeChanged: (range) {
              setState(() {
                _selectedDateRange = range;
              });
            },
          ),
          
          // Tab Bar
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  colors: [AppColors.primary, AppColors.secondary],
                ),
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              indicatorPadding: const EdgeInsets.all(4),
              labelColor: Colors.white,
              unselectedLabelColor: Colors.grey[600],
              labelStyle: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
              unselectedLabelStyle: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
              tabs: const [
                Tab(
                  icon: Icon(Icons.trending_up_rounded, size: 20),
                  text: 'Trends',
                ),
                Tab(
                  icon: Icon(Icons.pie_chart_rounded, size: 20),
                  text: 'Categories',
                ),
                Tab(
                  icon: Icon(Icons.analytics_rounded, size: 20),
                  text: 'Summary',
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                isBusinessAccount ? _buildBusinessTrendsTab() : _buildTrendsTab(),
                isBusinessAccount ? _buildBusinessCategoriesTab() : _buildCategoriesTab(),
                isBusinessAccount ? _buildBusinessSummaryTab() : _buildSummaryTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrendsTab() {
    return AnimationLimiter(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 375),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(child: widget),
            ),
            children: [
              SpendingTrendsChart(
                incomeData: _incomeData,
                expenseData: _expenseData,
              ),
              const SizedBox(height: 24),
              _buildTrendInsights(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoriesTab() {
    return AnimationLimiter(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 375),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(child: widget),
            ),
            children: [
              CategoryBreakdownChart(data: _categoryData),
              const SizedBox(height: 24),
              _buildCategoryList(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryTab() {
    return AnimationLimiter(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 375),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(child: widget),
            ),
            children: [
              SummaryCards(dateRange: _selectedDateRange),
              const SizedBox(height: 24),
              _buildFinancialHealthScore(),
              const SizedBox(height: 24),
              _buildGoalsProgress(),
            ],
          ),
        ),
      ),
    );
  }

  // Business-specific tab methods
  Widget _buildBusinessTrendsTab() {
    if (_businessAnalyticsService == null) {
      return const Center(child: Text('Business analytics not available'));
    }

    return AnimationLimiter(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 375),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(child: widget),
            ),
            children: [
              BusinessTrendsChart(analyticsService: _businessAnalyticsService!),
              const SizedBox(height: 24),
              _buildBusinessTrendInsights(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBusinessCategoriesTab() {
    if (_businessAnalyticsService == null) {
      return const Center(child: Text('Business analytics not available'));
    }

    return AnimationLimiter(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 375),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(child: widget),
            ),
            children: [
              _buildBusinessCategoryBreakdown(),
              const SizedBox(height: 24),
              _buildTopProductsWidget(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBusinessSummaryTab() {
    if (_businessAnalyticsService == null) {
      return const Center(child: Text('Business analytics not available'));
    }

    return AnimationLimiter(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 375),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(child: widget),
            ),
            children: [
              BusinessSummaryCards(
                analyticsService: _businessAnalyticsService!,
                dateRange: _selectedDateRange,
              ),
              const SizedBox(height: 24),
              BusinessInsightsWidget(analyticsService: _businessAnalyticsService!),
              const SizedBox(height: 24),
              _buildBusinessPerformanceMetrics(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTrendInsights() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.lightbulb_rounded,
                  color: AppColors.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Insights',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInsightItem(
            'Your spending increased by 12% this month',
            Icons.trending_up_rounded,
            AppColors.warning,
          ),
          const SizedBox(height: 12),
          _buildInsightItem(
            'You saved \$450 more than last month',
            Icons.savings_rounded,
            AppColors.success,
          ),
          const SizedBox(height: 12),
          _buildInsightItem(
            'Food expenses are 15% above budget',
            Icons.restaurant_rounded,
            AppColors.error,
          ),
        ],
      ),
    );
  }

  Widget _buildInsightItem(String text, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryList() {
    final categories = [
      {'name': 'Food & Dining', 'amount': 850.0, 'percentage': 35.0, 'color': AppColors.primary},
      {'name': 'Transportation', 'amount': 600.0, 'percentage': 25.0, 'color': AppColors.secondary},
      {'name': 'Shopping', 'amount': 480.0, 'percentage': 20.0, 'color': AppColors.accent},
      {'name': 'Entertainment', 'amount': 240.0, 'percentage': 10.0, 'color': AppColors.success},
      {'name': 'Others', 'amount': 240.0, 'percentage': 10.0, 'color': AppColors.warning},
    ];

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.all(20),
            child: Text(
              'Category Breakdown',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          ...categories.map((category) => _buildCategoryItem(
            category['name'] as String,
            category['amount'] as double,
            category['percentage'] as double,
            category['color'] as Color,
          )).toList(),
        ],
      ),
    );
  }

  Widget _buildCategoryItem(String name, double amount, double percentage, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(6),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              name,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          Text(
            '\$${amount.toStringAsFixed(0)}',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '${percentage.toStringAsFixed(0)}%',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialHealthScore() {
    const score = 78;
    const maxScore = 100;
    
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.success, AppColors.success.withOpacity(0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.success.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            'Financial Health Score',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 20),
          Stack(
            alignment: Alignment.center,
            children: [
              SizedBox(
                width: 120,
                height: 120,
                child: CircularProgressIndicator(
                  value: score / maxScore,
                  strokeWidth: 8,
                  backgroundColor: Colors.white.withOpacity(0.3),
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              Column(
                children: [
                  Text(
                    '$score',
                    style: const TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  Text(
                    'Good',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Your financial health is good. Keep maintaining your spending habits!',
            style: TextStyle(
              fontSize: 14,
              color: Colors.white.withOpacity(0.9),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildGoalsProgress() {
    final goals = [
      {'name': 'Emergency Fund', 'current': 2500.0, 'target': 5000.0},
      {'name': 'Vacation Savings', 'current': 800.0, 'target': 2000.0},
      {'name': 'New Car Fund', 'current': 3200.0, 'target': 15000.0},
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Savings Goals',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          ...goals.map((goal) => _buildGoalItem(
            goal['name'] as String,
            goal['current'] as double,
            goal['target'] as double,
          )).toList(),
        ],
      ),
    );
  }

  Widget _buildGoalItem(String name, double current, double target) {
    final progress = (current / target).clamp(0.0, 1.0);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                name,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppColors.textPrimary,
                ),
              ),
              Text(
                '\$${current.toStringAsFixed(0)} / \$${target.toStringAsFixed(0)}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ClipRRect(
            borderRadius: BorderRadius.circular(6),
            child: LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.grey[200],
              valueColor: AlwaysStoppedAnimation<Color>(
                progress >= 1.0 ? AppColors.success : AppColors.primary,
              ),
              minHeight: 6,
            ),
          ),
        ],
      ),
    );
  }

  void _showDateRangePicker() async {
    final range = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: AppColors.textPrimary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (range != null) {
      setState(() {
        _selectedDateRange = range;
      });
    }
  }

  void _showFilterModal() {
    HapticFeedback.lightImpact();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ReportFilterModal(
        selectedType: _selectedType,
        selectedCategory: _selectedCategory,
        dateRange: _selectedDateRange,
        onFiltersChanged: (type, category, dateRange) {
          setState(() {
            _selectedType = type;
            _selectedCategory = category;
            _selectedDateRange = dateRange;
          });
        },
      ),
    );
  }

  void _exportReport() async {
    HapticFeedback.lightImpact();
    setState(() {
      _isLoading = true;
    });

    try {
      final transactions = await _transactionService.getTransactionsByDateRange(
        _selectedDateRange.start,
        _selectedDateRange.end,
      );

      final filteredTransactions = transactions.where((transaction) {
        if (_selectedType != null && transaction.type != _selectedType) {
          return false;
        }
        if (_selectedCategory != null && transaction.category != _selectedCategory) {
          return false;
        }
        return true;
      }).toList();

      await _exportService.exportTransactionsToCSV(
        startDate: _selectedDateRange.start,
        endDate: _selectedDateRange.end,
        category: _selectedCategory?.name,
        transactionType: _selectedType?.name,
      );

      Get.snackbar(
        'Success',
        'Report exported successfully',
        backgroundColor: AppColors.success,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to export report: ${e.toString()}',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Business-specific helper methods
  Widget _buildBusinessTrendInsights() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.trending_up_rounded,
                  color: AppColors.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Business Insights',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Obx(() {
            final summary = _businessAnalyticsService?.summary;
            if (summary == null) {
              return const Text('No data available');
            }

            return Column(
              children: [
                _buildInsightItem(
                  'Sales grew by ${summary.salesGrowth.toStringAsFixed(1)}% this month',
                  summary.salesGrowth > 0 ? Icons.trending_up_rounded : Icons.trending_down_rounded,
                  summary.salesGrowth > 0 ? AppColors.success : AppColors.warning,
                ),
                const SizedBox(height: 12),
                _buildInsightItem(
                  'Average profit margin is ${summary.profitMargin.toStringAsFixed(1)}%',
                  Icons.analytics_rounded,
                  summary.profitMargin > 20 ? AppColors.success : AppColors.warning,
                ),
                if (summary.topSellingProduct != null) ...[
                  const SizedBox(height: 12),
                  _buildInsightItem(
                    'Top seller: ${summary.topSellingProduct}',
                    Icons.star_rounded,
                    AppColors.primary,
                  ),
                ],
              ],
            );
          }),
        ],
      ),
    );
  }

  Widget _buildBusinessCategoryBreakdown() {
    return Container(
      height: 300,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Sales by Category',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: Obx(() {
              final categoryData = _businessAnalyticsService?.categoryBreakdown ?? [];

              if (categoryData.isEmpty) {
                return const Center(
                  child: Text(
                    'No sales data available',
                    style: TextStyle(color: AppColors.textSecondary),
                  ),
                );
              }

              return PieChart(
                PieChartData(
                  sections: categoryData,
                  centerSpaceRadius: 40,
                  sectionsSpace: 2,
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildTopProductsWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Top Products This Month',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'Feature coming soon...',
            style: TextStyle(color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildBusinessPerformanceMetrics() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Performance Metrics',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          Obx(() {
            final summary = _businessAnalyticsService?.summary;
            if (summary == null) {
              return const Text('No data available');
            }

            return Column(
              children: [
                _buildMetricRow('Average Order Value', '\$${summary.averageOrderValue.toStringAsFixed(2)}'),
                const SizedBox(height: 12),
                _buildMetricRow('Total Transactions', '${summary.transactionCount}'),
                const SizedBox(height: 12),
                _buildMetricRow('Net Revenue', '\$${summary.netRevenue.toStringAsFixed(2)}'),
              ],
            );
          }),
        ],
      ),
    );
  }

  Widget _buildMetricRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: AppColors.textSecondary,
            fontSize: 14,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            color: AppColors.textPrimary,
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}
