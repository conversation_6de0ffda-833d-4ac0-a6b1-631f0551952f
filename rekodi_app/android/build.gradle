allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")

    // Fix namespace issues for packages that don't specify namespace
    project.plugins.withId("com.android.library") {
        project.android {
            if (namespace == null) {
                if (project.name == 'sms_advanced') {
                    namespace 'com.phan_tech.sms_advanced'
                } else {
                    namespace project.group ?: "com.example.${project.name}"
                }
            }
        }
    }
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
