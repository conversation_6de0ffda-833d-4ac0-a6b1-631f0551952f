import 'dart:io';

/// Rekodi App - Appwrite Database Schema Documentation
///
/// This file contains the complete database schema for the Rekodi financial management app.
/// Since collections must be created through the Appwrite Console or Server SDK,
/// this serves as documentation and a reference for manual setup.
///
/// Instructions:
/// 1. Create a new database in Appwrite Console named "rekodi_main_db"
/// 2. Create each collection manually using the schemas below
/// 3. Set up the specified attributes and indexes for each collection
/// 4. Configure permissions as needed

void main() async {
  print('📋 Rekodi App - Appwrite Database Schema\n');
  print('⚠️  Collections must be created manually in Appwrite Console\n');

  _printDatabaseSchema();
  _printCollectionSchemas();
  _printPermissionsGuide();
  _printNextSteps();
}

void _printDatabaseSchema() {
  print('🗄️  DATABASE CONFIGURATION');
  print('=' * 50);
  print('Database ID: rekodi_main_db');
  print('Database Name: Rekodi Main Database');
  print('Project ID: 683c61460026c3e03330');
  print('');
}

void _printCollectionSchemas() {
  print('📊 COLLECTIONS SCHEMA');
  print('=' * 50);

  _printUserAccountsSchema();
  _printTransactionsSchema();
  _printSmsTransactionsSchema();
  _printSmsPatternsSchema();
  _printBudgetsSchema();
  _printRecurringTransactionsSchema();
  _printBusinessCollectionsSchema();
}

void _printUserAccountsSchema() {
  print('\n1️⃣  USER_ACCOUNTS Collection');
  print('-' * 30);
  print('Collection ID: user_accounts');
  print('Collection Name: User Accounts');
  print('');
  print('Attributes:');
  print('  • userId (String, 255, Required) - Unique user identifier');
  print('  • name (String, 255, Required) - User full name');
  print('  • email (String, 255, Required) - User email address');
  print('  • phone (String, 20, Optional) - User phone number');
  print('  • accountType (String, 50, Required) - personal/business');
  print('  • isActive (Boolean, Required) - Account status');
  print('  • isPremium (Boolean, Required) - Premium subscription status');
  print('  • profileImageUrl (String, 500, Optional) - Profile image URL');
  print('  • currency (String, 10, Optional) - Preferred currency code');
  print('  • timezone (String, 50, Optional) - User timezone');
  print('  • language (String, 10, Optional) - Preferred language');
  print('  • createdAt (DateTime, Required) - Account creation date');
  print('  • updatedAt (DateTime, Required) - Last update date');
  print('');
  print('Indexes:');
  print('  • userId_idx (userId)');
  print('  • email_idx (email)');
  print('  • isActive_idx (isActive)');
  print('  • accountType_idx (accountType)');
}

void _printTransactionsSchema() {
  print('\n2️⃣  TRANSACTIONS Collection');
  print('-' * 30);
  print('Collection ID: transactions');
  print('Collection Name: Personal Transactions');
  print('');
  print('Attributes:');
  print('  • accountId (String, 255, Required) - User account reference');
  print('  • title (String, 255, Required) - Transaction title');
  print('  • description (String, 1000, Optional) - Transaction description');
  print('  • amount (Float, Required) - Transaction amount');
  print('  • type (String, 50, Required) - income/expense');
  print('  • category (String, 100, Required) - Transaction category');
  print('  • subcategory (String, 100, Optional) - Transaction subcategory');
  print('  • date (DateTime, Required) - Transaction date');
  print('  • paymentMethod (String, 100, Optional) - Payment method used');
  print('  • location (String, 255, Optional) - Transaction location');
  print('  • tags (String, 1000, Optional) - Comma-separated tags');
  print('  • receiptImageUrl (String, 500, Optional) - Receipt image URL');
  print('  • isRecurring (Boolean, Required) - Is recurring transaction');
  print('  • recurringPattern (String, 100, Optional) - Recurrence pattern');
  print('  • nextRecurringDate (DateTime, Optional) - Next occurrence date');
  print('  • isSynced (Boolean, Required) - Sync status');
  print('  • confidence (Float, Optional) - AI confidence score');
  print('  • sourceType (String, 50, Optional) - manual/sms/receipt');
  print('  • createdAt (DateTime, Required) - Creation timestamp');
  print('  • updatedAt (DateTime, Required) - Last update timestamp');
  print('');
  print('Indexes:');
  print('  • accountId_idx (accountId)');
  print('  • type_idx (type)');
  print('  • category_idx (category)');
  print('  • date_idx (date)');
  print('  • paymentMethod_idx (paymentMethod)');
  print('  • isRecurring_idx (isRecurring)');
}

void _printSmsTransactionsSchema() {
  print('\n3️⃣  SMS_TRANSACTIONS Collection');
  print('-' * 30);
  print('Collection ID: sms_transactions');
  print('Collection Name: SMS Transaction Data');
  print('');
  print('Attributes:');
  print('  • accountId (String, 255, Required) - User account reference');
  print('  • rawMessage (String, 2000, Required) - Original SMS content');
  print('  • sender (String, 100, Required) - SMS sender identifier');
  print('  • merchantName (String, 255, Optional) - Extracted merchant name');
  print('  • amount (Float, Optional) - Extracted transaction amount');
  print('  • transactionType (String, 50, Optional) - Extracted type');
  print('  • category (String, 100, Optional) - Auto-assigned category');
  print('  • confidence (Float, Required) - AI parsing confidence (0-1)');
  print('  • isProcessed (Boolean, Required) - Processing status');
  print('  • isVerified (Boolean, Required) - User verification status');
  print('  • linkedTransactionId (String, 255, Optional) - Linked transaction');
  print('  • receivedAt (DateTime, Required) - SMS received timestamp');
  print('  • processedAt (DateTime, Optional) - Processing timestamp');
  print('  • errorMessage (String, 500, Optional) - Processing error details');
  print('  • metadata (String, 1000, Optional) - Additional parsed data (JSON)');
  print('');
  print('Indexes:');
  print('  • accountId_idx (accountId)');
  print('  • sender_idx (sender)');
  print('  • isProcessed_idx (isProcessed)');
  print('  • receivedAt_idx (receivedAt)');
  print('  • confidence_idx (confidence)');
}

void _printSmsPatternsSchema() {
  print('\n4️⃣  SMS_PATTERNS Collection');
  print('-' * 30);
  print('Collection ID: sms_patterns');
  print('Collection Name: SMS Parsing Patterns');
  print('');
  print('Attributes:');
  print('  • name (String, 255, Required) - Pattern name/description');
  print('  • sender (String, 100, Required) - SMS sender identifier');
  print('  • pattern (String, 1000, Required) - Regex pattern for matching');
  print('  • amountRegex (String, 500, Required) - Amount extraction regex');
  print('  • merchantRegex (String, 500, Optional) - Merchant extraction regex');
  print('  • typeRegex (String, 500, Optional) - Transaction type regex');
  print('  • categoryMapping (String, 500, Optional) - Category mapping rules');
  print('  • isActive (Boolean, Required) - Pattern active status');
  print('  • priority (Integer, Required) - Pattern priority (1-100)');
  print('  • successCount (Integer, Required) - Successful matches count');
  print('  • totalAttempts (Integer, Required) - Total attempts count');
  print('  • createdAt (DateTime, Required) - Pattern creation date');
  print('  • updatedAt (DateTime, Required) - Last update date');
  print('');
  print('Indexes:');
  print('  • sender_idx (sender)');
  print('  • isActive_idx (isActive)');
  print('  • priority_idx (priority)');
}

void _printBudgetsSchema() {
  print('\n5️⃣  BUDGETS Collection');
  print('-' * 30);
  print('Collection ID: budgets');
  print('Collection Name: Budget Management');
  print('');
  print('Attributes:');
  print('  • accountId (String, 255, Required) - User account reference');
  print('  • name (String, 255, Required) - Budget name');
  print('  • category (String, 100, Required) - Budget category');
  print('  • budgetAmount (Float, Required) - Budget limit amount');
  print('  • spentAmount (Float, Required) - Current spent amount');
  print('  • period (String, 50, Required) - monthly/weekly/yearly');
  print('  • startDate (DateTime, Required) - Budget period start');
  print('  • endDate (DateTime, Required) - Budget period end');
  print('  • isActive (Boolean, Required) - Budget active status');
  print('  • alertThreshold (Float, Optional) - Alert threshold (0-1)');
  print('  • description (String, 500, Optional) - Budget description');
  print('  • color (String, 10, Optional) - Budget color code');
  print('  • createdAt (DateTime, Required) - Creation timestamp');
  print('  • updatedAt (DateTime, Required) - Last update timestamp');
  print('');
  print('Indexes:');
  print('  • accountId_idx (accountId)');
  print('  • category_idx (category)');
  print('  • period_idx (period)');
  print('  • isActive_idx (isActive)');
}

void _printRecurringTransactionsSchema() {
  print('\n6️⃣  RECURRING_TRANSACTIONS Collection');
  print('-' * 30);
  print('Collection ID: recurring_transactions');
  print('Collection Name: Recurring Transactions');
  print('');
  print('Attributes:');
  print('  • accountId (String, 255, Required) - User account reference');
  print('  • title (String, 255, Required) - Transaction title');
  print('  • description (String, 1000, Optional) - Transaction description');
  print('  • amount (Float, Required) - Transaction amount');
  print('  • type (String, 50, Required) - income/expense');
  print('  • category (String, 100, Required) - Transaction category');
  print('  • frequency (String, 50, Required) - daily/weekly/monthly/yearly');
  print('  • nextDate (DateTime, Required) - Next occurrence date');
  print('  • endDate (DateTime, Optional) - Recurrence end date');
  print('  • isActive (Boolean, Required) - Recurrence active status');
  print('  • lastProcessed (DateTime, Optional) - Last processing date');
  print('  • paymentMethod (String, 100, Optional) - Default payment method');
  print('  • reminderDays (Integer, Optional) - Reminder days before');
  print('  • createdAt (DateTime, Required) - Creation timestamp');
  print('  • updatedAt (DateTime, Required) - Last update timestamp');
  print('');
  print('Indexes:');
  print('  • accountId_idx (accountId)');
  print('  • nextDate_idx (nextDate)');
  print('  • isActive_idx (isActive)');
  print('  • frequency_idx (frequency)');
}

void _printBusinessCollectionsSchema() {
  print('\n7️⃣  BUSINESS_PRODUCTS Collection');
  print('-' * 30);
  print('Collection ID: business_products');
  print('Collection Name: Business Products & Services');
  print('');
  print('Attributes:');
  print('  • accountId (String, 255, Required) - Business account reference');
  print('  • name (String, 255, Required) - Product/service name');
  print('  • description (String, 1000, Optional) - Product description');
  print('  • type (String, 50, Required) - product/service');
  print('  • category (String, 100, Required) - Product category');
  print('  • price (Float, Required) - Unit price');
  print('  • currency (String, 10, Required) - Price currency');
  print('  • quantity (Integer, Optional) - Stock quantity (products only)');
  print('  • unit (String, 50, Optional) - Unit of measurement');
  print('  • sku (String, 100, Optional) - Stock keeping unit');
  print('  • barcode (String, 100, Optional) - Product barcode');
  print('  • imageUrl (String, 500, Optional) - Product image URL');
  print('  • isActive (Boolean, Required) - Product active status');
  print('  • lowStockThreshold (Integer, Optional) - Low stock alert threshold');
  print('  • createdAt (DateTime, Required) - Creation timestamp');
  print('  • updatedAt (DateTime, Required) - Last update timestamp');
  print('');
  print('Indexes:');
  print('  • accountId_idx (accountId)');
  print('  • type_idx (type)');
  print('  • category_idx (category)');
  print('  • sku_idx (sku)');
  print('  • barcode_idx (barcode)');

  print('\n8️⃣  BUSINESS_TRANSACTIONS Collection');
  print('-' * 30);
  print('Collection ID: business_transactions');
  print('Collection Name: Business Sales & Purchases');
  print('');
  print('Attributes:');
  print('  • accountId (String, 255, Required) - Business account reference');
  print('  • type (String, 50, Required) - sale/purchase');
  print('  • invoiceNumber (String, 100, Optional) - Invoice/receipt number');
  print('  • customerName (String, 255, Optional) - Customer/supplier name');
  print('  • customerContact (String, 100, Optional) - Customer contact info');
  print('  • totalAmount (Float, Required) - Total transaction amount');
  print('  • taxAmount (Float, Optional) - Tax amount');
  print('  • discountAmount (Float, Optional) - Discount amount');
  print('  • paymentMethod (String, 100, Optional) - Payment method');
  print('  • paymentStatus (String, 50, Required) - paid/pending/overdue');
  print('  • dueDate (DateTime, Optional) - Payment due date');
  print('  • notes (String, 1000, Optional) - Transaction notes');
  print('  • items (String, 5000, Required) - Transaction items (JSON)');
  print('  • createdAt (DateTime, Required) - Creation timestamp');
  print('  • updatedAt (DateTime, Required) - Last update timestamp');
  print('');
  print('Indexes:');
  print('  • accountId_idx (accountId)');
  print('  • type_idx (type)');
  print('  • paymentStatus_idx (paymentStatus)');
  print('  • customerName_idx (customerName)');
  print('  • invoiceNumber_idx (invoiceNumber)');
}

void _printPermissionsGuide() {
  print('\n🔐 PERMISSIONS CONFIGURATION');
  print('=' * 50);
  print('For each collection, configure these permissions in Appwrite Console:');
  print('');
  print('📖 READ Permissions:');
  print('  • users - Allow authenticated users to read their own data');
  print('  • role:member - Allow app members to read data');
  print('');
  print('✏️  WRITE Permissions:');
  print('  • users - Allow authenticated users to create/update their own data');
  print('  • role:member - Allow app members to write data');
  print('');
  print('🗑️  DELETE Permissions:');
  print('  • users - Allow authenticated users to delete their own data');
  print('  • role:admin - Allow admins to delete any data');
  print('');
  print('💡 Security Rules:');
  print('  • Use document-level security with accountId filters');
  print('  • Implement rate limiting for API calls');
  print('  • Enable audit logs for sensitive operations');
  print('  • Set up backup and recovery procedures');
}

void _printNextSteps() {
  print('\n🚀 NEXT STEPS');
  print('=' * 50);
  print('1. 📊 Create Database:');
  print('   • Go to Appwrite Console > Databases');
  print('   • Create new database: "rekodi_main_db"');
  print('');
  print('2. 📋 Create Collections:');
  print('   • Create each collection listed above');
  print('   • Add all specified attributes with correct types');
  print('   • Create all specified indexes');
  print('');
  print('3. 🔐 Configure Permissions:');
  print('   • Set up read/write permissions as specified');
  print('   • Test permissions with sample data');
  print('');
  print('4. 🗂️  Create Storage Bucket:');
  print('   • Bucket ID: "rekodi_storage"');
  print('   • For receipts, profile images, and documents');
  print('   • Configure file size limits and allowed types');
  print('');
  print('5. 🔑 Update App Configuration:');
  print('   • Verify project ID: 683c61460026c3e03330');
  print('   • Update database ID in app constants');
  print('   • Test database connection from app');
  print('');
  print('6. 🧪 Test Setup:');
  print('   • Create test user account');
  print('   • Add sample transactions');
  print('   • Verify all CRUD operations work');
  print('');
  print('✅ Setup complete! Your Rekodi app database is ready.');
}
